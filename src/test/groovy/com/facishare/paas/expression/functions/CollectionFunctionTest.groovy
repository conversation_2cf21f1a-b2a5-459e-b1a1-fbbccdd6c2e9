package com.facishare.paas.expression.functions

import com.facishare.paas.expression.ExpressionEngine
import spock.lang.Specification

/**
 * CollectionFunction 单元测试
 * 测试 ARRAYCONTAINS、ARRAYCONTAINSALL、SIZE 三个方法的 compile 和 evaluate 场景
 */
class CollectionFunctionTest extends Specification {

    ExpressionEngine engine

    def setup() {
        engine = new ExpressionEngine()
    }

    def "test ARRAYCONTAINS compile"() {
        when:
        engine.compile(expression)
        then:
        noExceptionThrown()
        where:
        expression << [
                'ARRAYCONTAINS(["a", "b", "c"], "a")',
                'ARRAYCONTAINS(["apple", "banana"], "apple")',
                'ARRAYCONTAINS(null, "value")',
                'ARRAYCONTAINS([], "value")'
        ]
    }

    def "test ARRAYCONTAINSALL compile"() {
        when:
        engine.compile(expression)
        then:
        noExceptionThrown()
        where:
        expression << [
                'ARRAYCONTAINSALL(["a", "b", "c"], ["a", "b"])',
                'ARRAYCONTAINSALL(["x", "y", "z"], ["x"])',
                'ARRAYCONTAINSALL(null, ["a"])',
                'ARRAYCONTAINSALL(["a"], null)',
                'ARRAYCONTAINSALL([], [])'
        ]
    }

    def "test SIZE compile"() {
        when:
        engine.compile(expression)
        then:
        noExceptionThrown()
        where:
        expression << [
                'SIZE(["a", "b", "c"])',
                'SIZE([1, 2, 3, 4])',
                'SIZE(null)',
                'SIZE([])'
        ]
    }

    def "test ARRAYCONTAINS evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                              | bindings                                    || expectedResult
        'ARRAYCONTAINS(list, "apple")'          | [list: ["apple", "banana", "orange"]]      || true
        'ARRAYCONTAINS(list, "grape")'          | [list: ["apple", "banana", "orange"]]      || false
        'ARRAYCONTAINS(list, "apple")'          | [list: []]                                  || false
        'ARRAYCONTAINS(list, "apple")'          | [list: null]                                || false
        'ARRAYCONTAINS(["a", "b", "c"], "b")'   | [:]                                         || true
        'ARRAYCONTAINS(["a", "b", "c"], "d")'   | [:]                                         || false
        'ARRAYCONTAINS([], "any")'              | [:]                                         || false
        'ARRAYCONTAINS(null, "any")'            | [:]                                         || false
    }

    def "test ARRAYCONTAINSALL evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                      | bindings                                                        || expectedResult
        'ARRAYCONTAINSALL(list1, list2)'                | [list1: ["a", "b", "c", "d"], list2: ["a", "b"]]               || true
        'ARRAYCONTAINSALL(list1, list2)'                | [list1: ["a", "b", "c"], list2: ["a", "d"]]                    || false
        'ARRAYCONTAINSALL(list1, list2)'                | [list1: ["a", "b", "c"], list2: []]                            || false
        'ARRAYCONTAINSALL(list1, list2)'                | [list1: [], list2: ["a"]]                                      || false
        'ARRAYCONTAINSALL(list1, list2)'                | [list1: null, list2: ["a"]]                                    || false
        'ARRAYCONTAINSALL(list1, list2)'                | [list1: ["a"], list2: null]                                    || false
        'ARRAYCONTAINSALL(["x", "y", "z"], ["x", "y"])' | [:]                                                             || true
        'ARRAYCONTAINSALL(["x", "y"], ["x", "y", "z"])' | [:]                                                             || false
        'ARRAYCONTAINSALL([], [])'                      | [:]                                                             || false
    }

    def "test SIZE evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                  | bindings                                    || expectedResult
        'SIZE(list)'                | [list: ["apple", "banana", "orange"]]      || 3
        'SIZE(list)'                | [list: []]                                  || 0
        'SIZE(list)'                | [list: null]                                || 0
        'SIZE(["a", "b", "c", "d"])' | [:]                                         || 4
        'SIZE([])'                  | [:]                                         || 0
        'SIZE(null)'                | [:]                                         || 0
    }

    def "test complex expressions with CollectionFunction"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                                          | bindings                                                    || expectedResult
        'SIZE(list) > 0 && ARRAYCONTAINS(list, "apple")'                   | [list: ["apple", "banana"]]                                || true
        'SIZE(list) > 0 && ARRAYCONTAINS(list, "grape")'                   | [list: ["apple", "banana"]]                                || false
        'ARRAYCONTAINSALL(list1, list2) && SIZE(list1) >= SIZE(list2)'     | [list1: ["a", "b", "c"], list2: ["a", "b"]]                || true
        'ARRAYCONTAINSALL(list1, list2) && SIZE(list1) >= SIZE(list2)'     | [list1: ["a", "b"], list2: ["a", "b", "c"]]                || false
        'IF(SIZE(list) > 0, ARRAYCONTAINS(list, target), false)'           | [list: ["x", "y", "z"], target: "y"]                       || true
        'IF(SIZE(list) > 0, ARRAYCONTAINS(list, target), false)'           | [list: [], target: "y"]                                    || false
    }

    def "test edge cases and error handling"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                  | bindings                                        || expectedResult
        'ARRAYCONTAINS(list, null)'                 | [list: ["a", "b", null]]                       || true
        'ARRAYCONTAINS(list, null)'                 | [list: ["a", "b"]]                             || false
        'ARRAYCONTAINSALL(list1, list2)'            | [list1: [1, 2, 3], list2: [1, 2]]              || true
        'ARRAYCONTAINSALL(list1, list2)'            | [list1: ["1", "2", "3"], list2: [1, 2]]        || false
        'SIZE(list)'                                | [list: [1, 2, 3, 4, 5]]                        || 5
        'SIZE(list)'                                | [list: ["", null, 0, false]]                   || 4
    }
}
