package com.facishare.paas.expression.functions

import com.facishare.paas.expression.ExpressionEngine
import spock.lang.Specification

/**
 * LogicCaseFunction 单元测试
 * 测试 CASE 函数的各种重载版本的 compile 和 evaluate 场景
 */
class LogicCaseFunctionTest extends Specification {

    ExpressionEngine engine

    def setup() {
        engine = new ExpressionEngine()
    }

    def "test CASE functions compile"() {
        when:
        engine.compile(expression)
        then:
        noExceptionThrown()
        where:
        expression << [
                'CASE(1, 1, "one", "default")',
                'CASE(2, 1, "one", 2, "two", "default")',
                'CASE(3, 1, "one", 2, "two", 3, "three", "default")',
                'CASE("A", "A", "Apple", "B", "Banana", "C", "Cherry", "Unknown")',
                'CASE(status, "ACTIVE", "活跃", "INACTIVE", "非活跃", "未知")',
                'CASE(grade, 90, "A", 80, "B", 70, "C", 60, "D", "F")'
        ]
    }

    def "test CASE with 1 condition evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                              | bindings                          || expectedResult
        'CASE(1, 1, "one", "default")'          | [:]                               || "one"
        'CASE(2, 1, "one", "default")'          | [:]                               || "default"
        'CASE("A", "A", "Apple", "Unknown")'    | [:]                               || "Apple"
        'CASE("B", "A", "Apple", "Unknown")'    | [:]                               || "Unknown"
        'CASE(value, 1, "first", "other")'      | [value: 1]                        || "first"
        'CASE(value, 1, "first", "other")'      | [value: 2]                        || "other"
        'CASE(null, 1, "first", "other")'       | [:]                               || "other"
    }

    def "test CASE with 2 conditions evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                      | bindings                          || expectedResult
        'CASE(1, 1, "one", 2, "two", "default")'        | [:]                               || "one"
        'CASE(2, 1, "one", 2, "two", "default")'        | [:]                               || "two"
        'CASE(3, 1, "one", 2, "two", "default")'        | [:]                               || "default"
        'CASE("A", "A", "Apple", "B", "Banana", "Unknown")' | [:]                           || "Apple"
        'CASE("B", "A", "Apple", "B", "Banana", "Unknown")' | [:]                           || "Banana"
        'CASE("C", "A", "Apple", "B", "Banana", "Unknown")' | [:]                           || "Unknown"
        'CASE(status, "ACTIVE", 1, "INACTIVE", 0, -1)'  | [status: "ACTIVE"]                || 1
        'CASE(status, "ACTIVE", 1, "INACTIVE", 0, -1)'  | [status: "INACTIVE"]              || 0
        'CASE(status, "ACTIVE", 1, "INACTIVE", 0, -1)'  | [status: "PENDING"]               || -1
    }

    def "test CASE with 3 conditions evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                                  | bindings                          || expectedResult
        'CASE(1, 1, "one", 2, "two", 3, "three", "default")'       | [:]                               || "one"
        'CASE(2, 1, "one", 2, "two", 3, "three", "default")'       | [:]                               || "two"
        'CASE(3, 1, "one", 2, "two", 3, "three", "default")'       | [:]                               || "three"
        'CASE(4, 1, "one", 2, "two", 3, "three", "default")'       | [:]                               || "default"
        'CASE(grade, "A", 90, "B", 80, "C", 70, 0)'                | [grade: "A"]                      || 90
        'CASE(grade, "A", 90, "B", 80, "C", 70, 0)'                | [grade: "B"]                      || 80
        'CASE(grade, "A", 90, "B", 80, "C", 70, 0)'                | [grade: "C"]                      || 70
        'CASE(grade, "A", 90, "B", 80, "C", 70, 0)'                | [grade: "F"]                      || 0
    }

    def "test CASE with 4 conditions evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                                          | bindings                          || expectedResult
        'CASE(1, 1, "one", 2, "two", 3, "three", 4, "four", "default")'    | [:]                               || "one"
        'CASE(2, 1, "one", 2, "two", 3, "three", 4, "four", "default")'    | [:]                               || "two"
        'CASE(3, 1, "one", 2, "two", 3, "three", 4, "four", "default")'    | [:]                               || "three"
        'CASE(4, 1, "one", 2, "two", 3, "three", 4, "four", "default")'    | [:]                               || "four"
        'CASE(5, 1, "one", 2, "two", 3, "three", 4, "four", "default")'    | [:]                               || "default"
        'CASE(day, 1, "Mon", 2, "Tue", 3, "Wed", 4, "Thu", "Other")'       | [day: 1]                          || "Mon"
        'CASE(day, 1, "Mon", 2, "Tue", 3, "Wed", 4, "Thu", "Other")'       | [day: 2]                          || "Tue"
        'CASE(day, 1, "Mon", 2, "Tue", 3, "Wed", 4, "Thu", "Other")'       | [day: 3]                          || "Wed"
        'CASE(day, 1, "Mon", 2, "Tue", 3, "Wed", 4, "Thu", "Other")'       | [day: 4]                          || "Thu"
        'CASE(day, 1, "Mon", 2, "Tue", 3, "Wed", 4, "Thu", "Other")'       | [day: 5]                          || "Other"
    }

    def "test CASE with 5 conditions evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                                                      | bindings                          || expectedResult
        'CASE(1, 1, "one", 2, "two", 3, "three", 4, "four", 5, "five", "default")'     | [:]                               || "one"
        'CASE(5, 1, "one", 2, "two", 3, "three", 4, "four", 5, "five", "default")'     | [:]                               || "five"
        'CASE(6, 1, "one", 2, "two", 3, "three", 4, "four", 5, "five", "default")'     | [:]                               || "default"
        'CASE(score, 90, "A", 80, "B", 70, "C", 60, "D", 50, "E", "F")'                | [score: 90]                       || "A"
        'CASE(score, 90, "A", 80, "B", 70, "C", 60, "D", 50, "E", "F")'                | [score: 80]                       || "B"
        'CASE(score, 90, "A", 80, "B", 70, "C", 60, "D", 50, "E", "F")'                | [score: 70]                       || "C"
        'CASE(score, 90, "A", 80, "B", 70, "C", 60, "D", 50, "E", "F")'                | [score: 60]                       || "D"
        'CASE(score, 90, "A", 80, "B", 70, "C", 60, "D", 50, "E", "F")'                | [score: 50]                       || "E"
        'CASE(score, 90, "A", 80, "B", 70, "C", 60, "D", 50, "E", "F")'                | [score: 40]                       || "F"
    }

    def "test CASE with different data types"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                                  | bindings                              || expectedResult
        'CASE(type, "STRING", 1, "NUMBER", 2, "BOOLEAN", 3, 0)'    | [type: "STRING"]                      || 1
        'CASE(type, "STRING", 1, "NUMBER", 2, "BOOLEAN", 3, 0)'    | [type: "NUMBER"]                      || 2
        'CASE(type, "STRING", 1, "NUMBER", 2, "BOOLEAN", 3, 0)'    | [type: "BOOLEAN"]                     || 3
        'CASE(type, "STRING", 1, "NUMBER", 2, "BOOLEAN", 3, 0)'    | [type: "OTHER"]                       || 0
        'CASE(flag, true, "YES", false, "NO", "UNKNOWN")'          | [flag: true]                          || "YES"
        'CASE(flag, true, "YES", false, "NO", "UNKNOWN")'          | [flag: false]                         || "NO"
        'CASE(flag, true, "YES", false, "NO", "UNKNOWN")'          | [flag: null]                          || "UNKNOWN"
        'CASE(num, 1.0, "one", 2.0, "two", 3.0, "three", "other")' | [num: 1.0]                           || "one"
        'CASE(num, 1.0, "one", 2.0, "two", 3.0, "three", "other")' | [num: 2.0]                           || "two"
        'CASE(num, 1.0, "one", 2.0, "two", 3.0, "three", "other")' | [num: 4.0]                           || "other"
    }

    def "test CASE with null values"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                          | bindings                          || expectedResult
        'CASE(null, null, "null_value", "not_null")'       | [:]                               || "null_value"
        'CASE(null, "test", "test_value", "default")'      | [:]                               || "default"
        'CASE("test", null, "null_value", "default")'      | [:]                               || "default"
        'CASE(value, null, "is_null", "not_null")'         | [value: null]                     || "is_null"
        'CASE(value, null, "is_null", "not_null")'         | [value: "test"]                   || "not_null"
    }

    def "test complex CASE expressions"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                                                      | bindings                                      || expectedResult
        'CASE(status, "ACTIVE", CASE(type, "VIP", "VIP_ACTIVE", "NORMAL_ACTIVE"), "INACTIVE")' | [status: "ACTIVE", type: "VIP"]       || "VIP_ACTIVE"
        'CASE(status, "ACTIVE", CASE(type, "VIP", "VIP_ACTIVE", "NORMAL_ACTIVE"), "INACTIVE")' | [status: "ACTIVE", type: "NORMAL"]    || "NORMAL_ACTIVE"
        'CASE(status, "ACTIVE", CASE(type, "VIP", "VIP_ACTIVE", "NORMAL_ACTIVE"), "INACTIVE")' | [status: "INACTIVE", type: "VIP"]     || "INACTIVE"
        'CASE(level, 1, "Level " + "1", 2, "Level " + "2", "Unknown")'                         | [level: 1]                            || "Level 1"
        'CASE(level, 1, "Level " + "1", 2, "Level " + "2", "Unknown")'                         | [level: 2]                            || "Level 2"
        'CASE(level, 1, "Level " + "1", 2, "Level " + "2", "Unknown")'                         | [level: 3]                            || "Unknown"
    }

    def "test CASE error handling"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        thrown(expectedException)
        where:
        expression                              | bindings      || expectedException
        'CASE(null, 1, "one", "default")'       | [:]           || NullPointerException
    }
}
