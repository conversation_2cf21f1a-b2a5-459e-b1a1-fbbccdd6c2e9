package com.facishare.paas.expression.functions

import com.facishare.paas.expression.ExpressionEngine
import spock.lang.Specification

/**
 * MathFunction 单元测试
 * 测试 ABS, MIN, MAX, MULTIPLE, MOD, ADDS, SUBTRACTS, ROUNDUP, ROUNDDOWN, ROUND<PERSON><PERSON>UP, ROUND<PERSON><PERSON>DOWN 等方法的 compile 和 evaluate 场景
 */
class MathFunctionTest extends Specification {

    ExpressionEngine engine

    def setup() {
        engine = new ExpressionEngine()
    }

    def "test math functions compile"() {
        when:
        engine.compile(expression)
        then:
        noExceptionThrown()
        where:
        expression << [
                'ABS(-5)',
                'ABS(5)',
                'MIN(10, 20)',
                'MIN(new BigDecimal("10.5"), new BigDecimal("20.3"))',
                'MAX(10, 20)',
                'MAX(new BigDecimal("10.5"), new BigDecimal("20.3"))',
                'MULTIPLE(5, 3)',
                'MULTIPLE(new BigDecimal("5.5"), new BigDecimal("3.2"))',
                'MOD(10, 3)',
                'MOD(new BigDecimal("10"), new BigDecimal("3"))',
                'ADDS(5, 3)',
                'ADDS(new BigDecimal("5.5"), new BigDecimal("3.2"))',
                'SUBTRACTS(10, 3)',
                'SUBTRACTS(new BigDecimal("10.5"), new BigDecimal("3.2"))',
                'ROUNDUP(3.14159, 2)',
                'ROUNDUP(new BigDecimal("3.14159"), new BigDecimal("2"))',
                'ROUNDDOWN(3.14159, 2)',
                'ROUNDDOWN(new BigDecimal("3.14159"), new BigDecimal("2"))',
                'ROUNDHALFUP(3.145, 2)',
                'ROUNDHALFUP(new BigDecimal("3.145"), new BigDecimal("2"))',
                'ROUNDHALFDOWN(3.145, 2)',
                'ROUNDHALFDOWN(new BigDecimal("3.145"), new BigDecimal("2"))'
        ]
    }

    def "test ABS evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression          | bindings                  || expectedResult
        'ABS(-5)'           | [:]                       || 5.0
        'ABS(5)'            | [:]                       || 5.0
        'ABS(-3.14)'        | [:]                       || 3.14
        'ABS(0)'            | [:]                       || 0.0
        'ABS(value)'        | [value: -10]              || 10.0
        'ABS(value)'        | [value: 10]               || 10.0
    }

    def "test MIN evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                              | bindings                                          || expectedResult
        'MIN(10, 20)'                                           | [:]                                               || 10.0
        'MIN(20, 10)'                                           | [:]                                               || 10.0
        'MIN(-5, 3)'                                            | [:]                                               || -5.0
        'MIN(new BigDecimal("10.5"), new BigDecimal("20.3"))'  | [:]                                               || new BigDecimal("10.5")
        'MIN(new BigDecimal("20.3"), new BigDecimal("10.5"))'  | [:]                                               || new BigDecimal("10.5")
        'MIN(null, new BigDecimal("10"))'                      | [:]                                               || null
        'MIN(new BigDecimal("10"), null)'                      | [:]                                               || null
        'MIN(a, b)'                                             | [a: 15, b: 25]                                    || 15.0
        'MIN(a, b)'                                             | [a: new BigDecimal("15.5"), b: new BigDecimal("25.3")] || new BigDecimal("15.5")
    }

    def "test MAX evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                              | bindings                                          || expectedResult
        'MAX(10, 20)'                                           | [:]                                               || 20.0
        'MAX(20, 10)'                                           | [:]                                               || 20.0
        'MAX(-5, 3)'                                            | [:]                                               || 3.0
        'MAX(new BigDecimal("10.5"), new BigDecimal("20.3"))'  | [:]                                               || new BigDecimal("20.3")
        'MAX(new BigDecimal("20.3"), new BigDecimal("10.5"))'  | [:]                                               || new BigDecimal("20.3")
        'MAX(null, new BigDecimal("10"))'                      | [:]                                               || null
        'MAX(new BigDecimal("10"), null)'                      | [:]                                               || null
        'MAX(a, b)'                                             | [a: 15, b: 25]                                    || 25.0
        'MAX(a, b)'                                             | [a: new BigDecimal("15.5"), b: new BigDecimal("25.3")] || new BigDecimal("25.3")
    }

    def "test MULTIPLE evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                                  | bindings                                          || expectedResult
        'MULTIPLE(5, 3)'                                            | [:]                                               || 15.0
        'MULTIPLE(-5, 3)'                                           | [:]                                               || -15.0
        'MULTIPLE(2.5, 4)'                                          | [:]                                               || 10.0
        'MULTIPLE(new BigDecimal("5.5"), new BigDecimal("3.2"))'   | [:]                                               || new BigDecimal("17.60")
        'MULTIPLE(new BigDecimal("0"), new BigDecimal("10"))'      | [:]                                               || new BigDecimal("0")
        'MULTIPLE(a, b)'                                            | [a: 6, b: 7]                                     || 42.0
        'MULTIPLE(a, b)'                                            | [a: new BigDecimal("2.5"), b: new BigDecimal("4")] || new BigDecimal("10.0")
    }

    def "test MOD evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                              | bindings                                          || expectedResult
        'MOD(10, 3)'                                            | [:]                                               || 3.0
        'MOD(15, 4)'                                            | [:]                                               || 3.0
        'MOD(new BigDecimal("10"), new BigDecimal("3"))'       | [:]                                               || new BigDecimal("3")
        'MOD(new BigDecimal("17"), new BigDecimal("5"))'       | [:]                                               || new BigDecimal("3")
        'MOD(a, b)'                                             | [a: 13, b: 5]                                    || 2.0
        'MOD(a, b)'                                             | [a: new BigDecimal("13"), b: new BigDecimal("5")] || new BigDecimal("2")
    }

    def "test ADDS evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                              | bindings                                          || expectedResult
        'ADDS(5, 3)'                                            | [:]                                               || 8.0
        'ADDS(-5, 3)'                                           | [:]                                               || -2.0
        'ADDS(2.5, 3.7)'                                        | [:]                                               || 6.2
        'ADDS(new BigDecimal("5.5"), new BigDecimal("3.2"))'   | [:]                                               || new BigDecimal("8.7")
        'ADDS(new BigDecimal("0"), new BigDecimal("10"))'      | [:]                                               || new BigDecimal("10")
        'ADDS(a, b)'                                            | [a: 15, b: 25]                                   || 40.0
        'ADDS(a, b)'                                            | [a: new BigDecimal("15.5"), b: new BigDecimal("25.3")] || new BigDecimal("40.8")
    }

    def "test SUBTRACTS evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                                  | bindings                                          || expectedResult
        'SUBTRACTS(10, 3)'                                          | [:]                                               || 7.0
        'SUBTRACTS(3, 10)'                                          | [:]                                               || -7.0
        'SUBTRACTS(5.7, 2.3)'                                       | [:]                                               || 3.4
        'SUBTRACTS(new BigDecimal("10.5"), new BigDecimal("3.2"))'  | [:]                                               || new BigDecimal("7.3")
        'SUBTRACTS(new BigDecimal("0"), new BigDecimal("10"))'      | [:]                                               || new BigDecimal("-10")
        'SUBTRACTS(a, b)'                                           | [a: 25, b: 15]                                   || 10.0
        'SUBTRACTS(a, b)'                                           | [a: new BigDecimal("25.5"), b: new BigDecimal("15.3")] || new BigDecimal("10.2")
    }

    def "test ROUNDUP evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                                  | bindings                                          || expectedResult
        'ROUNDUP(3.14159, 2)'                                      | [:]                                               || 3.15
        'ROUNDUP(3.14159, 0)'                                      | [:]                                               || 4.0
        'ROUNDUP(-3.14159, 2)'                                     | [:]                                               || -3.15
        'ROUNDUP(new BigDecimal("3.14159"), new BigDecimal("2"))'  | [:]                                               || new BigDecimal("3.15")
        'ROUNDUP(new BigDecimal("3.14159"), new BigDecimal("4"))'  | [:]                                               || new BigDecimal("3.1416")
        'ROUNDUP(value, places)'                                   | [value: 3.14159, places: 3]                      || 3.142
        'ROUNDUP(value, places)'                                   | [value: new BigDecimal("3.14159"), places: new BigDecimal("3")] || new BigDecimal("3.142")
    }

    def "test ROUNDDOWN evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                                      | bindings                                          || expectedResult
        'ROUNDDOWN(3.14159, 2)'                                        | [:]                                               || 3.14
        'ROUNDDOWN(3.14159, 0)'                                        | [:]                                               || 3.0
        'ROUNDDOWN(-3.14159, 2)'                                       | [:]                                               || -3.14
        'ROUNDDOWN(new BigDecimal("3.14159"), new BigDecimal("2"))'    | [:]                                               || new BigDecimal("3.14")
        'ROUNDDOWN(new BigDecimal("3.14159"), new BigDecimal("4"))'    | [:]                                               || new BigDecimal("3.1415")
        'ROUNDDOWN(value, places)'                                     | [value: 3.14159, places: 3]                      || 3.141
        'ROUNDDOWN(value, places)'                                     | [value: new BigDecimal("3.14159"), places: new BigDecimal("3")] || new BigDecimal("3.141")
    }

    def "test ROUNDHALFUP evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                                      | bindings                                          || expectedResult
        'ROUNDHALFUP(3.145, 2)'                                        | [:]                                               || 3.15
        'ROUNDHALFUP(3.144, 2)'                                        | [:]                                               || 3.14
        'ROUNDHALFUP(3.5, 0)'                                          | [:]                                               || 4.0
        'ROUNDHALFUP(new BigDecimal("3.145"), new BigDecimal("2"))'    | [:]                                               || new BigDecimal("3.15")
        'ROUNDHALFUP(new BigDecimal("3.144"), new BigDecimal("2"))'    | [:]                                               || new BigDecimal("3.14")
        'ROUNDHALFUP(value, places)'                                   | [value: 3.145, places: 2]                        || 3.15
        'ROUNDHALFUP(value, places)'                                   | [value: new BigDecimal("3.145"), places: new BigDecimal("2")] || new BigDecimal("3.15")
    }

    def "test ROUNDHALFDOWN evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                                          | bindings                                          || expectedResult
        'ROUNDHALFDOWN(3.145, 2)'                                          | [:]                                               || 3.14
        'ROUNDHALFDOWN(3.146, 2)'                                          | [:]                                               || 3.15
        'ROUNDHALFDOWN(3.5, 0)'                                            | [:]                                               || 3.0
        'ROUNDHALFDOWN(new BigDecimal("3.145"), new BigDecimal("2"))'      | [:]                                               || new BigDecimal("3.14")
        'ROUNDHALFDOWN(new BigDecimal("3.146"), new BigDecimal("2"))'      | [:]                                               || new BigDecimal("3.15")
        'ROUNDHALFDOWN(value, places)'                                     | [value: 3.145, places: 2]                        || 3.14
        'ROUNDHALFDOWN(value, places)'                                     | [value: new BigDecimal("3.145"), places: new BigDecimal("2")] || new BigDecimal("3.14")
    }

    def "test complex math expressions"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                              | bindings                                      || expectedResult
        'ROUNDUP(ADDS(a, b), 2)'                               | [a: 3.141, b: 2.718]                         || 5.86
        'MAX(ABS(a), ABS(b))'                                  | [a: -10, b: 8]                               || 10.0
        'MIN(MULTIPLE(a, 2), MULTIPLE(b, 3))'                  | [a: 5, b: 4]                                 || 10.0
        'ROUNDHALFUP(SUBTRACTS(MAX(a, b), MIN(a, b)), 1)'      | [a: 10.25, b: 8.75]                          || 1.5
        'MOD(ADDS(a, b), 10)'                                  | [a: 23, b: 17]                               || 0.0
    }
}
