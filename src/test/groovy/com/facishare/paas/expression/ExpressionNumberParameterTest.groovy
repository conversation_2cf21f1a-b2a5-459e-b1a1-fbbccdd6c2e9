package com.facishare.paas.expression

import com.facishare.paas.expression.type.PDate
import spock.lang.Specification
import spock.lang.Unroll

import java.time.LocalDate

/**
 * 将计算函数中的int、double类型参数统一使用Number类型替换；测试编译校验、计算
 */
class ExpressionNumberParameterTest extends Specification {
    def "test DateTimeFunction evaluate"() {
        given:
        ExpressionEngine engine = new ExpressionEngine()
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result.value == expectedResult
        where:
        expression             | bindings || expectedResult
        "YEARS(2019)"          | ["": ""] || 2019
        "YEARS(2019.1)"        | ["": ""] || 2019
        "MONTHS(12)"           | ["": ""] || 12
        "MONTHS(12.1)"         | ["": ""] || 12
        "DATE(2019,10,29)"     | ["": ""] || LocalDate.of(2019, 10, 29)
        "DATE(2019.2,10.1,29)" | ["": ""] || LocalDate.of(2019, 10, 29)
    }

    def "test DateTimeFunction null evaluate"() {
        given:
        ExpressionEngine engine = new ExpressionEngine()
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression       | bindings     || expectedResult
//        "YEARS(f1)"      | ["f1": null] || 2019
//        "MONTHS(f1)"     | ["f1": null] || 12
        "DATE(f1,10,29)" | ["f1": 2019] || PDate.of(2019, 10, 29)
    }

    def "test DateTimeFunction compile"() {
        given:
        ExpressionEngine engine = new ExpressionEngine()
        when:
        engine.compile(expression)
        then:
        noExceptionThrown()
        where:
        expression                                                                                              | bindings
        expressionInfo("YEARS(f1)", ["f1": "int"], "Years")                                                     | bindings()
        expressionInfo("YEARS(f1)", ["f1": "double"], "Years")                                                  | bindings()
        expressionInfo("YEARS(f1)", ["f1": "BigDecimal"], "Years")                                              | bindings()
        expressionInfo("MONTHS(f1)", ["f1": "int"], "Months")                                                   | bindings()
        expressionInfo("MONTHS(f1)", ["f1": "double"], "Months")                                                | bindings()
        expressionInfo("MONTHS(f1)", ["f1": "BigDecimal"], "Months")                                            | bindings()
        expressionInfo("DATE(f1,f2,f3)", ["f1": "int", "f2": "int", "f3": "int"], "PDate")                      | bindings()
        expressionInfo("DATE(f1,f2,f3)", ["f1": "BigDecimal", "f2": "BigDecimal", "f3": "BigDecimal"], "PDate") | bindings()
        expressionInfo("DATE(f1,f2,f3)", ["f1": "int", "f2": "double", "f3": "BigDecimal"], "PDate")            | bindings()
    }

    def "test LogicFunction evaluate"() {
        given:
        ExpressionEngine engine = new ExpressionEngine()
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression      | bindings                 || expectedResult
        "IF(1>2,f1,f2)" | ["f1": 20.1, "f2": 30.2] || 30.2
        "IF(1>2,f1,f2)" | ["f1": 20, "f2": 30]     || 30
        "IF(1>2,f1,f2)" | ["f1": 20, "f2": null]   || null

    }

    def "test LogicFunction compile"() {
        given:
        ExpressionEngine engine = new ExpressionEngine()
        when:
        engine.compile(expression)
        then:
        noExceptionThrown()
        where:
        expression                                                    | bindings
        expressionInfo("IF(f1,2,3)", ["f1": "boolean"], "BigDecimal") | bindings()
        //expressionInfo("IF(f1,f2,f3)", ["f1": "boolean", "f2": "int", "f3": "double"], "BigDecimal") | bindings()
    }

    @Unroll
    def "test MathFunction evaluate #expression"() {
        given:
        ExpressionEngine engine = new ExpressionEngine()
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression             | bindings                     || expectedResult
        "MIN(f1,f2)"           | ["f1": 20.1, "f2": 30.2]     || 20.1
        "MIN(f1,f2)"           | ["f1": 20.1, "f2": 13]       || 13
        "MIN(f1,f2)"           | ["f1": 20, "f2": 30]         || 20
        "MAX(f1,f2)"           | ["f1": 20.1, "f2": 30]       || 30
        "MAX(f1,f2)"           | ["f1": 20, "f2": 30]         || 30
        "MAX(f1,f2)"           | ["f1": 20.5, "f2": 30.6]     || 30.6
        "MULTIPLE(f1,f2)"      | ["f1": 20, "f2": 3]          || 60
        "MULTIPLE(f1,f2)"      | ["f1": 1.2, "f2": 3]         || 3.6
        "MULTIPLE(f1,f2)"      | ["f1": 1.2, "f2": 1.2]       || 1.44
        "MOD(f1,f2)"           | ["f1": 5, "f2": 2]           || 2
        "MOD(f1,f2)"           | ["f1": 5.7, "f2": 2.1]       || 2
        "MOD(f1,f2)"           | ["f1": 5.7, "f2": 2]         || 2
        "ADDS(f1,f2)"          | ["f1": 5.7, "f2": 2]         || 7.7
        "ADDS(f1,f2)"          | ["f1": 5, "f2": 2]           || 7
        "ADDS(f1,f2)"          | ["f1": 5.7, "f2": 2.2]       || 7.9
        "SUBTRACTS(f1,f2)"     | ["f1": 5.7, "f2": 2.2]       || 3.5
        "SUBTRACTS(f1,f2)"     | ["f1": 5, "f2": 2.2]         || 2.8
        "SUBTRACTS(f1,f2)"     | ["f1": 5, "f2": 2]           || 3
        "ROUNDUP(f1,f2)"       | ["f1": 1.255, "f2": 2]       || 1.26
        "ROUNDUP(f1,f2)"       | ["f1": 1.254, "f2": 2]       || 1.26
        "ROUNDUP(f1,f2)"       | ["f1": 6451.255, "f2": -2]   || 6500.0
        "ROUNDUP(f1,f2)"       | ["f1": 6451.255, "f2": -2.2] || 6500.0
        "ROUNDDOWN(f1,f2)"     | ["f1": 1.255, "f2": 2]       || 1.25
        "ROUNDDOWN(f1,f2)"     | ["f1": 1.254, "f2": 2]       || 1.25
        "ROUNDDOWN(f1,f2)"     | ["f1": 6451.255, "f2": -2]   || 6400.0
        "ROUNDDOWN(f1,f2)"     | ["f1": 6451.255, "f2": -2.2] || 6400.0
        "ROUNDHALFUP(f1,f2)"   | ["f1": 1.255, "f2": 2]       || 1.26
        "ROUNDHALFUP(f1,f2)"   | ["f1": 1.254, "f2": 2]       || 1.25
        "ROUNDHALFUP(f1,f2)"   | ["f1": 6451.255, "f2": -2]   || 6500.0
        "ROUNDHALFUP(f1,f2)"   | ["f1": 6451.255, "f2": -2.2] || 6500.0
        "ROUNDHALFUP(f1,f2)"   | ["f1": 6441.255, "f2": -2]   || 6400.0
        "ROUNDHALFUP(f1,f2)"   | ["f1": 6441.255, "f2": -2.2] || 6400.0
        "ROUNDHALFDOWN(f1,f2)" | ["f1": 1.255, "f2": 2]       || 1.25
        "ROUNDHALFDOWN(f1,f2)" | ["f1": 1.254, "f2": 2]       || 1.25
        "ROUNDHALFDOWN(f1,f2)" | ["f1": 1.256, "f2": 2]       || 1.26
        "ROUNDHALFDOWN(f1,f2)" | ["f1": 6450.0, "f2": -2]     || 6400.0
        "ROUNDHALFDOWN(f1,f2)" | ["f1": 6450.0, "f2": -2.2]   || 6400.0
        "ROUNDHALFDOWN(f1,f2)" | ["f1": 6441.255, "f2": -2]   || 6400.0
        "ROUNDHALFDOWN(f1,f2)" | ["f1": 6441.255, "f2": -2.2] || 6400.0
        "ROUNDHALFDOWN(f1,f2)" | ["f1": 6450.255, "f2": -2]   || 6500.0
        "ROUNDHALFDOWN(f1,f2)" | ["f1": 6450.255, "f2": -2.2] || 6500.0
        "ROUNDHALFDOWN(f1,f2)" | ["f1": 6461.255, "f2": -2]   || 6500.0
        "ROUNDHALFDOWN(f1,f2)" | ["f1": 6461.255, "f2": -2.2] || 6500.0
    }

    def "test MathFunction null evaluate"() {
        given:
        ExpressionEngine engine = new ExpressionEngine()
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression    | bindings               || expectedResult
        //"MIN(f1,f2)"      | ["f1": null, "f2": 30.2]   || 20.1
        //"MAX(f1,f2)"      | ["f1": null, "f2": 30]     || 30
        //"MULTIPLE(f1,f2)" | ["f1": null, "f2": 1.2]     || 1.44
        //"MOD(f1,f2)"      | ["f1": null, "f2": 2]         || 2
        //"MOD(f1,f2)"      | ["f1": 45, "f2": null]     || 2
        "ADDS(f1,f2)" | ["f1": 5.7, "f2": 2.2] || 7.9
        //"SUBTRACTS(f1,f2)"      | ["f1": null, "f2": 2]     || 3
        //"ROUNDUP(f1,f2)"  | ["f1": null, "f2": -2] || 6500.0
        //"ROUNDUP(f1,f2)"  | ["f1": 1, "f2": null] || 6500.0
    }

    def "test MathFunction compile"() {
        given:
        ExpressionEngine engine = new ExpressionEngine()
        when:
        engine.compile(expression)
        then:
        noExceptionThrown()
        where:
        expression                                                                                | bindings
        expressionInfo("MIN(f1,f2)", ["f1": "double", "f2": "double"], "double")                  | bindings()
        expressionInfo("MIN(f1,f2)", ["f1": "int", "f2": "double"], "BigDecimal")                 | bindings()
        expressionInfo("MULTIPLE(f1,f2)", ["f1": "int", "f2": "double"], "double")                | bindings()
        expressionInfo("MULTIPLE(f1,f2)", ["f1": "BigDecimal", "f2": "BigDecimal"], "BigDecimal") | bindings()
        expressionInfo("ROUNDUP(f1,f2)", ["f1": "BigDecimal", "f2": "BigDecimal"], "double")      | bindings()
        expressionInfo("ROUNDUP(f1,f2)", ["f1": "double", "f2": "int"], "double")                 | bindings()
    }

    def "test TextFunction evaluate"() {
        given:
        ExpressionEngine engine = new ExpressionEngine()
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression            | bindings                                         || expectedResult
        "NUMBERSTRINGRMB(f1)" | ["f1": null]                                     || null
        "NUMBERSTRINGRMB(f1)" | ["f1": ""]                                       || null
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("0")]                      || "零元整"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("0.0")]                    || "零元整"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("0.00")]                   || "零元整"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("0.000")]                  || "零元整"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("0.001")]                  || "零元"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("0.0000")]                 || "零元整"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("0.01")]                   || "壹分"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("0.010")]                  || "壹分"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("0.011")]                  || "壹分"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("0.1")]                    || "壹角"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("0.10")]                   || "壹角"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("0.100")]                  || "壹角"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("0.101")]                  || "壹角"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("0.11")]                   || "壹角壹分"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("0.110")]                  || "壹角壹分"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("0.111")]                  || "壹角壹分"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("2")]                      || "贰元整"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("2.0")]                    || "贰元整"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("2.00")]                   || "贰元整"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("2.000")]                  || "贰元整"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("2.001")]                  || "贰元"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("2.1")]                    || "贰元壹角"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("2.10")]                   || "贰元壹角"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("2.100")]                  || "贰元壹角"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("2.101")]                  || "贰元壹角"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("2.01")]                   || "贰元零壹分"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("2.010")]                  || "贰元零壹分"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("2.011")]                  || "贰元零壹分"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("2.11")]                   || "贰元壹角壹分"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("2.110")]                  || "贰元壹角壹分"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("2.111")]                  || "贰元壹角壹分"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("26")]                     || "贰拾陆元整"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("26.0000")]                || "贰拾陆元整"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("265")]                    || "贰佰陆拾伍元整"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("265.0")]                  || "贰佰陆拾伍元整"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("265.00")]                 || "贰佰陆拾伍元整"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("265.000")]                || "贰佰陆拾伍元整"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("265.001")]                || "贰佰陆拾伍元"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("265.05")]                 || "贰佰陆拾伍元零伍分"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("265.40")]                 || "贰佰陆拾伍元肆角"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("265.46")]                 || "贰佰陆拾伍元肆角陆分"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("265.460")]                || "贰佰陆拾伍元肆角陆分"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("265.465")]                || "贰佰陆拾伍元肆角陆分"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("1500")]                   || "壹仟伍佰元整"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("1500.0000")]              || "壹仟伍佰元整"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("26280")]                  || "贰万陆仟贰佰捌拾元整"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("26280.0000")]             || "贰万陆仟贰佰捌拾元整"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("385500")]                 || "叁拾捌万伍仟伍佰元整"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("385500.0000")]            || "叁拾捌万伍仟伍佰元整"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("2142484")]                || "贰佰壹拾肆万贰仟肆佰捌拾肆元整"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("2142484.0000")]           || "贰佰壹拾肆万贰仟肆佰捌拾肆元整"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("12142484")]               || "壹仟贰佰壹拾肆万贰仟肆佰捌拾肆元整"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("12142484.0000")]          || "壹仟贰佰壹拾肆万贰仟肆佰捌拾肆元整"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("189000000")]              || "壹亿捌仟玖佰万元整"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("189000000.0000")]         || "壹亿捌仟玖佰万元整"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("1189000000")]             || "壹拾壹亿捌仟玖佰万元整"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("1189000000.0000")]        || "壹拾壹亿捌仟玖佰万元整"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("10189000000")]            || "壹佰零壹亿捌仟玖佰万元整"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("10189000000.0000")]       || "壹佰零壹亿捌仟玖佰万元整"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("100189000000")]           || "壹仟零壹亿捌仟玖佰万元整"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("100189000000.0000")]      || "壹仟零壹亿捌仟玖佰万元整"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("1000189000000")]          || "壹万零壹亿捌仟玖佰万元整"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("1000189000000.0000")]     || "壹万零壹亿捌仟玖佰万元整"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("1000189000000.05")]       || "壹万零壹亿捌仟玖佰万元零伍分"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("10000000189000000")]      || "壹京零壹亿捌仟玖佰万元整"
        "NUMBERSTRINGRMB(f1)" | ["f1": new BigDecimal("10000000189000000.0000")] || "壹京零壹亿捌仟玖佰万元整"
        "NUMBERSTRINGRMB(f1)" | ["f1": "1000189000000.0000"]                     || "壹万零壹亿捌仟玖佰万元整"
        "NUMBERSTRINGRMB(f1)" | ["f1": "10000000189000000.0000"]                 || "壹京零壹亿捌仟玖佰万元整"
    }

    def "test TextFunction compile"() {
        given:
        ExpressionEngine engine = new ExpressionEngine()
        when:
        engine.compile(expression)
        then:
        noExceptionThrown()
        where:
        expression                                                            | bindings
        expressionInfo("NUMBERSTRINGRMB(f1)", ["f1": "BigDecimal"], "String") | bindings()
    }

    def bindings() {
        def map = ["": ""]
        map
    }


    def expressionInfo(String expression, Map<String, String> fields, String returnType) {
        StringBuilder sb = new StringBuilder();
        sb.append("import groovy.transform.Field\n")

        for (Map.Entry<String, String> field : fields.entrySet()) {
            String fieldType = "@Field " + field.getValue() + " " + field.getKey() + "\n"
            sb.append(fieldType)
        }

        sb.append(returnType + " value() {");
        sb.append(expression.trim())
        sb.append("}")
        sb.toString()
    }

    def "test IF LogicFunction compile"() {
        given:
        ExpressionEngine engine = new ExpressionEngine()
        when:
        engine.compile(expression)
        then:
        noExceptionThrown()
        where:
        expression                                                                                                                                     | bindings
        expressionInfo("IF(true, ROUNDUP(field_eZ1Al__c/8,0)*200, 1)", ["field_eZ1Al__c": "BigDecimal"], "BigDecimal")                                 | bindings()
        expressionInfo("IF(true, ROUNDUP(field_eZ1Al__c/8,0)*200, 1.2)", ["field_eZ1Al__c": "BigDecimal"], "BigDecimal")                               | bindings()
        expressionInfo("IF(true, ROUNDUP(field_eZ1Al__c/8,0)*200, ROUNDUP(field_eZ1Al__c/8,0)*200)", ["field_eZ1Al__c": "BigDecimal"], "BigDecimal")   | bindings()
        expressionInfo("IF(true, ROUNDUP(field_eZ1Al__c/8,0)*200, ROUNDUP(field_eZ1Al__c/8,0)*200.2)", ["field_eZ1Al__c": "BigDecimal"], "BigDecimal") | bindings()
    }
}

