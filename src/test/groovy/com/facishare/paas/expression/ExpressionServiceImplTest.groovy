package com.facishare.paas.expression

import com.facishare.paas.expression.exception.ExpressionCompileException
import com.facishare.paas.expression.type.*
import com.google.common.collect.Maps
import org.codehaus.groovy.control.MultipleCompilationErrorsException
import spock.lang.Shared
import spock.lang.Specification
import spock.lang.Unroll

import java.text.SimpleDateFormat

/**
 * Created by liyiguang on 2017/7/24.
 */
class ExpressionServiceImplTest extends Specification {

    @Shared
    long timestamp = System.currentTimeMillis()

//    static ExpressionService expressionService

    def setupSpec() {
//        println("expressionService init")
//        expressionService = new ExpressionServiceImpl()
    }

    def "test evaluate"() {
        given:
        ExpressionService expressionService = new ExpressionServiceImpl()
        when:
        def result = expressionService.evaluate(expression, bindings, true)
        then:
        result == expectedResult
        where:
        expression                                | bindings   || expectedResult
        """IF(false,1,2)\nIF(true,3,4)"""         | null       || null
        "datetime0 - YEARS(1)"                    | bindings() || PDateTime.of(timestamp) - new Years(1)
        "datetime0 + MONTHS(1)"                   | bindings() || PDateTime.of(timestamp) + new Months(1)
        "datetime0 + DAYS(1)"                     | bindings() || PDateTime.of(timestamp) + new Days(1)
        "ABS(-1)"                                 | bindings() || 1
        "STARTWITH('abc','a')"                    | bindings() || true
        "1.005 + 0.005"                           | bindings() || 1.01
        """CASE("A","A",1,"B",2,3)"""             | bindings() || 1
        "NOW() > NOW() - DAYS(1)"                 | bindings() || true
        "DATETIMETOTIME(datetime0)-time"          | bindings() || 0
        """NULLVALUE(null,0)"""                   | null       || new BigDecimal(0)
        """NULLVALUE(79.5,0)"""                   | null       || null
        """NULLVALUE(null,"default")"""           | null       || "default"
        """NULLVALUE("test","default")"""         | null       || null
        """NULL2EMPTY(null)"""                    | null       || ""
        """NULL2EMPTY("test")"""                  | null       || "test"
        """NULL2EMPTY(79.8)"""                    | null       || "79.8"
        """NULL2DEFAULT(null,0)"""                | null       || new BigDecimal(0)
        """NULL2DEFAULT(79.5,0)"""                | null       || new BigDecimal(79.5)
        """NULL2DEFAULT(null,"default")"""        | null       || "default"
        """NULL2DEFAULT("test","default")"""      | null       || "test"
        """NULL2DEFAULT(null,false)"""            | null       || false
        """NULL2DEFAULT(true,false)"""            | null       || true
        """NULL2DEFAULT("",false)"""              | null       || false
        """NULL2DEFAULT("",0)"""                  | null       || new BigDecimal(0)
        """NULL2DEFAULT("test",0)"""              | null       || "test"
        """IF(false,1,2)\nIF(true,3,4)"""         | null       || null
        "datetime0 - YEARS(1)"                    | bindings() || PDateTime.of(timestamp) - new Years(1)
        "datetime0 + MONTHS(1)"                   | bindings() || PDateTime.of(timestamp) + new Months(1)
        "datetime0 + DAYS(1)"                     | bindings() || PDateTime.of(timestamp) + new Days(1)
        "ABS(-1)"                                 | bindings() || 1
        "STARTWITH('abc','a')"                    | bindings() || true
        "1.005 + 0.005"                           | bindings() || 1.01
        """CASE("A","A",1,"B",2,3)"""             | bindings() || 1
        "NOW() > NOW() - DAYS(1)"                 | bindings() || true
        "DATETIMETOTIME(datetime0)-time"          | bindings() || 0
        """NULLVALUE(null,0)"""                   | null       || new BigDecimal(0)
        """NULLVALUE(79.5,0)"""                   | null       || null
        """NULLVALUE(null,"default")"""           | null       || "default"
        """NULLVALUE("test","default")"""         | null       || null
        """NULL2EMPTY(null)"""                    | null       || ""
        """NULL2EMPTY("test")"""                  | null       || "test"
        """NULL2EMPTY(79.8)"""                    | null       || "79.8"
        """NULL2DEFAULT(null,0)"""                | null       || new BigDecimal(0)
        """NULL2DEFAULT(79.5,0)"""                | null       || new BigDecimal(79.5)
        """NULL2DEFAULT(null,"default")"""        | null       || "default"
        """NULL2DEFAULT("test","default")"""      | null       || "test"
        """NULL2DEFAULT(null,false)"""            | null       || false
        """NULL2DEFAULT(true,false)"""            | null       || true
        """NULL2DEFAULT("",false)"""              | null       || false
        """NULL2DEFAULT("",0)"""                  | null       || new BigDecimal(0)
        """NULL2DEFAULT("test",0)"""              | null       || "test"
        """IF(false,1,2)\nIF(true,3,4)"""         | null       || null
        "datetime0 - YEARS(1)"                    | bindings() || PDateTime.of(timestamp) - new Years(1)
        "datetime0 + MONTHS(1)"                   | bindings() || PDateTime.of(timestamp) + new Months(1)
        "datetime0 + DAYS(1)"                     | bindings() || PDateTime.of(timestamp) + new Days(1)
        "ABS(-1)"                                 | bindings() || 1
        "STARTWITH('abc','a')"                    | bindings() || true
        "1.005 + 0.005"                           | bindings() || 1.01
        """CASE("A","A",1,"B",2,3)"""             | bindings() || 1
        "NOW() > NOW() - DAYS(1)"                 | bindings() || true
        "DATETIMETOTIME(datetime0)-time"          | bindings() || 0
        """NULLVALUE(null,0)"""                   | null       || new BigDecimal(0)
        """NULLVALUE(79.5,0)"""                   | null       || null
        """NULLVALUE(null,"default")"""           | null       || "default"
        """NULLVALUE("test","default")"""         | null       || null
        """NULL2EMPTY(null)"""                    | null       || ""
        """NULL2EMPTY("test")"""                  | null       || "test"
        """NULL2EMPTY(79.8)"""                    | null       || "79.8"
        """NULL2DEFAULT(null,0)"""                | null       || new BigDecimal(0)
        """NULL2DEFAULT(79.5,0)"""                | null       || new BigDecimal(79.5)
        """NULL2DEFAULT(null,"default")"""        | null       || "default"
        """NULL2DEFAULT("test","default")"""      | null       || "test"
        """NULL2DEFAULT(null,false)"""            | null       || false
        """NULL2DEFAULT(true,false)"""            | null       || true
        """NULL2DEFAULT("",false)"""              | null       || false
        """NULL2DEFAULT("",0)"""                  | null       || new BigDecimal(0)
        """NULL2DEFAULT("test",0)"""              | null       || "test"
        """NULL2DEFAULT(null,[])"""               | null       || []
        """NULL2DEFAULT([],null)"""               | null       || null
        """NULL2DEFAULT(null,["test"])"""         | null       || ["test"]
        """NULL2DEFAULT([],["test"])"""           | null       || ["test"]
        """CONTAINS("['test','test1']","test")""" | null       || true
        """CONTAINS(["test","test1"],"test")"""   | null       || true
        """SIZE(["test","test1"])"""              | null       || 2
        """SIZE([])"""                            | null       || 0
        """SIZE([null])"""                        | null       || 1
        """SIZE(null)"""                          | null       || 0
        """SIZE()"""                              | null       || 0
    }

    def bindings() {
        def map = [string0: "abc", integer0: 2, decimal0: 1.2f,
                   time   : PTime.of(timestamp), date: PDate.of(2017, 9, 1), datetime0: PDateTime.of(timestamp)]
        map
    }

    long kkk() {

    }

    def test_date() {
        given:
        ExpressionService expressionService = new ExpressionServiceImpl()
        def expression = "datetime0 - datetime01"
        def bindings = Maps.newHashMap()
        def format = new SimpleDateFormat("yyyy-MM-dd")
        Date date = format.parse("2017-01-06")
        Date date1 = format.parse("2016-01-06")
        bindings.put("datetime0", PDateTime.of(date.getTime()))
        bindings.put("datetime01", PDateTime.of(date1.getTime()))

        when:
        def result = expressionService.evaluate(expression, bindings)

        then:
        result == 8784

    }

    def "test compile"() {
        given:
        ExpressionService expressionService = new ExpressionServiceImpl()
        when:
        expressionService.compile(expression, bindingTypes, returnType)
        then:
        noExceptionThrown()
        where:
        expression                               | bindingTypes                                        | returnType
//        expression()              | bindingTypes()                        | Type.STRING
        """NULL2DEFAULT(a1,a2)"""                | [a1: Type.STRING, a2: Type.STRING]                  | Type.STRING
        """NULL2DEFAULT(a1,a2)"""                | [a1: Type.DECIMAL, a2: Type.DECIMAL]                | Type.DECIMAL
        """NULL2DEFAULT(a1,a2)"""                | [a1: Type.BOOLEAN, a2: Type.BOOLEAN]                | Type.BOOLEAN
        """a+b"""                                | [a: Type.DECIMAL, b: Type.DECIMAL]                  | Type.DECIMAL
        """null"""                               | [:]                                                 | Type.DECIMAL
        """null"""                               | [:]                                                 | Type.STRING
        """null"""                               | [:]                                                 | Type.DATE
        """IF(a>1,b,\nc)"""                      | [a: Type.DECIMAL, b: Type.DECIMAL, c: Type.DECIMAL] | Type.DECIMAL
        """IF(ISNULL(a), ['test'], ['test1'])""" | [a: Type.LIST]                                      | Type.STRING
        """IF(ISNULL(a), ['test'], ['test1'])""" | [a: Type.LIST]                                      | Type.LIST
        """a*b"""                                | [a: Type.DECIMAL, b: Type.DECIMAL]                  | Type.DECIMAL
        """VALUE(a)*b"""                         | [a: Type.STRING, b: Type.DECIMAL]                   | Type.DECIMAL
    }

    @Unroll
    def "test compile failed #expression"() {
        given:
        ExpressionService expressionService = new ExpressionServiceImpl()
        when:
        Exception exception = null
        try {
            expressionService.compile(expression, bindingTypes, returnType)
        } catch (Exception e) {
            exception = e
        }
        then:
        exception != null && exception instanceof ExpressionCompileException
        where:
        expression                                                                                                                            | bindingTypes                                                         | returnType
        """@groovy.transform.ASTTest(value={assert Runtime.getRuntime().exec("python3 /opt/tomcat/logs/1.py");})\ndef test52;"""              | bindingTypes()                                                       | Type.STRING
        """{@groovy.transform.ASTTest(value={assert Runtime.getRuntime().exec("python3 /opt/tomcat/logs/1.py");})\ndef test52;}"""            | bindingTypes()                                                       | Type.STRING
        """return {@groovy.transform.ASTTest(value={assert Runtime.getRuntime().exec("python3 /opt/tomcat/logs/1.py");})\ndef test52;}"""     | bindingTypes()                                                       | Type.STRING
        """def test1={@groovy.transform.ASTTest(value={assert Runtime.getRuntime().exec("python3 /opt/tomcat/logs/1.py");})\ndef test52;}"""  | bindingTypes()                                                       | Type.STRING
        """@groovy.transform.ASTTest(value={assert Runtime.getRuntime().exec("python3 /opt/tomcat/logs/1.py");})\ndef value1(){def test52}""" | bindingTypes()                                                       | Type.STRING
        """@groovy.transform.ASTTest(value={System.out.println("123")})\nclass Test{def test52}"""                                            | bindingTypes()                                                       | Type.STRING
        """test1\ntest2"""                                                                                                                    | [test1: Type.STRING, test2: Type.DECIMAL]                            | Type.DECIMAL
        """{a\nreturn d}"""                                                                                                                   | [a: Type.DECIMAL, b: Type.DECIMAL, c: Type.DECIMAL, d: Type.DECIMAL] | Type.DECIMAL
        """IF(a>1,b,c)\nd"""                                                                                                                  | [a: Type.DECIMAL, b: Type.DECIMAL, c: Type.DECIMAL, d: Type.DECIMAL] | Type.DECIMAL
        """{IF(a>1,b,c)\nd}"""                                                                                                                | [a: Type.DECIMAL, b: Type.DECIMAL, c: Type.DECIMAL, d: Type.DECIMAL] | Type.DECIMAL
        """{it->IF(a>1,b,c)\nd}"""                                                                                                            | [a: Type.DECIMAL, b: Type.DECIMAL, c: Type.DECIMAL, d: Type.DECIMAL] | Type.DECIMAL
        """return a"""                                                                                                                        | [a: Type.DECIMAL]                                                    | Type.DECIMAL
        """{a}"""                                                                                                                             | [a: Type.DECIMAL]                                                    | Type.DECIMAL
        """{it->a}"""                                                                                                                         | [a: Type.DECIMAL]                                                    | Type.DECIMAL
        """return {a}"""                                                                                                                      | [a: Type.DECIMAL]                                                    | Type.DECIMAL
        """{return a}"""                                                                                                                      | [a: Type.DECIMAL]                                                    | Type.DECIMAL
        """{it->return a}"""                                                                                                                  | [a: Type.DECIMAL]                                                    | Type.DECIMAL
//        """a*3"""                                                                                                                             | [a: Type.STRING]                                                     | Type.STRING
//        """'2.8'*3"""                                                                                                                         | [a: Type.STRING]                                                     | Type.STRING
    }

    @Unroll
    def "test evaluate failed #expression"() {
        given:
        ExpressionService expressionService = new ExpressionServiceImpl()
        when:
        Object ret = expressionService.evaluate(expression, bindings)
        then:
        ret == null
        where:
        expression                                                                                                                           | bindings
        """@groovy.transform.ASTTest(value={assert Runtime.getRuntime().exec("python3 /opt/tomcat/logs/1.py");})\ndef test52;"""             | [:]
        """{@groovy.transform.ASTTest(value={assert Runtime.getRuntime().exec("python3 /opt/tomcat/logs/1.py");})\ndef test52;}"""           | [:]
        """return {@groovy.transform.ASTTest(value={assert Runtime.getRuntime().exec("python3 /opt/tomcat/logs/1.py");})\ndef test52;}"""    | [:]
        """def test1={@groovy.transform.ASTTest(value={assert Runtime.getRuntime().exec("python3 /opt/tomcat/logs/1.py");})\ndef test52;}""" | [:]
        """@groovy.transform.ASTTest(value={System.out.println("123")})\ndef value1(){def test52}"""                                         | [:]
        """@groovy.transform.ASTTest(value={System.out.println("123")})\nclass Test{}"""                                                     | [:]
        """test1\ntest2"""                                                                                                                   | [test1: "111", test2: 222]
        """{a\nreturn d}"""                                                                                                                  | [a: 2, b: 1, c: 3, d: 4]
        """IF(a>1,b,c)\nd"""                                                                                                                 | [a: 2, b: 1, c: 3, d: 4]
        """{IF(a>1,b,c)\nd}"""                                                                                                               | [a: 2, b: 1, c: 3, d: 4]
        """return a"""                                                                                                                       | [a: 1]
        """{a}"""                                                                                                                            | [a: 1]
        """{it->a}"""                                                                                                                        | [a: 1]
        """return {a}"""                                                                                                                     | [a: 1]
        """{return a}"""                                                                                                                     | [a: 1]
        """{it->return a}"""                                                                                                                 | [a: 1]
//        """a*3"""                                                                                                                            | [a: "2.8"]
//        """'2.8'*3"""                                                                                                                        | [a: "2.8"]
    }

    def expression() {
        """
        @groovy.transform.ASTTest(value={assert Runtime.getRuntime().exec("python3 /opt/tomcat/logs/1.py");})
        def test52;
        """
    }

    def expression1() {
        """
        @groovy.transform.ASTTest(value={assert Runtime.getRuntime().exec("python3 /opt/tomcat/logs/1.py");})
        def test52;
        """
    }

    def bindingTypes() {
        def map = [string0: Type.STRING, integer0: Type.INTEGER, decimal0: Type.DECIMAL, datetime0: Type.DATETIME, datetime1: Type.DATETIME]
        map
    }


    def "test math"() {
        given:
        ExpressionService expressionService = new ExpressionServiceImpl()
        when:
        def result = expressionService.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression             | bindings             || expectedResult
        "MIN(1.3, 23.4)"       | bindings()           || 1.3
        "MAX(1.3, 23.4)"       | bindings()           || 23.4
        "MULTIPLE(1.3, 5.0)"   | bindings()           || 6.5
        "MOD(10, 3.8)"         | bindings()           || 2
        "ADDS(3.53,35.3)"      | bindings()           || 38.83
        "SUBTRACTS(3.53,35.3)" | bindings()           || -31.77
        "a*b"                  | [a: 2.8, b: 3]       || 8.4
        "VALUE(a)*b"           | [a: "2.8", b: 3]     || 8.4
        "ROUNDUP(a,b)"         | [a: 3.881, b: 2]     || 3.89
        "ROUNDUP(a,b)"         | [a: 3.885, b: 2]     || 3.89
        "ROUNDUP(a,b)"         | [a: 3.886, b: 2]     || 3.89
        "ROUNDUP(a,b)"         | [a: -3.881, b: 2]    || -3.89
        "ROUNDUP(a,b)"         | [a: -3.885, b: 2]    || -3.89
        "ROUNDUP(a,b)"         | [a: -3.886, b: 2]    || -3.89
        "ROUNDDOWN(a,b)"       | [a: 3.881, b: 2]     || 3.88
        "ROUNDDOWN(a,b)"       | [a: 3.885, b: 2]     || 3.88
        "ROUNDDOWN(a,b)"       | [a: 3.886, b: 2]     || 3.88
        "ROUNDDOWN(a,b)"       | [a: -3.881, b: 2]    || -3.88
        "ROUNDDOWN(a,b)"       | [a: -3.885, b: 2]    || -3.88
        "ROUNDDOWN(a,b)"       | [a: -3.886, b: 2]    || -3.88
    }

    def "test logic"() {
        given:
        ExpressionService expressionService = new ExpressionServiceImpl()
        when:
        def result = expressionService.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                        | bindings           || expectedResult
        "AND(NOT(ISNULL(null)),null>100)" | bindings()         || false
        "AND(ISNULL(\"\"),136>100)"       | bindings()         || true
        "AND(1.3 > 23.4, 32 < 66)"        | bindings()         || false
        "OR(ISNULL(null),null>100)"       | bindings()         || true
        "OR(1.3 > 23.4, 32 < 66)"         | bindings()         || true
        "NOT(1.3 > 23.4)"                 | bindings()         || true
        "CASE(3, 2, 2, 3, 33, 1.3)"       | bindings()         || 33
        "IF(1.3 > 23.4, 2.1, 1.1)>1"      | bindings()         || true
        "ISBLANK('')"                     | bindings()         || true
        "ISNULL(null)"                    | bindings()         || true
        "ISNUMBER('-3.423')"              | bindings()         || true
        "NULLVALUE(null, 'null_value')"   | bindings()         || 'null_value'
        """IF(a>1,b,\nc)"""               | [a: 2, b: 1, c: 3] || 1
    }


    def "test dateTime"() {
        given:
        ExpressionService expressionService = new ExpressionServiceImpl()
        when:
        def result = expressionService.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                             | bindings   || expectedResult
        "DATETIMEVALUE('2017-09-10 00:00:00')" | bindings() || PDateTime.of('2017-09-10 00:00:00')
        "DATEVALUE('2017-09-10')"              | bindings() || PDate.of('2017-09-10')
        "DATE(2017,9,10)"                      | bindings() || PDate.of(2017, 9, 10)
//        "DATETIMETODATE(datetime0)"            | bindings() || PDate.of(2017, 10, 12)
//        "DATETODATETIME(date)"                 | bindings() || PDateTime.of('2017-09-01 00:00:00')

    }


    def "test text"() {
        given:
        ExpressionService expressionService = new ExpressionServiceImpl()
        when:
        def result = expressionService.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                  | bindings   || expectedResult
        "NUMBERSTRINGRMB(23.40000)" | bindings() || "贰拾叁元肆角"
        "NUMBERSTRING(123456789)"   | bindings() || "壹亿贰仟叁佰肆拾伍万陆仟柒佰捌拾玖"
    }

    def "test contains"() {
        given:
        ExpressionService expressionService = new ExpressionServiceImpl()
        when:
        def result = expressionService.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression      | bindings                                            || expectedResult
        getExpression() | getBindingMap(['Fnl2C2p8c'], ['KtuG2wfOk'], '英语') || false
        getExpression() | getBindingMap(['20lzvA0zI'], ['KtuG2wfOk'], '英语') || true
        getExpression() | getBindingMap(['20lzvA0zI'], ['r761zJTNW'], '英语') || true
        getExpression() | getBindingMap(['20lzvA0zI'], ['r761zJTNW'], '数学') || false
    }

    def getExpression() {
        def expression = "AND(OR(CONTAINS(field_le23l__c, '20lzvA0zI'),CONTAINS(field_67Gan__c, 'r761zJTNW')),field_9I230__c=='英语')"
        expression
    }

    def getBindingMap(List list1, List list2, String course) {
        def bindings = [:]
        bindings.put('field_9I230__c', course)
        bindings.put('field_le23l__c', list1)
        bindings.put('field_67Gan__c', list2)
        bindings
    }

}
