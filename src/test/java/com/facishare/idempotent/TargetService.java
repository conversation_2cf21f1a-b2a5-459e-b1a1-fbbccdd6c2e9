package com.facishare.idempotent;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * Created by liyiguang on 2019/4/18.
 */

@Service
public class TargetService {
//
//
//    @Limiter(QPS = 400, concurrency = 1)
//    public String helloLimiter(String a) {
//
//        try {
//            TimeUnit.MILLISECONDS.sleep(5);  //QPS 200
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        }
//
//        return a;
//    }
//
//    @Limiter(QPS = 400, concurrency = 2)
//    public String helloLimiter2(String a) {
//
//        try {
//            TimeUnit.MILLISECONDS.sleep(5);  //QPS 200
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        }
//
//        return a;
//    }


    @Idempotent()
    public Result hello(String world) {

        try {
            TimeUnit.SECONDS.sleep(5);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return Result.builder()
                .status("Success")
                .data("hello " + world).build();
    }

    @Idempotent()
    public Result hello_requestId(@RequestId String requestId, String world) {

        try {
            TimeUnit.SECONDS.sleep(5);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return Result.builder()
                .status("Success")
                .data("hello " + world).build();
    }

    @Idempotent()
    public Result hello_requestIdArg(@RequestId(propertyName = "requestId") RequestIdArg arg, String world) {

        try {
            TimeUnit.SECONDS.sleep(5);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return Result.builder()
                .status("Success")
                .data("hello " + world).build();
    }


    @Idempotent(waitTime = 2)
    public Result hello_wait(@RequestId String requestId, String world) {

        try {
            TimeUnit.SECONDS.sleep(5);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return Result.builder()
                .status("Success")
                .data("hello " + world).build();
    }


    @Idempotent()
    public Result hello2(String world) {

        try {
            TimeUnit.SECONDS.sleep(5);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return Result.builder()
                .status("Success")
                .data("hello " + world).build();
    }


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    private static class Result {
        Object data;
        String status;
    }

    @Data
    @Builder
    public static class RequestIdArg {
        String requestId;
    }

}
