package com.facishare.paas.expression.functions

trait CollectionFunction {

    boolean ARRAYCONTAINS(Collection list, String value) {
        if (!list) {
            return false
        }
        return list.contains(value)
    }

    boolean ARRAYCONTAINSALL(Collection list1, Collection list2) {
        if (!list1 || !list2) {
            return false
        }
        return list1.containsAll(list2)
    }

    int SIZE(Collection list) {
        if(!list) {
            return 0
        }
        return list.size()
    }
    // 不看顺序的相等判断  any
}
