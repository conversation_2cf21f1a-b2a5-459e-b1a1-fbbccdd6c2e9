package com.facishare.paas.expression.functions

import com.facishare.paas.expression.type.*
import com.facishare.paas.timezone.DateUtils
import com.facishare.paas.timezone.TimeZoneContextHolder

import java.text.SimpleDateFormat
import java.time.LocalDate

/**
 * Created by liyiguang on 2017/9/10.
 */
trait DateTimeFunction {

    PDateTime NOW() {
        return PDateTime.of(System.currentTimeMillis())
    }

    PDateTime DATETIMEVALUE(String value) {
        return PDateTime.of(value)
    }

    PDateTime LONGDATETIMEVALUE(long timestamp) {
        return PDateTime.of(timestamp)
    }

    Years YEARS(Number value) {
        return new Years(value.intValue())
    }

    Years YEARS(BigDecimal value) {
        return new Years(value.intValue())
    }

    Months MONTHS(Number value) {
        return new Months(value.intValue())
    }

    Months MONTHS(BigDecimal value) {
        return new Months(value.intValue())
    }

    Days DAYS(Number value) {
        return new Days(value.intValue())
    }

    Days DAYS(BigDecimal value) {
        return new Days(value.intValue())
    }

    Hours HOURS(Number value) {
        return new Hours(value.intValue())
    }

    Hours HOURS(BigDecimal value) {
        return new Hours(value.intValue())
    }

    Minutes MINUTES(BigDecimal value) {
        return new Minutes(value.intValue())
    }

    Minutes MINUTES(Number value) {
        return new Minutes(value.intValue())
    }

    PDate DATEVALUE(String value) {
        return PDate.of(value)
    }

    PDate LONGDATEVALUE(long timestamp) {
        return PDate.of(timestamp)
    }

    PDate DATE(Number year, Number month, Number day) {
        return PDate.of(year.intValue(), month.intValue(), day.intValue())
    }

    PDate DATE() {
        if (TimeZoneContextHolder.isGray()) {
            return PDate.of(DateUtils.nowDate(TimeZoneContextHolder.getTenantTimeZone()))
        }
        return new PDate(LocalDate.now())
    }

    PDate DATE(BigDecimal year, BigDecimal month, BigDecimal day) {
        return PDate.of(year.intValue(), month.intValue(), day.intValue())
    }

    PDate TODAY() {
        if (TimeZoneContextHolder.isGray()) {
            return PDate.of(DateUtils.nowDate(TimeZoneContextHolder.getTenantTimeZone()))
        }
        def date = new Date()
        def dateFormat = new SimpleDateFormat("yyyy-MM-dd")
        def dateValue = dateFormat.format(date)
        PDate.of(dateValue)
    }

    int YEAR(PDate pDate) {
        return pDate.getYear()
    }

    int YEAR(PDateTime pDateTime) {
        return pDateTime.getYear()
    }


    int MONTH(PDateTime pDateTime) {
        return pDateTime.getMonth()
    }

    int MONTH(PDate pDate) {
        return pDate.getMonth()
    }

    int DAY(PDate pDate) {
        return pDate.getDay()
    }

    int DAY(PDateTime pDateTime) {
        return pDateTime.getDay()
    }

    PDateTime DATETODATETIME(PDate pDate) {
        return PDateTime.of(pDate.getYear(), pDate.getMonth(), pDate.getDay(), 0, 0, 0)
    }

    PDate DATETIMETODATE(PDateTime pDateTime) {
        return PDate.of(pDateTime.getYear(), pDateTime.getMonth(), pDateTime.getDay())
    }

    PTime DATETIMETOTIME(PDateTime pDateTime) {
        return PTime.of(pDateTime.toTimeStamp())
    }

    long TOTIMESTAMP(PDateTime pDateTime) {
        return pDateTime.toTimeStamp()
    }

    long TOTIMESTAMP(PDate pDate) {
        return pDate.toTimeStamp()
    }

    long TOTIMESTAMP(PTime pTime) {
        return pTime.toTimeStamp()
    }

}