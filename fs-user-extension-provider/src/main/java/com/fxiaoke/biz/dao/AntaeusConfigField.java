package com.fxiaoke.biz.dao;

/**
 * Description
 * Created by shang<PERSON><PERSON>g on 16/8/9.
 */
public interface AntaeusConfigField {
    String version = "VN";//该升级客户端的版本号

    String subClientType = "SCT";//NONE(0),HTML(1),ANDROID(2),IOS(3),WINDOWS(4),MAC(5);此处只能为安卓或iOS

    String downloadUrl = "DU";//下载链接

    String antaeusType = "AT";//升级弹窗的类型

    String title = "TT";//默认模式升级弹窗的标题

    String description = "DESC"; // 默认模式升级弹窗的简介

    String h5URL = "HURL"; // 如果不是遮罩采用h5页面，则此处是升级提示的h5页面的URL。

    String button1 = "B1";//默认模式按钮1类型

    String button1Text = "B1D";//默认模式按钮1文本

    String button2 = "B2";//默认模式按钮2类型

    String button2Text = "B2D";//默认模式按钮2文本

    String ignoreTimes = "IT";//点击Later按钮多少次后相当于点击Ignore按钮。

    String createTime = "CT";

    String modifyTime = "MT";

    String isDelete = "DEL";

    String channel = "CHN";

    String startClientVersion = "SCV";
}
