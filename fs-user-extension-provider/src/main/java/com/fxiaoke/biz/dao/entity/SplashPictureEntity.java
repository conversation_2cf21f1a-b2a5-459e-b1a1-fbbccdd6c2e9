package com.fxiaoke.biz.dao.entity;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.api.model.SplashPictureDto;
import com.fxiaoke.api.model.SplashPictureItemDto;
import com.fxiaoke.biz.dao.entity.property.SplashPictureEntityProperty;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 闪屏图
 *
 * <AUTHOR>
 */

@Entity(value = "SplashPicture", noClassnameStored = true)
@Indexes({
        @Index(fields = {
                @Field(SplashPictureEntityProperty.ENTERPRISE_ID),
                @Field(SplashPictureEntityProperty.STATUS),
                @Field(SplashPictureEntityProperty.SPLASH_PICTURE_ID)
        }
                , options = @IndexOptions(dropDups = true, background = true)),
})

@Data
public class SplashPictureEntity {
    @Id
    private ObjectId id;

    @Property(SplashPictureEntityProperty.ENTERPRISE_ID)
    private Integer enterpriseId;

    @Property(SplashPictureEntityProperty.SPLASH_PICTURE_ID)
    private String splashPictureId;

    @Embedded(SplashPictureEntityProperty.IMAGE_URL)
    private String imageUrl;

    @Property(SplashPictureEntityProperty.PICTURE_INFO)
    private String pictureInfo;

    @Property(SplashPictureEntityProperty.DURATION)
    private long duration;

    @Property(SplashPictureEntityProperty.ACTION)
    private String action;

    @Property(SplashPictureEntityProperty.UPDATE_EMPLOYEE_ID)
    private Integer updateEmployeeId;

    @Property(SplashPictureEntityProperty.UPDATE_TIME)
    private Long updateTime;

    @Property(SplashPictureEntityProperty.STATUS)
    private Integer status;

    public static List<SplashPictureDto> convert2Dto(List<SplashPictureEntity> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return new ArrayList<>();
        }

        return entities.stream().map(SplashPictureEntity::convert2Dto).collect(Collectors.toList());
    }

    private static SplashPictureDto convert2Dto(SplashPictureEntity entity) {
        if (Objects.isNull(entity)) {
            return null;
        }
        SplashPictureDto splashPictureDto = new SplashPictureDto();
        splashPictureDto.setEnterpriseId(entity.enterpriseId);
        splashPictureDto.setImageUrl(entity.imageUrl);
        splashPictureDto.setDuration(entity.duration);
        splashPictureDto.setAction(entity.action);
        splashPictureDto.setUpdateEmployeeId(entity.updateEmployeeId);
        splashPictureDto.setUpdateTime(entity.updateTime);
        if (StringUtils.isNotEmpty(entity.pictureInfo)) {
            splashPictureDto.setPictureInfos(JSONObject.parseArray(entity.pictureInfo, SplashPictureItemDto.class));
        }
        return splashPictureDto;
    }
}
