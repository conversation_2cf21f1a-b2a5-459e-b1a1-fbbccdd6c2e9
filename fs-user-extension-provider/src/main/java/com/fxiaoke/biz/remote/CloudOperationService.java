package com.fxiaoke.biz.remote;

import com.facishare.cloud.operation.api.model.Employee;
import com.facishare.cloud.operation.api.model.arg.UpdateStrategyArg;
import com.facishare.cloud.operation.api.service.StrategyConfigService;
import com.facishare.converter.EIEAConverter;
import com.fxiaoke.biz.profile.ConfigCoreProviderService;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class CloudOperationService {

    @Autowired
    private StrategyConfigService strategyConfigService;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private ConfigCoreProviderService configCoreService;

    public void updateFloatingNumber(int enterpriseId, Collection<Integer> employeeIds, List<String> appId) {
        if (CollectionUtils.isEmpty(employeeIds)) {
            return;
        }

        Long effectiveTime = configCoreService.getFloatingNumberEffectiveTime();
        long now = System.currentTimeMillis();
        String ea = eieaConverter.enterpriseIdToAccount(enterpriseId);

        UpdateStrategyArg updateStrategyArg = new UpdateStrategyArg();
        updateStrategyArg.setStrategyName("");
        updateStrategyArg.setPlatform(1);
        ArrayList<String> appIdList = Lists.newArrayList(appId);
        updateStrategyArg.setAppIdList(appIdList);
        updateStrategyArg.setStartTime(now);
        updateStrategyArg.setExpireTime(now + effectiveTime);
        updateStrategyArg.setOperationType(1);
        updateStrategyArg.setNotifyPolingRealTime(true);

        updateStrategyArg.setTargetEmployees(employeeIds.stream().map(id -> new Employee(ea, id)).collect(Collectors.toList()));

        strategyConfigService.updateStrategy(updateStrategyArg);
    }
}
