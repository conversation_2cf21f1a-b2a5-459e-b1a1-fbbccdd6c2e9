package com.fxiaoke.biz.remote.codec;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.FunctionException;
import com.facishare.paas.appframework.core.exception.FunctionTimeoutException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.rest.core.codec.IRestCodeC;
import com.facishare.rest.core.exception.RestProxyBusinessException;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.List;
import java.util.Map;

@Slf4j
public class UserGroupRemoteCodec implements IRestCodeC {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    static {
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
        objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
    }

    @Override
    public <T> byte[] encodeArg(T obj) {
        try {
            return objectMapper.writeValueAsBytes(obj);
        } catch (IOException e) {
            throw new RuntimeException("encode error", e);
        }
    }

    @Override
    public <T> T decodeResult(int statusCode, Map<String, List<String>> map, byte[] bytes, Class<T> aClass) {
        if (statusCode == 504) { // apiBus网关超时
            throw new FunctionTimeoutException(I18N.text(I18NKey.FUNC_TIMEOUT));
        }
        if (statusCode != 200) {
            log.error("decode error,body:{}", bytes != null ? new String(bytes) : "");
            throw new FunctionException(I18N.text(I18NKey.FUNC_FAIL));
        }
        try {
            JsonNode root = objectMapper.readTree(bytes);
            int errCode = root.get("errCode").asInt();
            JsonNode errMsg = root.get("errMessage");
            if (errCode == 400) {
                throw new ValidateException(I18NExt.text(I18NKey.ERRORCODE_REST_CALL_FAIL));
            }
            if (errCode != 0) {
                throw new RestProxyBusinessException(errCode, errMsg.asText());
            }
            return objectMapper.readValue(root.get("result").toString(), aClass);
        } catch (IOException e) {
            log.error("userGroup decode error", e);
            throw new FunctionException(I18N.text(I18NKey.ERRORCODE_REST_CALL_FAIL));
        }
    }
}

