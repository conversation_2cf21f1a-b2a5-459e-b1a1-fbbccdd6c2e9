package com.fxiaoke.biz.config;

import com.facishare.webpage.customer.core.service.EnterpriseConfigProxyService;
import com.fxiaoke.biz.util.GraySwitch;
import com.google.common.collect.ImmutableSet;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.Set;

@Slf4j
@Service
public class NewCrmCheckService {

    private static final Set<String> USE_GO_NEW_CRM_VARIABLE_BUSINESS_SET = ImmutableSet.of("globalTopBar");

    @Autowired
    private EnterpriseConfigProxyService enterpriseConfigProxyService;

    @Autowired
    private UiPaasConfig uiPaaSConfig;

    
    public boolean checkGoNewCrm(int enterpriseId) {
        //通过GraySwitch开启新版CRM
        if (GraySwitch.isOpenNewCrmByGraySwitch(enterpriseId)) {
            return true;
        }
        //灰度了EnterpriseConfig的走新逻辑
        if (GraySwitch.isGrayOpenNewCrmByEnterpriseConfig(enterpriseId)) {
            boolean ret = enterpriseConfigProxyService.isOpenNewCrm(enterpriseId);
            log.info("checkGoNewCRM By EnterpriseConfig,enterpriseId:{} ret:{}", enterpriseId, ret);
            return ret;
        }
        return uiPaaSConfig.checkGoNewCrm(enterpriseId);
    }

    public boolean isAllowForMenuWidget(String business, int enterpriseId, Integer employeeId) {
        if (USE_GO_NEW_CRM_VARIABLE_BUSINESS_SET.contains(business)
                && GraySwitch.isGrayOpenNewCrmByEnterpriseConfig(enterpriseId)) {
            return enterpriseConfigProxyService.isOpenNewCrm(enterpriseId);
        }
        return GraySwitch.isAllowForMenuWidget(business, enterpriseId, employeeId);
    }

}
