package com.fxiaoke.biz.config;

import com.facishare.converter.EIEAConverter;
import com.facishare.sandbox.api.SandboxService;
import com.facishare.sandbox.module.sandbox.GetSandboxConfigByEnterpriseId;
import com.fxiaoke.biz.util.GraySwitch;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.github.autoconf.api.IniChangeListener;
import com.github.autoconf.base.IniConfig;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/17 5:55 下午
 */
@Component
@Slf4j
public class UiPaasConfig {
    @Resource
    private SandboxService sandboxService;
    @Autowired
    private EIEAConverter eieaConverter;

    private List<String> supportAvaObjects = Lists.newArrayList();
    private List<String> userV3DropListAppIds = Lists.newArrayList();
    private String httpDomain;
    private List<Integer> goNewCrmEnterpriseIdList = Lists.newArrayList();
    // 厂商门户自定义 关系下游主导航 的默认行为 useOldRange 的 EA
    private List<String> useOldRange = Lists.newArrayList();

    @PostConstruct
    public void init() {
        ConfigFactory.getConfig("fs-ui-paas-config.ini", new IniChangeListener("appObject") {
            @Override
            public void iniChanged(IniConfig iniConfig) {
                reloadAppObject(iniConfig);
            }
        });
        ConfigFactory.getConfig("fs-ui-paas-config.ini", new IniChangeListener("appStandard") {
            @Override
            public void iniChanged(IniConfig iniConfig) {
                reloadStandard(iniConfig);
            }
        });

        ConfigFactory.getConfig("fs-ui-paas-config.ini", new IniChangeListener("OTHER") {
            @Override
            public void iniChanged(IniConfig iniConfig) {
                reloadOther(iniConfig);
            }
        });
        ConfigFactory.getConfig("variables_crm_migrate_tenants", this::reloadMigrateTenants);
    }

    private void reloadMigrateTenants(IConfig config) {
        String goNewCrmEnterpriseIds = config.get("goNewCrmEnterpriseIds");
        if (StringUtils.isEmpty(goNewCrmEnterpriseIds)) {
            return;
        }
        String[] goNewCrmEnterpriseIdsStr = goNewCrmEnterpriseIds.split(",");
        List<Integer> goNewCrmEnterpriseIdList = Lists.newArrayList();
        for (String enterpriseId : goNewCrmEnterpriseIdsStr) {
            if (StringUtils.isEmpty(enterpriseId)) {
                continue;
            }
            goNewCrmEnterpriseIdList.add(Integer.parseInt(enterpriseId.trim()));
        }
        this.goNewCrmEnterpriseIdList = goNewCrmEnterpriseIdList;
        log.info("init reloadMigrateTenants goNewCrmEnterpriseIdList:{}", this.goNewCrmEnterpriseIdList);
    }

    private void reloadStandard(IniConfig iniConfig) {
        String httpDomain = iniConfig.get("http_domain");
        this.httpDomain = httpDomain;
        log.info("reloadStandard httpDomain:{}", this.httpDomain);
    }
    private void reloadOther(IniConfig iniConfig) {
        useOldRange = Lists.newArrayList();
        useOldRange.addAll(Arrays.asList(iniConfig.get("useOldRange").split(",")));
    }

    public boolean useOldRange(String ea){
        return useOldRange.contains(ea);
    }

    private void reloadAppObject(IniConfig iniConfig) {
        String supportAvaObject = iniConfig.get("supportAvaObject");
        String[] split = supportAvaObject.split(",");
        List<String> supportAvaObjects = Lists.newArrayList();
        for (String apiName : split) {
            if (StringUtils.isEmpty(apiName)) {
                continue;
            }
            supportAvaObjects.add(apiName.trim());
        }
        this.supportAvaObjects = supportAvaObjects;

        String useV3DropList = iniConfig.get("useV3DropList");
        String[] userV3DropListSplit = useV3DropList.split(",");
        List<String> userV3DropListAppIds = Lists.newArrayList();
        for (String userV3DropListAppId : userV3DropListSplit) {
            if (StringUtils.isEmpty(userV3DropListAppId)) {
                continue;
            }
            userV3DropListAppIds.add(userV3DropListAppId);
        }
        this.userV3DropListAppIds = userV3DropListAppIds;
        log.info("reloadAppObject supportAvaObjects:{}, userV3DropListAppIds:{}", this.supportAvaObjects, this.userV3DropListAppIds);
    }

    public List<String> querySupportAvaObjects() {
        return Collections.unmodifiableList(supportAvaObjects);
    }

    public String getHttpDomain() {
        return httpDomain;
    }

    public boolean useV3DropList(String appId) {
        if (StringUtils.isEmpty(appId)) {
            return false;
        }
        return userV3DropListAppIds.contains(appId);
    }

    public boolean checkGoNewCrm(int enterpriseId) {
        return goNewCrmEnterpriseIdList.contains(enterpriseId);
    }


    public boolean checkNewAppCustomer(int enterpriseId) {
        String ea = eieaConverter.enterpriseIdToAccount(enterpriseId);
        if(ea.contains("sandbox")){
        //所有企业都查询一下源企业
        GetSandboxConfigByEnterpriseId.Argument argument = new GetSandboxConfigByEnterpriseId.Argument();
        argument.setEnterpriseId(enterpriseId);
        GetSandboxConfigByEnterpriseId.Result result = sandboxService.getSandboxConfigByEnterpriseId(argument);
        if (result != null && result.getConfig() != null) {
            int ei = result.getConfig().getEnterpriseId();
            return GraySwitch.isAllowForEi(GraySwitch.APP_CUSTOM_CACHE_GRAY_NEW, ei);
        }
        }
        return GraySwitch.isAllowForEi(GraySwitch.APP_CUSTOM_CACHE_GRAY_NEW, enterpriseId);
    }

}
