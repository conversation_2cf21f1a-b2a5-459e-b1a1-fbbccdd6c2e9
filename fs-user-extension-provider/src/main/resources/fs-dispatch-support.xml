<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <!--上报错误日志-->
    <bean id="metricsConfiguration" class="com.fxiaoke.metrics.MetricsConfiguration"/>

    <bean id="userExtersionDynamicOrder" class="com.fxiaoke.dispatcher.processor.DynamicOrder">
        <constructor-arg name="keyPrefix" value="app-task-dispatch"/>
        <constructor-arg name="redissonClient" ref="userExtersionRedissonClient"/>
    </bean>

    <bean id="userExtersionEventStore" class="com.fxiaoke.dispatcher.store.EventStore"
          depends-on="userExtersionMongoStore,userExtersionDynamicOrder">
        <property name="configName" value="fs-user-extersion-provider-dispatcher-config.ini"/>
        <property name="sectionName" value="store"/>
        <constructor-arg name="store" ref="userExtersionMongoStore"/>
        <constructor-arg name="order" ref="userExtersionDynamicOrder"/>
    </bean>

    <bean id="userExtersionMongoStore" name="userExtersionMongoStore" class="com.github.mongo.support.MongoDataStoreFactoryBean">
        <property name="configName" value="fs-user-extersion-provider-dispatcher-config.ini"/>
        <property name="sectionNames" value="mongo"/>
    </bean>

    <bean id="userExtersionRedissonClient" class="com.fxiaoke.common.redisson.RedissonFactoryBean">
        <property name="configName" value="fs-paas-metadata-service-redis"/>
    </bean>

    <bean id="userExtersionLockService" name="lockService" class="com.fxiaoke.dispatcher.processor.LockService">
        <constructor-arg ref="userExtersionRedissonClient"/>
        <property name="prefix" value="describe-layout-change-dispatch-lock"/>
    </bean>

    <bean id="appNavigationDistributeParser"
          class="com.fxiaoke.biz.dispatch.parser.AppNavigationDistributeParser"/>

    <bean id="appNavigationDistributeEventListener"
          class="com.fxiaoke.biz.dispatch.listener.AppNavigationDistributeEventListener"/>

    <bean id="userExtersionEventPorter" class="com.fxiaoke.dispatcher.processor.EventPorter"
          depends-on="userExtersionEventStore,userExtersionLockService,userExtersionDynamicOrder">
        <property name="configName" value="fs-user-extersion-provider-dispatcher-config.ini"/>
        <property name="processor" ref="appNavigationDistributeEventListener"/>
        <constructor-arg name="store" ref="userExtersionEventStore"/>
        <constructor-arg name="parser" ref="appNavigationDistributeParser"/>
        <constructor-arg name="lock" ref="userExtersionLockService"/>
        <constructor-arg name="order" ref="userExtersionDynamicOrder"/>
        <constructor-arg name="counter" ref="getMetrics"/>
    </bean>
</beans>