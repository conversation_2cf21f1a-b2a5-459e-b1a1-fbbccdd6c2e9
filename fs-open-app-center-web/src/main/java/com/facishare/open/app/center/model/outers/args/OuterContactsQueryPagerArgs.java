package com.facishare.open.app.center.model.outers.args;

import java.io.Serializable;
import java.util.List;

/**
 * 外部联系人分页查询参数对象.
 * Created by zenglb on 2016/11/8.
 */
public class OuterContactsQueryPagerArgs implements Serializable {

    /**
     * 应用id
     */
    private String appId;
    /**
     * 服务专员的id.
     */
    private Integer userId;
    /**
     * 查询名称的字符串.
     */
    private String searchText;
    /**
     * 当前页.
     */
    private Integer currentPage = 1;
    /**
     * 页大小.
     */
    private Integer pageSize = 10;

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getSearchText() {
        return searchText;
    }

    public void setSearchText(String searchText) {
        this.searchText = searchText;
    }

    public Integer getCurrentPage() {
        return currentPage;
    }

    public void setCurrentPage(Integer currentPage) {
        this.currentPage = currentPage;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    @Override
    public String toString() {
        return "OuterContactsQueryPagerArgs{" +
                "appId='" + appId + '\'' +
                ", userId=" + userId +
                ", searchText='" + searchText + '\'' +
                ", currentPage=" + currentPage +
                ", pageSize=" + pageSize +
                '}';
    }
}
