package com.facishare.open.app.center.controller;

import com.facishare.converter.EIEAConverter;
import com.facishare.enterprise.common.result.Result;
import com.facishare.open.app.center.ajax.code.AjaxCode;
import com.facishare.open.app.center.ajax.result.AjaxResult;
import com.facishare.open.app.center.ajax.result.AppPagerAjaxResult;
import com.facishare.open.app.center.api.constants.CommonConstant;
import com.facishare.open.app.center.api.model.OpenAppDO;
import com.facishare.open.app.center.api.model.enums.AppCenterEnum;
import com.facishare.open.app.center.api.model.enums.IconType;
import com.facishare.open.app.center.api.model.property.OpenAppProperties;
import com.facishare.open.app.center.api.result.AppListResult;
import com.facishare.open.app.center.api.result.AppResult;
import com.facishare.open.app.center.api.result.BaseResult;
import com.facishare.open.app.center.api.result.StatusResult;
import com.facishare.open.app.center.api.service.OpenAppAdminService;
import com.facishare.open.app.center.common.BaseController;
import com.facishare.open.app.center.manager.AppBindManager;
import com.facishare.open.app.center.manager.AppIconManager;
import com.facishare.open.app.center.manager.AppManager;
import com.facishare.open.app.center.manager.PollingService;
import com.facishare.open.app.center.model.AppAuthorizeParam;
import com.facishare.open.app.center.model.QueryAppsParam;
import com.facishare.open.app.center.model.UpdateAppCrmAuthParam;
import com.facishare.open.app.center.utils.*;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.exception.BizException;
import com.facishare.open.common.storage.mysql.dao.Pager;
import com.facishare.open.material.api.service.MessageCommentService;
import com.facishare.open.msg.constant.CustomerServiceMsgType;
import com.facishare.open.msg.model.QueryUnReadUserSessionVO;
import com.facishare.open.msg.model.ResetUnReadUserSessionVO;
import com.facishare.open.msg.result.MessageExhibitionResult;
import com.facishare.open.msg.service.MessageExhibitionService;
import com.facishare.open.oauth.model.enums.AppTypeEnum;
import com.facishare.open.oauth.result.CommonResult;
import com.facishare.open.oauth.service.AppEaAuthService;
import com.fxiaoke.enterpriserelation.arg.IsCustomerLinkOrWxServiceOrAppManagerArg;
import com.fxiaoke.enterpriserelation.common.HeaderObj;
import com.fxiaoke.enterpriserelation.common.RestResult;
import com.fxiaoke.enterpriserelation.result.UserPositionResult;
import com.fxiaoke.enterpriserelation.service.UpstreamEmployeeCustomerAppRoleService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.facishare.open.app.center.api.model.enums.AppCenterEnum.AppType.DEV_APP;

/**
 * 企业与第三方应用的绑定关系.
 *
 * <AUTHOR>
 *         2015年8月3日
 */
@Controller
@RequestMapping("/open/appcenter/app/bind")
public class AppBindController extends BaseController {

    private static final int BIND_STATUS_ENABLE = 1;

    @Resource
    private AppEaAuthService appEaAuthService;
    @Resource
    private OpenAppAdminService openAppAdminService;
    @Resource
    private MessageExhibitionService messageExhibitionService;
    @Resource
    private AppBindManager appBindManager;
    @Resource
    private MessageCommentService messageCommentService;
    @Resource
    private AppManager appManager;
    @Autowired
    private UpstreamEmployeeCustomerAppRoleService upstreamEmployeeCustomerAppRoleService;
    @Resource
    private AppIconManager appIconManager;

    @Resource
    private EIEAConverter eieaConverter;

    @Value("${fs.open.app.center.AppBindController.nearbyCustomersAppId}")
    private String nearbyCustomersAppId;

    private List<String> officialDevIds;
    @Resource
    private PollingService pollingService;

    @Value("${fs.open.app.center.app.official.dev.ids}")
    public void setOfficialDevIds(String officialDevIds) {
        this.officialDevIds = Arrays.asList(officialDevIds.split(","));
    }

//    /**
//     * 查询当前企业管理员可操作的应用列表(已授权的第三方应用).
//     *
//     * @param user 操作者.
//     * @return 可操作的应用列表
//     */
//    @RequestMapping("/queryAppListByFsAdmin")
//    @ResponseBody
//    @Deprecated
//    public AppListAjaxResult queryAppListByFsAdmin(@ModelAttribute FsUserVO user) {
//        //1.验证是管理员
//        checkFsAdmin(user);
//
//        //2.获取企业已经授权的应用（应用list根据应用创建的时间倒序）
//        List<OpenAppDO> appDOs = appBindManager.queryAppsByFsAdmin(user);
//
//        List<Map<String, Object>> resultAppMaps = appDOs.stream().filter((obj) -> !nearbyCustomersAppId.equals(obj.getAppId()))
//                .map(this::appDO2MapResult).collect(Collectors.toList());
//
//        return new AppListAjaxResult(resultAppMaps);
//    }

//    /**
//     * 查询当前应用管理员可操作的应用列表.
//     *
//     * @param user 操作者.
//     * @return 可操作的应用列表
//     */
//    @RequestMapping("/queryAppListByFsAppAdmin")
//    @ResponseBody
//    @Deprecated
//    public AjaxResult queryAppListByFsAppAdmin(@ModelAttribute FsUserVO user) {
//        //验证是否为应用管理员.
//        checkAppAdmin(user);//验证应用管理员.
//
//        List<OpenAppDO> appDOs = appBindManager.queryAppsByFsAppAdmin(user);
//
//        final Map<String, Boolean> appUnReadStatusMap = getUnReadStatusForApps(user.getEnterpriseAccount(), appDOs);
//
//        //封装数据供前端使用
//        List<Map<String, Object>> resultAppMaps = appDOs.stream()
//                .filter((obj) -> !nearbyCustomersAppId.equals(obj.getAppId()))
//                .map(obj -> appDO2MapResult(obj, appUnReadStatusMap)).collect(Collectors.toList());
//        return new AjaxResult(resultAppMaps);
//    }

    /**
     * 查询当前服务号管理员可操作的服务号列表.
     *
     * @param user 操作者.
     * @return 可操作的应用列表
     */
    @RequestMapping("/queryServiceListByFsAppAdmin")
    @ResponseBody
    public AjaxResult queryServiceListByFsAppAdmin(@ModelAttribute FsUserVO user) {
        List<OpenAppDO> appDOs = appBindManager.queryServicesByFsAppAdmin(user, OpenAppUtils.getAllAppTypesForService());

        final Map<String, Boolean> appUnReadStatusMap = getUnReadStatusForApps(user.getEnterpriseAccount(), appDOs);

        //是否有未读文章评论
        final Map<String, Boolean> hasUnreadCommentMap = getHasUnreadCommentForApps(user.getEnterpriseAccount(), appDOs);

        //封装数据供前端使用
        List<Map<String, Object>> resultAppMaps = appDOs.stream()
                .filter((obj) -> !nearbyCustomersAppId.equals(obj.getAppId()))
                .map(obj -> appDO2MapResult(obj, appUnReadStatusMap, hasUnreadCommentMap)).collect(Collectors.toList());
        return new AjaxResult(resultAppMaps);
    }

    /**
     * 管理员添加应用（后来改了，普通用户也可以添加）
     * 会默认自动设置当前系统管理员为应用管理员,并默认设置可见范围为全公司.
     *
     * @param user    操作者.
     * @param appForm 添加应用的应用信息
     * @return 添加是否成功
     */
    @RequestMapping("/addAppByFsAdmin")
    @ResponseBody
    public AjaxResult addAppByFsAdmin(@ModelAttribute FsUserVO user, @RequestBody AppAuthorizeParam appForm) {
        checkParamNotBlank(appForm, "请填写表单"); // ignoreI18n
        checkParamNotBlank(appForm.getAppId(), "请选择要添加的应用"); // ignoreI18n

        //新的版本购买方案：不允许用户自己添加/试用/购买应用
        return new AjaxResult(appBindManager.addAppByUser(user, appForm.getAppId(), appForm.getAppAdmins(),
                appForm.getComponents()));
    }

    /**
     * 应用启用/停用.
     *
     * @param user   操作者.
     * @param appId  应用id
     * @param status 1启用, 2停用
     * @return 处理结果
     */
    @RequestMapping("/updateAppBindOnOff")
    @ResponseBody
    public AjaxResult updateAppBindOnOff(@ModelAttribute FsUserVO user,
                                         @RequestParam(value = "appId", required = false) String appId,
                                         @RequestParam(value = "status", required = false) Integer status) {
        checkParamNotBlank(appId, "请选择要设置的应用"); // ignoreI18n
        checkParamRegex("" + status, "[12]{1}", "请填写有效的状态"); // ignoreI18n

        // 判断用户与应用是否同一个企业
        OpenAppDO openAppDO = appManager.loadAppBrief(appId);

        if (!isFsAdmin(user) && !hasAppManageFunction(user, openAppDO) && !isAppAdmin(user, appId)) {
            return new AjaxResult(AjaxCode.NO_AUTHORITY,"权限不足"); // ignoreI18n
        }

        checkBindOff(appId, status);

        String enterpriseAccount = user.getEnterpriseAccount();
        //1.启动，停用操作
        StatusResult statusResult
                = openFsUserBindAppService.updateAppBindOnOff(user, appId, enterpriseAccount, status);
        if (!statusResult.isSuccess()) {
            logger.warn("updateAppBindOnOff failed,user[{}] appId[{}],status[{}],result[{}]", user, appId, status, statusResult);
            return new AjaxResult(AjaxCode.BIZ_EXCEPTION, statusResult.getErrDescription());
        }
        //将启用禁用信息保存到paasApp
        //885灰度 ea
        if (GraySwitch.isAllowForEa(GraySwitch.syncAppStatus, user.getEa())){
            appManager.saveApplicationIfToPaas(user, appId, status);
            //发送polling变更通知
            pollingService.notifyQixinChange(user.getEnterpriseAccount());

        }


        return SUCCESS;
    }

    private void checkBindOff(String appId, Integer status) {
        if (CommonConstant.APP_BIND_STATUS_OFF == status) {
            if (StringUtils.isNotBlank(ConfigCenter.NOT_ALLOW_BIND_OFF_APP_IDS)) {
                boolean notAllowBindOff = Stream.of(ConfigCenter.NOT_ALLOW_BIND_OFF_APP_IDS.split(",")).anyMatch(appId::equals);
                if (notAllowBindOff) {
                    throw new BizException(AjaxCode.BIZ_EXCEPTION, "app not allow to be bound off");
                }
            }
        }
    }


    /**
     * 自建应用CRM接口授权用/停用.
     */
    @RequestMapping("/updateAppCrmAuthOnOff")
    @ResponseBody
    public AjaxResult updateAppCrmAuthOnOff(@ModelAttribute FsUserVO user,
                                            @RequestBody UpdateAppCrmAuthParam param) {
        checkParamNotBlank(param.getAppId(), "请选择要设置的应用"); // ignoreI18n
        checkParamNotBlank(param.getStatus(), "请选择要设置的状态"); // ignoreI18n

        // 判断用户与应用是否同一个企业
        OpenAppDO openAppDO = appManager.loadAppBrief(param.getAppId());

        if (!Objects.equals(openAppDO.getAppType(), AppCenterEnum.AppType.CUSTOM_APP.value())) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "非自建应用，无法操作"); // ignoreI18n
        }

        if (!isFsAdmin(user) && !hasAppManageFunction(user, openAppDO) && !isAppAdmin(user, param.getAppId())) {
            return new AjaxResult(AjaxCode.NO_AUTHORITY,"权限不足"); // ignoreI18n
        }

        if ("on".equals(param.getStatus())) {
            try {
                CommonResult commonResult = appEaAuthService.saveAppEa(null, null, param.getAppId(), user.getEa(), Lists.newArrayList("app-crm-mgr-group"));
                logger.info("appEaAuthService.saveAppEa appId[{}], user[{}], commonResult[{}]", param.getAppId(), user, commonResult);
                if (!commonResult.isSuccess()) {
                    logger.warn("appEaAuthService.saveAppEa failed appId[{}], user[{}], commonResult[{}]", param.getAppId(), user, commonResult);
                    throw new BizException(AjaxCode.BIZ_EXCEPTION, "开启失败" + commonResult.getErrMessage()); // ignoreI18n
                }
            } catch (Exception e) {
                logger.warn("appEaAuthService.saveAppEa failed appId[{}], user[{}]", param.getAppId(), user, e);
                throw new BizException(AjaxCode.BIZ_EXCEPTION, "开启失败"); // ignoreI18n
            }
        } else if ("off".equals(param.getStatus())) {
            try {
                CommonResult commonResult = appEaAuthService.deleteAppEa(null, null, param.getAppId(), user.getEa(), Lists.newArrayList("app-crm-mgr-group"));
                logger.info("appEaAuthService.deleteAppEa appId[{}], user[{}], commonResult[{}]", param.getAppId(), user, commonResult);
                if (!commonResult.isSuccess()) {
                    logger.warn("appEaAuthService.deleteAppEa failed appId[{}], user[{}], commonResult[{}]", param.getAppId(), user, commonResult);
                    throw new BizException(AjaxCode.BIZ_EXCEPTION, "取消失败" + commonResult.getErrMessage()); // ignoreI18n
                }
            } catch (Exception e) {
                logger.warn("appEaAuthService.saveAppEa failed appId[{}], user[{}]", param.getAppId(), user, e);
                throw new BizException(AjaxCode.BIZ_EXCEPTION, "取消失败"); // ignoreI18n
            }
        } else {
            throw new BizException(AjaxCode.PARAM_ERROR, "status错误"); // ignoreI18n
        }

        return SUCCESS;
    }

    /**
     * 应用取消授权.
     *
     * @param user  操作者.
     * @param appId 应用id
     * @return 处理是否成功
     */
    @RequestMapping("/updateAppBindCancelAuth")
    @ResponseBody
    public AjaxResult updateAppBindCancelAuth(@ModelAttribute FsUserVO user,
                                              @RequestParam(value = "appId", required = false) String appId) {
        checkParamNotBlank(appId, "请选择要设置的应用"); // ignoreI18n
        //验证系统管理员
        checkFsAdmin(user);

        final String fsEa = user.getEnterpriseAccount();
        // 取消授权
        StatusResult statusResult = openFsUserBindAppService.saveFsUserBindAppCancelAuth(user, appId, fsEa);
        if (!statusResult.isSuccess()) {
            logger.error("app cancel auth error!,user[" + user + "] appId [ " + appId + " ] result ["
                    + statusResult.getErrCode() + " : " + statusResult.getErrDescription() + "]");
            return new AjaxResult(AjaxCode.BIZ_EXCEPTION, statusResult.getErrDescription());
        }
        return SUCCESS;
    }

    /**
     * 查询登录用户的身份态:是否是管理员/应用管理员.
     *
     * @param user 操作者.
     * @return {@code data: {isAppAdmin: 1, isFsAdmin: 0, isServiceAdmin: 1, hasWorkbenchServices: 1} }
     */
    @RequestMapping("/queryIdentity")
    @ResponseBody
    public AjaxResult queryIdentity(@ModelAttribute FsUserVO user) {
        boolean isAppAdmin = isAppAdmin(user);
        boolean isServiceAdmin = isServiceAdmin(user);
        boolean isFsAdmin = isFsAdmin(user);
        boolean isLinkAdmin = webAuthManager.isLinkAdmin(user);
        boolean isCrmAdmin = webAuthManager.isCrmAdmin(user);
        boolean hasServiceDashboard = appBindManager.hasServiceDashboard(user);
        boolean isUpstream = linkServiceManager.isUpstream(user);
        UserPositionResult linkAdmin = linkServiceManager.getUserPosition(user);//调用平台组接口
        boolean isUpstreamLinkAdmin = linkAdmin.getIsUpEaConnAdmin();//是否上游企业互联管理员
        boolean isDownstreamLinkAdmin = linkAdmin.getIsDownEaConnAdmin();//是否下游游企业互联管理员
        boolean isLinkServiceAdmin = linkServiceManager.isLinkServiceAdmin(user);
        boolean isWxServiceAdmin = isWxServiceAdmin(user); //是否外联服务号管理员
        //外联服务号 用户是否有企业互联子频道(除互联服务号之外)使用权限
        boolean isLinkSubChannelDisplay = linkServiceManager.isLinkSubChannelDisplay(user.getEnterpriseAccount(), user.getUserId());
        // 是否客户互联/客户互联服务号/客户互联应用管理员
        boolean isCustomerLinkOrWxServiceOrAppManager = isCustomerLinkOrWxServiceOrAppManager(user);

        Map<String, Object> resultData = Maps.newHashMap();
        resultData.put("isFsAdmin", isFsAdmin ? 1 : 0);//系统管理员
        resultData.put("isLinkAdmin", isLinkAdmin ? 1 : 0);//互联管理员
        resultData.put("isAppAdmin", isAppAdmin ? 1 : 0);//应用管理员
        resultData.put("isServiceAdmin", isServiceAdmin ? 1 : 0);//服务号管理员(内部服务号管理员)
        resultData.put("isCrmAdmin", isCrmAdmin ? 1 : 0);//是否CRM管理员
        resultData.put("isWxServiceAdmin", isWxServiceAdmin ? 1 : 0);//是否外联服务号管理员
        resultData.put("hasServiceDashboard", hasServiceDashboard ? 1 : 0);
        resultData.put("isUpstream", isUpstream ? 1 : 0);//是否上游企业
        resultData.put("isUpstreamLinkAdmin", isUpstreamLinkAdmin ? 1 : 0);//是否上游企业互联管理员
        resultData.put("isDownstreamLinkAdmin", isDownstreamLinkAdmin ? 1 : 0);//是否下游企业互联管理员
        resultData.put("isLinkServiceAdmin", isLinkServiceAdmin ? 1 : 0);//是否互联服务号管理员
        resultData.put("isLinkSubChannelDisplay", isLinkSubChannelDisplay ? 1 : 0);//是否企业互联子频道显示
        resultData.put("isCustomerLinkOrWxServiceOrAppManager", isCustomerLinkOrWxServiceOrAppManager ? 1 : 0);
        return new AjaxResult(resultData);
    }

    /**
     * 是否客户互联/客户互联服务号/客户互联应用管理员
     */
    private boolean isCustomerLinkOrWxServiceOrAppManager(FsUserVO fsUserVO) {
        try {
            HeaderObj headerObj = HeaderObj.newInstance(fsUserVO.getEnterpriseAccount(), null, null, null);
            RestResult<Boolean> result = upstreamEmployeeCustomerAppRoleService.isCustomerLinkOrWxServiceOrAppManager(headerObj, IsCustomerLinkOrWxServiceOrAppManagerArg.builder().ea(fsUserVO.getEnterpriseAccount()).fsUserId(fsUserVO.getUserId()).build());
            if (!result.isSuccess()) {
                logger.warn("call upstreamEmployeeCustomerAppRoleService.isCustomerLinkOrWxServiceOrAppManager error. ea[{}], userId[{}], result[{}]",
                        fsUserVO.getEnterpriseAccount(), fsUserVO.getUserId(), result);
                return false;
            }
            return result.getData();
        } catch (Exception e) {
            logger.warn("call upstreamEmployeeCustomerAppRoleService.isCustomerLinkOrWxServiceOrAppManager error. ea[{}], userId[{}]",
                    fsUserVO.getEnterpriseAccount(), fsUserVO.getUserId(), e);
            return false;
        }

    }

    /**
     * appDO转变为应用列表中应用的属性map.
     *
     * @param appDO 应用DO
     * @return 应用属性的map
     */
    private Map<String, Object> appDO2MapResult(final OpenAppDO appDO) {
        Map<String, Object> appMap = new HashMap<>();
        appMap.put("appId", appDO.getAppId());
        appMap.put("appName", BizCommonUtils.getAppComponentName(appDO.getAppName(),appDO.getAppId()));
        appMap.put("appType", appDO.getAppType());
        //add by lambo@******** 自定义应用显示简介，其它应用显示一句话描述.
        appMap.put("appDesc", appDO.getAppDesc());
        appMap.put("payType", appDO.getPayType());
        OpenAppProperties openAppProperties = OpenAppProperties.fromJson(appDO.getProperties());
        switch (AppCenterEnum.AppType.from(appDO.getAppType())) {
            case BASE_APP: case DEV_APP:
                if (openAppProperties != null) {
                    appMap.put("appDesc", openAppProperties.getAppIntro());
                }
                break;
            case BASE_SERVICE_APP: case EXT_SERVICE_APP:
                if (openAppProperties != null) {
                    appMap.put("isRecommended", openAppProperties.getIsRecommendedApp());
                }
                break;
            default:
                break;
        }
        appMap.put("bindStatus", appDO.getBindStatus());
        appMap.put("appLogoUrl", appIconManager.queryAppIconUrl(appDO.getAppId(), IconType.WEB));
        appMap.put("appLabel", loadAppLabel(appDO));
        return appMap;
    }

    private Map<String, Object> appDO2MapResult(final OpenAppDO appDO, final Map<String, Boolean> appUnReadStatusMap) {
        Map<String, Object> appMap = appDO2MapResult(appDO);
        appMap.put("appUnReadStatus", appUnReadStatusMap.getOrDefault(appDO.getAppId(), false));
        if (appDO.getAppType() == AppCenterEnum.AppType.OUT_SERVICE_APP.value()){
            appMap.put("appUnReadStatus", Boolean.FALSE);
        }
        return appMap;
    }

    private Map<String, Object> appDO2MapResult(final OpenAppDO appDO, final Map<String, Boolean> appUnReadStatusMap, Map<String, Boolean> hasUnreadCommentMap) {
        Map<String, Object> appMap = appDO2MapResult(appDO);

        boolean appUnReadStatus = appUnReadStatusMap.getOrDefault(appDO.getAppId(), false);
        boolean hasUnreadCommentStatus = hasUnreadCommentMap.getOrDefault(appDO.getAppId(), false);

        appMap.put("appUnReadStatus", (appUnReadStatus || hasUnreadCommentStatus));  //有未读的"用户消息" / 未读的"文章评论"
        if (appDO.getAppType() == AppCenterEnum.AppType.OUT_SERVICE_APP.value()){
            appMap.put("appUnReadStatus", Boolean.FALSE);
        }
        return appMap;
    }

    private Integer loadAppLabel(OpenAppDO appDO) {
        if (AppCenterEnum.AppType.CUSTOM_APP.value() == appDO.getAppType()
                || AppCenterEnum.AppType.SERVICE.value() == appDO.getAppType()
                || AppCenterEnum.AppType.LINK_SERVICE.value() == appDO.getAppType()) {
            return 1;//自建
        } else if (AppCenterEnum.AppType.BASE_APP.value() == appDO.getAppType()) {
            return 3;//官方
        } else if (DEV_APP.value() == appDO.getAppType()) {
            if (officialDevIds.contains(appDO.getAppCreater())) {
                return 3;//官方
            }
            return 2;//第三方
        }
        return 2;//第三方
    }

    /**
     * 查询可管理的服务号，用于服务号管理
     *
     * @param user        用户.
     * @param currentPage 当前页
     * @param pageSize    页大小.默认为10.
     * @return
     */
    @RequestMapping("/queryServiceListByAdmin")
    @ResponseBody
    public AppPagerAjaxResult queryServiceListByAdmin(@ModelAttribute FsUserVO user,
                                              @RequestParam(value = "currentPage", defaultValue = "1") Integer currentPage,
                                              @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                              @RequestParam(value = "serviceType", defaultValue = "0") Integer serviceType) {

        checkParamNotBlank(serviceType, "serviceType 不能为空"); // ignoreI18n

        boolean isFsAdmin;
        boolean isServiceAdmin;
        boolean isUpstreamLinkAdmin;
        boolean linkServiceAdmin;
        boolean isWxServiceAdmin;
        Pager<OpenAppDO> appDoPager;
        if (Objects.equals(serviceType, AppCenterEnum.AppType.LINK_SERVICE.value())) {
            //外联服务号1 是否上游企业互联管理员  调用平台组接口
            isUpstreamLinkAdmin = linkServiceManager.isUpstreamLinkAdmin(user);
            linkServiceAdmin = linkServiceManager.isLinkServiceAdmin(user);
            if (!isUpstreamLinkAdmin && !linkServiceAdmin) {
                return new AppPagerAjaxResult(AjaxCode.NO_AUTHORITY, "不是上游的企业互联管理员也不是互联服务号管理员"); // ignoreI18n
            }
            appDoPager = appBindManager.queryServiceListByAdmin(user, currentPage, pageSize, isUpstreamLinkAdmin, linkServiceAdmin, serviceType);
        } else if (Objects.equals(serviceType, AppCenterEnum.AppType.OUT_SERVICE_APP.value())) {
            isWxServiceAdmin = isWxServiceAdmin(user);
            boolean isLinkAdmin = webAuthManager.isLinkAdmin(user);
            if (!isLinkAdmin && !isWxServiceAdmin) {
                return new AppPagerAjaxResult(AjaxCode.NO_AUTHORITY, "不是互联管理员也不是外联服务号管理员"); // ignoreI18n
            }
            appDoPager = appBindManager.queryServiceListByAdmin(user, currentPage, pageSize, isLinkAdmin,
                    isWxServiceAdmin, serviceType);
        } else {
            isFsAdmin = isFsAdmin(user);
            isServiceAdmin = isServiceAdmin(user);
            if (!isFsAdmin && !isServiceAdmin) {
                return new AppPagerAjaxResult(AjaxCode.NO_AUTHORITY, "不是企业管理员也不是服务号管理员"); // ignoreI18n
            }
            appDoPager = appBindManager.queryServiceListByAdmin(user, currentPage, pageSize, isFsAdmin , isServiceAdmin, serviceType);
        }
        List<Map<String, Object>> resultAppMaps = appDoPager.getData().stream()
                .map(this::appDO2MapResult).collect(Collectors.toList());

        Pager<Map<String, Object>> result = new Pager<>();
        result.setCurrentPage(currentPage);
        result.setPageSize(pageSize);
        result.setRecordSize(appDoPager.getRecordSize());
        result.setData(resultAppMaps);
        return new AppPagerAjaxResult(result);
    }

    /**
     * 根据服务号名称查询服务号列表（用于服务号管理页面的服务号查询功能）
     * @param user
     * @param appName
     * @return
     */
    @RequestMapping("/queryServiceListByAppName")
    @ResponseBody
    public AjaxResult queryServiceListByAppName(@ModelAttribute FsUserVO user,
                                            @RequestParam(value = "appName", required = false) String appName) {
        boolean isFsAdmin = isFsAdmin(user);
        boolean isServiceAdmin = isServiceAdmin(user);
        boolean isUpstreamLinkAdmin = linkServiceManager.isUpstreamLinkAdmin(user);
        boolean isLinkServiceAdmin = linkServiceManager.isLinkServiceAdmin(user);

        if (!isFsAdmin && !isServiceAdmin && !isUpstreamLinkAdmin && !isLinkServiceAdmin) {
            return new AjaxResult(AjaxCode.NO_AUTHORITY, "无权限"); // ignoreI18n
        }

        List<OpenAppDO> appDoList =  appBindManager.queryServiceListByAppName(user, appName, isFsAdmin, isServiceAdmin, isUpstreamLinkAdmin, isLinkServiceAdmin);
        List<Map<String, Object>> resultAppMaps =
                appDoList.stream().map(this::appDO2MapResult).collect(Collectors.toList());
        return new AjaxResult(resultAppMaps);
    }

    /**
     * 根据应用名称查询应用列表（用于应用管理页面的应用查询功能）
     * @param user
     * @param appName
     * @return
     */
    @RequestMapping("/queryAppListByAppName")
    @ResponseBody
    public AjaxResult queryAppListByAppName(@ModelAttribute FsUserVO user,
                                          @RequestParam(value = "appName", required = false) String appName) {
        boolean isFsAdmin = isFsAdmin(user);
        boolean isAppAdmin = isAppAdmin(user);
        if (!isFsAdmin && !isAppAdmin) {
            return new AjaxResult(AjaxCode.NO_AUTHORITY, "不是企业管理员也不是应用管理员"); // ignoreI18n
        }
        List<OpenAppDO> appDoList =  appBindManager.queryAppListByAppName(user, appName, isFsAdmin, isAppAdmin);
        List<Map<String, Object>> resultAppMaps =
                appDoList.stream().map(this::appDO2MapResult).collect(Collectors.toList());
        return new AjaxResult(resultAppMaps);
    }

    /**
     * 查询可管理的应用，用于应用管理，（系统管理员或应用管理员可用）.add by lambo @20160120.
     *
     * @param user        用户.
     * @param currentPage 当前页
     * @param pageSize    页大小.默认为10.
     * @return AjaxResult.
     */
    @RequestMapping("/queryAppListByAdmin")
    @ResponseBody
    public AppPagerAjaxResult queryAppListByAdmin(@ModelAttribute FsUserVO user, @ModelAttribute String lang,
                                          @RequestParam(value = "currentPage", defaultValue = "1") Integer currentPage,
                                          @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        boolean isFsAdmin = isFsAdmin(user);
        boolean isAppAdmin = isAppAdmin(user);
        if (!isFsAdmin && !isAppAdmin) {
            return new AppPagerAjaxResult(AjaxCode.NO_AUTHORITY, "不是企业管理员也不是应用管理员"); // ignoreI18n
        }

        //查询可管理的应用，用于应用管理
        Pager<OpenAppDO> appDoPager = appBindManager.queryAppListByAdmin(user, currentPage, pageSize, isFsAdmin, isAppAdmin);

        int ei = eieaConverter.enterpriseAccountToId(user.getEa());
        appDoPager.setData(I18NUtils.modifyOpenAppDOByLang(ei, appDoPager.getData(), lang));

        List<String> appIds = null;
        //查询用户作为应用管理员的所有应用id列表(不包括服务号)
        BaseResult<List<String>> listBaseResult = openAppAdminService.queryAppIdList(user.asStringUser());
        if (listBaseResult.isSuccess()) {
            appIds = listBaseResult.getResult();
        }

        List<Map<String, Object>> resultAppMaps;
        final List<String> finalAppIds = appIds;
        resultAppMaps = appDoPager.getData().stream()
                .map(this::appDO2MapResult).map(map -> {
                    String appId = (String) map.get("appId");
                    map.put("isThisAppAdmin", CommonConstant.NO);
                    if (null != finalAppIds && finalAppIds.contains(appId)) {
                        map.put("isThisAppAdmin", CommonConstant.YES);
                    }
                    return map;
                }).collect(Collectors.toList());

        Pager<Map<String, Object>> result = new Pager<>();
        result.setCurrentPage(currentPage);
        result.setPageSize(pageSize);
        result.setRecordSize(appDoPager.getRecordSize());
        result.setData(resultAppMaps);
        return new AppPagerAjaxResult(result);
    }

    /**
     * 740 应用管理后台升级后废弃，具体见：http://wiki.firstshare.cn/pages/viewpage.action?pageId=135686031
     *
     * 新管理后台拉取应用列表
     *    拉基础应用和扩展应用  appTypes=[2, 3]
     *    拉自建应用           appTypes=[1]
     * @deprecated
     */
    @RequestMapping("/queryApps")
    @ResponseBody
    @Deprecated
    public AjaxResult queryApps(@ModelAttribute FsUserVO user, @ModelAttribute String lang,
                                @RequestBody QueryAppsParam param) {
        checkParamNotBlank(param.getAppTypes(), "appTypes不能为空"); // ignoreI18n
        if (CollectionUtils.isEmpty(param.getAppTypes())) {
            throw new BizException(AjaxCode.PARAM_ERROR, "appTypes不能为空"); // ignoreI18n
        }
        List<Integer> appTypes = Lists.newArrayList(AppCenterEnum.AppType.CUSTOM_APP.value(), AppCenterEnum.AppType.BASE_APP.value(), AppCenterEnum.AppType.DEV_APP.value());
        if (!appTypes.containsAll(param.getAppTypes())) {
            throw new BizException(AjaxCode.PARAM_ERROR, "appTypes包含非法类型"); // ignoreI18n
        }

        //权限
        if (param.getAppTypes().contains(AppCenterEnum.AppType.CUSTOM_APP.value())) {
            if (!isAppAdmin(user) && !hasAppManageFunction(user, AppCenterEnum.AppType.CUSTOM_APP.value())) {
                throw new BizException(AjaxCode.NO_AUTHORITY, "没有权限"); // ignoreI18n
            }
        }
        if (param.getAppTypes().contains(AppCenterEnum.AppType.BASE_APP.value())
                || param.getAppTypes().contains(AppCenterEnum.AppType.DEV_APP.value())) {
            if (!isAppAdmin(user) && !hasAppManageFunction(user, AppCenterEnum.AppType.BASE_APP.value())) {
                throw new BizException(AjaxCode.NO_AUTHORITY, "没有权限"); // ignoreI18n
            }
        }

        Map<String, Object> result = new HashMap<>();
        result.put("appList", appBindManager.queryApps(user, lang, param.getAppTypes()));
        return new AjaxResult(result);
    }

    private Map<String, Boolean> getUnReadStatusForApps(String ea, List<OpenAppDO> appDOs) {
        Map<String, Boolean> result = Maps.newHashMap();

        List<String> appIds = appDOs
                .stream()
                .filter(appDO -> !nearbyCustomersAppId.equals(appDO.getAppId())
                        && appDO.getBindStatus() == CommonConstant.APP_BIND_STATUS_ON).map(appDO -> appDO.getAppId())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(appIds)) {
            return result;
        }

        //互联服务号处理
        MessageExhibitionResult<Map<String, Boolean>> unReadStatusResult = queryUnReadUserSessions(ea, appIds);
        if (unReadStatusResult.isSuccess() && !unReadStatusResult.getData().isEmpty()) {
            result.putAll(unReadStatusResult.getData());
        }
        return result;
    }

    /**
     * 是否有未读文章评论
     *
     * @param ea
     * @param appDOs
     * @return
     */
    private Map<String, Boolean> getHasUnreadCommentForApps(String ea, List<OpenAppDO> appDOs) {
        Map<String, Boolean> result = Maps.newHashMap();

        List<String> appIds = appDOs
                .stream()
                .filter(appDO -> !nearbyCustomersAppId.equals(appDO.getAppId())
                        && appDO.getBindStatus() == CommonConstant.APP_BIND_STATUS_ON).map(appDO -> appDO.getAppId())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(appIds)) {
            return result;
        }
        try {
            com.facishare.open.common.result.BaseResult<Map<String, Boolean>> hasUnreadCommentsResult = messageCommentService.hasUnreadComments(appIds, ea);
            if (hasUnreadCommentsResult.isSuccess() && !hasUnreadCommentsResult.getResult().isEmpty()) {
                result.putAll(hasUnreadCommentsResult.getResult());
            }
        } catch (Exception e) {
            logger.error("hasUnreadComments failed, appDOs[{}], ea[{}]", appDOs, ea, e);
        }

        return result;
    }

    /**
     * 重置应用的id,列表.
     *
     * @param user        用户.
     * @param appId 应用id.
     * @return AjaxResult.
     */
    @RequestMapping("/resetUnReadUserSessions")
    @ResponseBody
    public AjaxResult resetUnReadUserSessions(@ModelAttribute FsUserVO user,
                                                  @RequestParam(value = "appId", required = false) String appId) {
        checkParamNotBlank(appId, "请选择要设置的应用"); // ignoreI18n
        boolean isFsAdmin = isFsAdmin(user);
        boolean isAppAdmin = isAppAdmin(user, appId);
        if (!isFsAdmin && !isAppAdmin) {
            return new AjaxResult(AjaxCode.NO_AUTHORITY, "不是企业管理员也不是应用管理员"); // ignoreI18n
        }
        try {
            MessageExhibitionResult<Void> resetUnReadUserSessionsResult = resetUnReadUserSessions(user.getEnterpriseAccount(), appId);
            if (!resetUnReadUserSessionsResult.isSuccess()) {
                logger.warn("resetUnReadUserSessions failed, user[{}], appId[{}], result[{}]", user, appId, resetUnReadUserSessionsResult);
                return new AjaxResult(AjaxCode.BIZ_EXCEPTION, "设置失败"); // ignoreI18n
            }
            return SUCCESS;
        } catch (Exception e) {
            logger.error("resetUnReadUserSessions failed, user[{}], appId[{}]", user, appId, e);
        }
        return new AjaxResult(AjaxCode.UNAUTHERIZED_EXCEPTION, "系统异常"); // ignoreI18n
    }

    /**
     * 设置用户消息会话session
     * @param fsEa
     * @param appId
     * @return
     */
    private MessageExhibitionResult<Void> resetUnReadUserSessions(String fsEa, String appId){
        AppResult appResult = openAppService.loadOpenApp(appId);
        if(!appResult.isSuccess()){
            logger.warn("fail to openAppService.loadOpenApp, fsEa={}, appIds={}, result={}", fsEa, appId, appResult);
            throw new BizException(appResult);
        }
        MessageExhibitionResult<Void> resetUnReadUserSessionsResult;
        ResetUnReadUserSessionVO resetUnReadUserSessionVO = new ResetUnReadUserSessionVO();
        resetUnReadUserSessionVO.setUpStreamEa(fsEa);
        resetUnReadUserSessionVO.setAppIds(Lists.newArrayList(appId));
        if(Objects.equals(appResult.getResult().getAppType(), AppCenterEnum.AppType.LINK_SERVICE.value())){
            resetUnReadUserSessionVO.setFilterType(CustomerServiceMsgType.EA_CONN_CUSTOMER_SERVICE);
        } else {
            resetUnReadUserSessionVO.setFilterType(CustomerServiceMsgType.INTENAL_CUSTOMER_SERVICE);
        }
        resetUnReadUserSessionsResult = messageExhibitionService.resetUnReadUserSessions(resetUnReadUserSessionVO);
        return resetUnReadUserSessionsResult;
    }

    /**
     * 查询用户会话session
     * @param fsEa
     * @param appIds
     * @return
     */
    private MessageExhibitionResult<Map<String, Boolean>> queryUnReadUserSessions(String fsEa, List<String> appIds){
        List<String> linkAppIds = Lists.newArrayList();
        List<String> innerAppIds = Lists.newArrayList();
        AppListResult appListResult = openAppService.loadOpenAppByIds(appIds);
        if(!appListResult.isSuccess()){
            logger.warn("fail to openAppService.loadOpenAppByIds, fsEa={}, appIds={}, result={}", fsEa, appIds, appListResult);
            throw new BizException(appListResult);
        }
        List<OpenAppDO> openAppDOs = appListResult.getResult();
        openAppDOs.forEach(openAppDO -> {
            if(Objects.equals(openAppDO.getAppType(), AppCenterEnum.AppType.LINK_SERVICE.value())){
                linkAppIds.add(openAppDO.getAppId());
            } else {
                innerAppIds.add(openAppDO.getAppId());
            }
        });

        Map<String, Boolean> unReadMap = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(linkAppIds)) {
            MessageExhibitionResult<Map<String, Boolean>> unReadStatusLinkResult = buildAndQueryUnReadUserSessions(fsEa, linkAppIds, true);
            unReadMap.putAll(unReadStatusLinkResult.getData());
        }
        if (!CollectionUtils.isEmpty(innerAppIds)) {
            MessageExhibitionResult<Map<String, Boolean>> unReadStatusResult = buildAndQueryUnReadUserSessions(fsEa, innerAppIds, false);
            unReadMap.putAll(unReadStatusResult.getData());
        }

        return new MessageExhibitionResult<>(unReadMap);
    }

    /**
     * 构建并查询用户会话消息
     * @param fsEa
     * @param linkAppIds
     * @param isLinkService
     * @return
     */
    public MessageExhibitionResult<Map<String, Boolean>> buildAndQueryUnReadUserSessions(String fsEa, List<String> linkAppIds,
                                                                                         boolean isLinkService){
        QueryUnReadUserSessionVO queryUnReadUserSessionVO = new QueryUnReadUserSessionVO();
        queryUnReadUserSessionVO.setUpStreamEa(fsEa);
        queryUnReadUserSessionVO.setAppIds(linkAppIds);
        if (isLinkService) {
            queryUnReadUserSessionVO.setFilterType(CustomerServiceMsgType.EA_CONN_CUSTOMER_SERVICE);
        } else {
            queryUnReadUserSessionVO.setFilterType(CustomerServiceMsgType.INTENAL_CUSTOMER_SERVICE);
        }
        MessageExhibitionResult<Map<String, Boolean>> unReadStatusResult = messageExhibitionService.queryUnReadUserSessions(queryUnReadUserSessionVO);
        if(!unReadStatusResult.isSuccess()){
            logger.error("messageExhibitionService.queryUnReadUserSessions error, queryUnReadUserSessionVO=[{}], result=[{}]",
                    queryUnReadUserSessionVO, unReadStatusResult);
            throw new BizException(unReadStatusResult);
        }
        return unReadStatusResult;
    }

    /**
     * 判断是否为外联服务号管理员
     * @param user 用户
     * @return boolean
     */
    public boolean isWxServiceAdmin(FsUserVO user){
        List<Integer> appTypes = new ArrayList<>();
        appTypes.add(AppCenterEnum.AppType.OUT_SERVICE_APP.value());
        BaseResult<Boolean> wxServiceAdmin = openAppAdminService.isServiceAdmin(user.getEnterpriseAccount(), user.getUserId(), appTypes);
        if (!wxServiceAdmin.isSuccess()) {
            logger.warn("isLinkServiceAdmin error, openAppAdminService.isServiceAdmin failed, user[{}], result[{}]", user, wxServiceAdmin);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "权限校验异常"); // ignoreI18n
        }
        return wxServiceAdmin.getResult();
    }
}
