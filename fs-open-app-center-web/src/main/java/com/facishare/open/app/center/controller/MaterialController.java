package com.facishare.open.app.center.controller;

import com.facishare.open.app.center.ajax.code.AjaxCode;
import com.facishare.open.app.center.ajax.result.AjaxResult;
import com.facishare.open.app.center.ajax.result.AppPagerAjaxResult;
import com.facishare.open.app.center.api.result.BaseResult;
import com.facishare.open.app.center.api.service.OpenAppAdminService;
import com.facishare.open.app.center.common.BaseController;
import com.facishare.open.app.center.manager.MaterialManager;
import com.facishare.open.app.center.model.ImageMaterialForm;
import com.facishare.open.app.center.model.ImageTextMaterialForm;
import com.facishare.open.app.center.threadlocal.UserContextHolder;
import com.facishare.open.app.center.utils.ConfigCenter;
import com.facishare.open.app.center.utils.FileCheckUtils;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.exception.BizException;
import com.facishare.open.common.storage.mysql.dao.Pager;
import com.facishare.open.material.api.model.ImageResult;
import com.facishare.open.material.api.model.vo.ImageTextVO;
import com.facishare.training.api.enums.ResourceTypeEnum;
import com.facishare.training.api.out.arg.ResourceAppCenterListArg;
import com.facishare.training.api.out.result.ResourceAppCenterListResult;
import com.facishare.training.api.service.ResourceService;
import com.facishare.training.common.result.PageObject;
import com.facishare.training.common.result.Result;
import com.facishare.training.common.result.ResultCode;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 自定义菜单相关接口
 *
 * <AUTHOR>
 *         2015年12月8日
 */
@Controller
@RequestMapping("/open/appcenter/material")
public class MaterialController extends BaseController {

    @Resource
    private MaterialManager materialManager;
    @Resource
    private ResourceService trainingResourceService;
    @Resource
    private OpenAppAdminService openAppAdminService;

    @RequestMapping("/queryTrainingVideo")
    @ResponseBody
    public AppPagerAjaxResult queryTrainingVideo(@RequestParam(value = "currentPage", defaultValue = "1") Integer currentPage,
                                         @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                         @RequestParam(value = "appId", required = false) String appId,
                                         @RequestParam(value = "name", required = false) String name) {
        checkParamNotBlank(appId, "请选择应用"); // ignoreI18n
        if (currentPage < 1 || pageSize < 1) {
            throw new BizException(AjaxCode.PARAM_ERROR, "参数错误"); // ignoreI18n
        }
        FsUserVO fsUserVO = UserContextHolder.getFsUserVO();
        checkAppAdmin(fsUserVO, appId);

        Pager<ResourceAppCenterListResult> pager = new Pager<>();
        pager.setCurrentPage(currentPage);
        pager.setPageSize(pageSize);

        ResourceAppCenterListArg resourceAppCenterListArg = new ResourceAppCenterListArg();
        resourceAppCenterListArg.setEnterpriseAccount(fsUserVO.getEnterpriseAccount());
        resourceAppCenterListArg.setEmployeeId(fsUserVO.getUserId());
        resourceAppCenterListArg.setMaterialNameCriteria(name);
        resourceAppCenterListArg.setPageNo(currentPage);
        resourceAppCenterListArg.setPageSize(pageSize);
        resourceAppCenterListArg.setType(ResourceTypeEnum.VIDEO_RESOURCE.getType());
        Result<PageObject<ResourceAppCenterListResult>> listForAppCenterResult = trainingResourceService.listForAppCenter(resourceAppCenterListArg);
        if (!listForAppCenterResult.isSuccess()) {
            // 无权限，返回管理员列表用于前端展示
            if (ResultCode.INVALID_USER.getErrorCode() == listForAppCenterResult.getErrorCode()) {
                BaseResult<List<String>> queryAppAdminIdListResult = openAppAdminService.queryAppAdminIdList(fsUserVO.getEnterpriseAccount(), ConfigCenter.TRAINING_ASSISTANT_APP_ID);
                if (!queryAppAdminIdListResult.isSuccess()) {
                    logger.error("trainingResourceService.listForAppCenter error. resourceAppCenterListArg[{}], result[{}]", resourceAppCenterListArg, listForAppCenterResult);
                    throw new BizException(AjaxCode.BIZ_EXCEPTION, queryAppAdminIdListResult, "查询培训助手管理员列表失败"); // ignoreI18n
                }
                AppPagerAjaxResult result = new AppPagerAjaxResult(pager);
                result.setErrCode(AjaxCode.NO_AUTHORITY);
                Map<String, Object> data = new HashMap();
                data.put("trainingAppAdmins", queryAppAdminIdListResult.getResult());
                result.setData(data);
                return result;
            } else {
                logger.error("trainingResourceService.listForAppCenter error. resourceAppCenterListArg[{}], result[{}]", resourceAppCenterListArg, listForAppCenterResult);
                throw new BizException(AjaxCode.BIZ_EXCEPTION, "查询视频列表失败"); // ignoreI18n
            }
        }
        PageObject<ResourceAppCenterListResult> resultPageObject = listForAppCenterResult.getData();
        pager.setRecordSize(resultPageObject.getTotalCount());
        pager.setData(resultPageObject.getResult());
        return new AppPagerAjaxResult(pager);
    }

    /**
     * 分页查询应用的素材。
     *
     * @param user        用户.
     * @param currentPage 当前页
     * @param pageSize    页大小.默认为10.
     * @param appId       应用id.
     * @return AjaxResult.
     */
    @RequestMapping("/queryPageAndAppId")
    @ResponseBody
    public AppPagerAjaxResult queryPageAndAppId(@ModelAttribute FsUserVO user,
                                        @RequestParam(value = "currentPage", defaultValue = "1") Integer currentPage,
                                        @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                        @RequestParam(value = "appId", required = false) String appId) {
        checkParamNotBlank(appId, "请选择应用"); // ignoreI18n
        checkAppAdmin(user, appId);//验证是否为应用管理员.
        Pager<ImageTextVO> materialPager = materialManager.queryPageAndAppId(user, appId, currentPage, pageSize);
        return new AppPagerAjaxResult(materialPager);
    }

    /**
     * 上传图片
     *
     * @param user 操作者.
     * @return 素材id.
     */
    @RequestMapping("/uploadImageAndAppId")
    @ResponseBody
    public AjaxResult uploadImageAndId(@ModelAttribute FsUserVO user, ImageMaterialForm imageMaterialForm) {
        checkParamNotBlank(imageMaterialForm, "请填写表单"); // ignoreI18n
        checkParamNotBlank(imageMaterialForm.getAppId(), "请选择应用"); // ignoreI18n
        checkParamNotBlank(imageMaterialForm.getImageFile(), "请上传图片"); // ignoreI18n
        checkParamTrue(FileCheckUtils.isImageFile(imageMaterialForm.getImageFile().getOriginalFilename()), "图片格式不符合"); // ignoreI18n
        checkParamTrue(imageMaterialForm.getImageFile().getBytes().length > 0, "请上传图片"); // ignoreI18n
        checkParamTrue(imageMaterialForm.getImageFile().getBytes().length <= 5242880, "图片大小不能超过5M。"); // ignoreI18n
        checkAppAdmin(user, imageMaterialForm.getAppId());//验证是否为应用管理员.
        ImageResult imageResult = materialManager.uploadImageAndAppId(user, imageMaterialForm);
        return new AjaxResult(imageResult);
    }

    /**
     * 创建单个素材
     *
     * @param user 操作者.
     * @return 素材id.
     */
    @RequestMapping("/createMaterial")
    @ResponseBody
    public AjaxResult createMaterial(@ModelAttribute FsUserVO user, @RequestBody ImageTextMaterialForm imageTextMaterialForm) {
        checkParamNotBlank(imageTextMaterialForm, "请填写表单"); // ignoreI18n
        checkParamNotBlank(imageTextMaterialForm.getAppId(), "请选择应用"); // ignoreI18n
        checkParamNotBlank(imageTextMaterialForm.getImageTextParams(), "请创建至少一个图文"); // ignoreI18n
        checkParamTrue(imageTextMaterialForm.getImageTextParams().size() > 0, "请创建至少一个图文"); // ignoreI18n
        checkAppAdmin(user, imageTextMaterialForm.getAppId());//验证是否为应用管理员.
        String materialId = materialManager.createMaterial(user, imageTextMaterialForm);
        return new AjaxResult(materialId);
    }

    /**
     * 修改图文消息
     *
     * @param user 操作者.
     * @return 素材id.
     */
    @RequestMapping("/updateMaterial")
    @ResponseBody
    public AjaxResult updateMaterial(@ModelAttribute FsUserVO user, @RequestBody ImageTextMaterialForm imageTextMaterialForm) {
        checkParamNotBlank(imageTextMaterialForm, "请填写表单"); // ignoreI18n
        checkParamNotBlank(imageTextMaterialForm.getAppId(), "请选择应用"); // ignoreI18n
        checkParamNotBlank(imageTextMaterialForm.getMaterialId(), "请选择应用"); // ignoreI18n
        checkParamNotBlank(imageTextMaterialForm.getImageTextParams(), "请创建至少一个图文"); // ignoreI18n
        checkParamTrue(imageTextMaterialForm.getImageTextParams().size() > 0, "请创建至少一个图文"); // ignoreI18n
        checkAppAdmin(user, imageTextMaterialForm.getAppId());//验证是否为应用管理员.
        String materialId = materialManager.updateMaterial(user, imageTextMaterialForm);
        return new AjaxResult(materialId);
    }
    /**
     * 查询单个素材明细
     *
     * @param user 操作者.
     * @return 素材明细.
     */
    @RequestMapping("/loadMaterialById")
    @ResponseBody
    public AjaxResult loadMaterialById(@ModelAttribute FsUserVO user, @RequestParam(value = "appId", required = false) String appId,
                                       @RequestParam(value = "materialId", required = false) String materialId) {
        checkParamNotBlank(materialId, "请选择素材"); // ignoreI18n
        checkParamNotBlank(appId, "请选择应用"); // ignoreI18n
        checkAppAdmin(user, appId);//验证是否为应用管理员.
        return new AjaxResult(materialManager.loadMaterialById(user, appId, materialId));
    }

    /**
     * 删除单个素材明细
     *
     * @param user 操作者.
     * @return 无.
     */
    @RequestMapping("/deleteMaterialById")
    @ResponseBody
    public AjaxResult deleteMaterialById(@ModelAttribute FsUserVO user,
                                         @RequestBody ImageTextMaterialForm imageTextMaterialForm) {
        checkParamNotBlank(imageTextMaterialForm, "请填写表单"); // ignoreI18n
        checkParamNotBlank(imageTextMaterialForm.getAppId(), "请选择应用"); // ignoreI18n
        checkParamNotBlank(imageTextMaterialForm.getMaterialId(), "请选择应用"); // ignoreI18n
        checkAppAdmin(user, imageTextMaterialForm.getAppId());//验证是否为应用管理员.
        materialManager.deleteMaterialById(user, imageTextMaterialForm.getAppId(), imageTextMaterialForm.getMaterialId());
        return SUCCESS;
    }

}
