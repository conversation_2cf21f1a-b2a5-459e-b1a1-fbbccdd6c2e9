package com.facishare.open.app.center.manager;


import com.facishare.open.app.center.cons.ServiceTypeEnum;
import com.facishare.open.app.center.model.*;
import com.facishare.open.autoreplymsg.model.PlatformMetaSessionVO;
import com.facishare.open.common.model.FsUserVO;

import java.util.List;

public interface ServiceNumberManager {
    
    /**
     * 应用开关的停用启用     
     * @param   entity    用户信息
     * @param   user  操作用户
     * @param   isInit 是否创建服务号时初始设置
     * @return Boolean   
     */
    boolean updateServiceNumberOnOff(OpenServiceNumberDO entity, FsUserVO user, String lang, boolean isInit);

    /**
     * 服务号列表缓存失效
     * @param user 用户
     * @param appId 应用ID
     */
    void resetTagByServiceNumberModuleKey(FsUserVO user, String appId);

    /**
     * 更新客服会话session名称和客服人员列表
     * @param user 用户
     * @param appId 应用ID
     * @param serviceName 服务号名称
     */
    void updateCustomServiceRepresentiveListAndSessionName(FsUserVO user, String appId, String serviceName);

    /**
     * 获取服务号列表
     * @param user 用户
     * @return 服务号列表
     */
    List<ServiceNumberVO> queryServiceNumbers(FsUserVO user);

    /**
     * 设置客服人员信息
     * @param user 用户
     * @param supportStaffForm 客服人员信息
     * @return boolean
     */
    boolean setSupportStaff(FsUserVO user, SupportStaffForm supportStaffForm);

    /**
     * 查询客服人员信息
     * @param user 用户
     * @param supportStaffForm 客服人员信息
     * @return SupportStaffVO
     */
    SupportStaffVO querySupportStaff(FsUserVO user, SupportStaffForm supportStaffForm);

    /**
     * 更新服务号工作台的session
     * @param user 用户
     * @param appId 应用ID
     * @param platformMetaSessionVO 服务号信息
     * @param serviceTypeEnum 操作类型
     * @param serviceType 服务号传0 ，外联服务号传1
     * @return boolean
     */
    boolean modifyServiceWorkBenchSession(FsUserVO user, String appId, PlatformMetaSessionVO platformMetaSessionVO, ServiceTypeEnum serviceTypeEnum, Integer serviceType);

    /**
     * 更新服务号session
     * @param user 用户
     * @param appId 应用ID
     * @param serviceTypeEnum 操作类型
     * @return boolean
     */
    boolean modifyServiceSession(FsUserVO user, String appId, ServiceTypeEnum serviceTypeEnum);

    /**
     * 设置工单管理开关
     * @param user 用户
     * @param form 工单信息
     * @return boolean
     */
    //老工单不用了
//    boolean setWorkOrder(FsUserVO user, ServiceNumberForm form);

    /**
     * 设置工单管理开关(paas)
     * @param user 用户
     * @param form 工单信息
     * @return boolean
     */
    void setWorkOrderPaas(FsUserVO user, ServiceNumberForm form);

    /**
     * 查询工单管理开关状态(paas)
     * @param user 用户
     * @param form 工单信息
     * @return boolean
     */
    Integer queryWorkOrderPaasStatus(FsUserVO user, ServiceNumberForm form);

    /**
     * 查询工单管理开关状态
     * @param user 用户
     * @param form 工单信息
     * @return boolean
     */
    //老工单下线
//    Integer queryWorkOrderStatus(FsUserVO user, ServiceNumberForm form);

    /**
     * 设置问卷管理开关
     * @param user 用户
     * @param form 问卷信息
     * @return boolean
     */
    boolean setQuestionnaire(FsUserVO user, ServiceNumberForm form);

    /**
     * 查询问卷管理开关状态
     * @param user 用户
     * @param form 问卷信息
     * @return boolean
     */
    Integer queryQuestionnaireStatus(FsUserVO user, ServiceNumberForm form);

    /**
     * 查询审批单开关状态
     * @param user 用户
     * @param form
     * @return boolean
     */
    @Deprecated
    Integer queryApprovalStatus(FsUserVO user, ServiceNumberForm form);

    /**
     * 设置审批单管理开关(paas)
     * @param user 用户
     * @param form 审批单信息
     * @return boolean
     */
    @Deprecated
    void setApproval(FsUserVO user, ServiceNumberForm form);

    /**
     * 服务号旧数据补偿操作
     * @param user 用户
     * @param appId 应用ID集合
     * @param setType 恢复数据类型，1：当前企业  9999：所有企业
     */
    @Deprecated
    void setServiceOldDate(FsUserVO user, String appId, Integer setType);
}
