package com.facishare.open.app.center.controller;


import com.facishare.open.app.center.ajax.code.AjaxCode;
import com.facishare.open.app.center.ajax.result.AjaxResult;
import com.facishare.open.app.center.common.BaseController;
import com.facishare.open.app.center.manager.SystemUpgradeManager;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.exception.BizException;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 应用中心系统升级相关接口
 *
 * <AUTHOR>
 *         2016年6月13日
 */
@Controller
@RequestMapping("/open/appcenter/system/upgrade")
public class SystemUpgradeController extends BaseController {
    @Resource
    private SystemUpgradeManager systemUpgradeManager;

    /**
     * 检查用户是否已经进行过升级提醒
     *
     * @param fsUserVO 用户.
     * @param version 升级版本标识
     * @param finishDateStr 结束升级指引时间 格式：yyyy-MM-dd HH:mm:ss
     * @return AjaxResult.
     */
    @Deprecated //todo 已经被open/operatingcenter/first/time/event/isFirstTime接口替代 added by liqiulin@20160823
    @RequestMapping("/needGuide")
    @ResponseBody
    public AjaxResult needGuide(@ModelAttribute FsUserVO fsUserVO,
                                    @RequestParam(value = "version") String version,
                                    @RequestParam(value = "finishDateStr") String finishDateStr) {
        checkParamNotBlank(version, "系统升级的版本标识不能为空"); // ignoreI18n
        checkParamNotBlank(finishDateStr, "系统升级指引提示结束时间不能为空"); // ignoreI18n

        Date finishDate = null;
        try {
            finishDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(finishDateStr);
        } catch (ParseException e) {
            throw new BizException(AjaxCode.PARAM_ERROR, "系统升级指引提示结束时间格式不对"); // ignoreI18n
        }

        boolean needGuide = systemUpgradeManager.needGuide(fsUserVO, version, finishDate);

        Map<String, Object> result = new HashMap<>();
        result.put("needGuide", needGuide);
        return new AjaxResult(result);
    }
}
