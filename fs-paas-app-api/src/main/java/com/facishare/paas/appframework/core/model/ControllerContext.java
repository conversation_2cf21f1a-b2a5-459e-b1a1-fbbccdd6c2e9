package com.facishare.paas.appframework.core.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.experimental.Delegate;

/**
 *
 *
 * Created by liyiguang on 2017/7/3.
 */
@AllArgsConstructor
@Getter
public class ControllerContext {
    @Delegate
    private final RequestContext requestContext;
    private final String objectApiName;
    private final String methodName;
    private final String bizScene;

    public ControllerContext(RequestContext requestContext, String objectApiName, String methodName) {
        this.requestContext = requestContext;
        this.objectApiName = objectApiName;
        this.methodName = methodName;
        this.bizScene = null;
    }
}
