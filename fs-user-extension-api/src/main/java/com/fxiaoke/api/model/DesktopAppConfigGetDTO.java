package com.fxiaoke.api.model;

import java.io.Serializable;

/**
 * Description
 * Created by programming on 2017/12/26.
 */
public interface DesktopAppConfigGetDTO {
    class Arg implements Serializable{
        private static final long serialVersionUID = -4745440744964153806L;
        private long enterpriseId;
        private long employeeId;

        public Arg(final long enterpriseId,final long employeeId) {
            this.enterpriseId = enterpriseId;
            this.employeeId = employeeId;
        }

        public Arg() {
        }

        public long getEnterpriseId() {
            return enterpriseId;
        }

        public void setEnterpriseId(final long enterpriseId) {
            this.enterpriseId = enterpriseId;
        }

        public long getEmployeeId() {
            return employeeId;
        }

        public void setEmployeeId(final long employeeId) {
            this.employeeId = employeeId;
        }

        @Override
        public String toString() {
            return "Arg{" +
                    "enterpriseId=" + enterpriseId +
                    ", employeeId=" + employeeId +
                    '}';
        }
    }

    class Result implements Serializable {

        private static final long serialVersionUID = 8862890787069223538L;

        private String config;

        public String getConfig() {
            return config;
        }

        public void setConfig(final String config) {
            this.config = config;
        }

        @Override
        public String toString() {
            return "Result{" +
                    "config='" + config + '\'' +
                    '}';
        }
    }
}
