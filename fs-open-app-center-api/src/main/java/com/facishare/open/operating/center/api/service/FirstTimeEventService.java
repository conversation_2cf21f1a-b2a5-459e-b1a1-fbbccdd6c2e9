package com.facishare.open.operating.center.api.service;


import com.facishare.dubbo.plugin.annotation.RestAction;
import com.facishare.open.common.result.BaseResult;

/**
 * 第一次事件服务
 * Created by liqiulin on 2016/8/1.
 */
public interface FirstTimeEventService {
    /**
     * 判断是否事件执行者是否第一次执行该事件，注意：若该事件是第一次，则会同时标记廖人员已经执行该事件。
     * @param eventFlag 事件标识
     * @param eventExecutor 事件执行对象，如 fsUserString（示例  E.xxx.111）
     * @return
     */
    @RestAction("isFirstTimeAction")
    BaseResult<Boolean> isFirstTime(String eventFlag, String eventExecutor);

    /**
     * 判断是否事件执行者是否第一次执行该事件
     * @param eventFlag 事件标识
     * @param eventExecutor 事件执行对象，如 fsUserString（示例  E.xxx.111）
     * @param onlyQuery 是否仅查询而不同步记录事件记录
     * @return
     */
    BaseResult<Boolean> isFirstTime(String eventFlag, String eventExecutor, boolean onlyQuery);
}
