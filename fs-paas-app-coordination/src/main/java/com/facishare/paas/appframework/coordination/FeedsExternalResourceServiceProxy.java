package com.facishare.paas.appframework.coordination;


import com.facishare.fsi.proxy.annotation.FsiService;
import com.facishare.fsi.proxy.annotation.FsiUri;
import com.facishare.paas.appframework.coordination.dto.FeedsExternalResourceDto;
import com.facishare.paas.appframework.coordination.dto.GetAccountCostContentByID;

/**
 * 调用协同获取CRM客户费用
 * <p>
 * Created by yuanjl on 19/5/13.
 */
@FsiService("MDS")
public interface FeedsExternalResourceServiceProxy {
    @FsiUri("Feeds/ExternalResource/MergeResources")
    FeedsExternalResourceDto.MergeResourcesResult mergeResources(FeedsExternalResourceDto.MergeResourcesArg arg, String ea);
}

