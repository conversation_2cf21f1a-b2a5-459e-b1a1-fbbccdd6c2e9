package com.facishare.paas.appframework.metadata.dto.tools;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Map;

public interface BrushLayouts {
    @EqualsAndHashCode(callSuper = true)
    @Data
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg extends InitTools.Arg {
        private Map<String, Object> listLayout;
        private Map<String, Object> mobileLayout;
        private Map<String, Object> detailLayout;
        private Map<String, Object> addEditLayout;
    }
}
