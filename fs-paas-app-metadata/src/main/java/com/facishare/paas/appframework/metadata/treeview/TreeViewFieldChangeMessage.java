package com.facishare.paas.appframework.metadata.treeview;

import com.facishare.paas.appframework.common.util.JacksonUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.nio.charset.StandardCharsets;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor(staticName = "of")
public class TreeViewFieldChangeMessage {
    private String tenantId;
    private String describeApiName;
    private List<String> dataIds;


    public byte[] toMessageData() {
        return JacksonUtils.toJson(this).getBytes(StandardCharsets.UTF_8);
    }
}
