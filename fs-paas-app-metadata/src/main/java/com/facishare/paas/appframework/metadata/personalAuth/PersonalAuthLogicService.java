package com.facishare.paas.appframework.metadata.personalAuth;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.personalAuth.model.ActionCallback;
import com.facishare.paas.appframework.metadata.personalAuth.model.GetPersonalAuthUrl;
import com.facishare.paas.appframework.metadata.repository.model.PersonalAuthEntity;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> create by liy on 2024/6/13
 */
public interface PersonalAuthLogicService {
    /**
     * 获取所有授权记录
     */
    List<PersonalAuthEntity> getAllAuth(User user, int pageNumber, int pageSize);

    /**
     * 获取当前人授权记录
     */
    PersonalAuthEntity getCurrentPersonalAuth(User user, String appType, String pluginApiName);

    /**
     * 添加授权记录
     */
    PersonalAuthEntity addAuth(User user, String appType, String pluginApiName, Map<String, Object> runtimeData, long expiredTime);

    /**
     * 删除授权记录
     */
    PersonalAuthEntity deleteAuth(User user, String appType, String pluginApiName);

    /**
     * 获取厂商授权url
     */
    GetPersonalAuthUrl.Result getAuthUrl(User user, String appType, String pluginApiName);

    /**
     * 处理用户授权后事件
     */
    ActionCallback.Result actionCallback(User user, String appType, String pluginApiName, String code);

    /**
     * 清理所有用户授权
     */
    void cleanAllUserAuth(User user, String appType, String pluginApiName);
}
