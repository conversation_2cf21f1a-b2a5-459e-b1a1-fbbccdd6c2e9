package com.facishare.paas.appframework.metadata.switchcache;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ContextCacheUtil;
import com.github.jedis.support.MergeJedisCmd;
import com.google.common.base.Preconditions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.BooleanSupplier;
import java.util.stream.Collectors;

/**
 * create by z<PERSON><PERSON> on 2021/04/16
 */
@Service
public class SwitchCacheRedisServiceImpl implements SwitchCacheRedisService {

    @Autowired
    @Qualifier("switchCacheRedisService")
    private MergeJedisCmd switchCacheRedis;

    @Override
    public boolean cacheExit(String tenantId, String switchType, String objectApiName) {
        String redisKey = SwitchInfo.getRedisKey(tenantId, switchType, objectApiName);
        Long ret = switchCacheRedis.hsetnx(redisKey, "status", "ACCEPT");
        switchCacheRedis.expire(redisKey, (int) TimeUnit.DAYS.toSeconds(1));
        return ret != null && ret == 0L;
    }

    @Override
    public Optional<Boolean> getIfPresent(String tenantId, String switchType, String switchName, String objectApiName) {
        String redisKey = SwitchInfo.getRedisKey(tenantId, switchType, objectApiName);
        return ContextCacheUtil.<Boolean>hget(redisKey, switchName)
                .map(Optional::of)
                .orElseGet(() -> cacheStatusIsOpen(switchCacheRedis.hget(redisKey, switchName))
                        .map(open -> updateContextCache(switchName, redisKey, open)));
    }

    @Override
    public boolean get(String tenantId, String switchType, String switchName, String objectApiName, BooleanSupplier other) {
        Preconditions.checkNotNull(other);
        return getIfPresent(tenantId, switchType, switchName, objectApiName)
                .orElseGet(() -> {
                    boolean switchStatus = other.getAsBoolean();
                    put(tenantId, switchType, switchName, objectApiName, switchStatus);
                    return switchStatus;
                });
    }

    @Override
    public void put(String tenantId, String switchType, String switchName, String objectApiName, boolean switchStatus) {
        put(SwitchInfo.of(tenantId, switchType, switchName, objectApiName, switchStatus));
    }

    @Override
    public void put(SwitchInfo switchInfo) {
        String redisKey = switchInfo.toRedisKey();
        switchCacheRedis.hset(redisKey, switchInfo.getSwitchName(), convertStatus(switchInfo));
        updateContextCache(redisKey, switchInfo.getSwitchName(), switchInfo.isSwitchStatus());
    }

    @Override
    public void putAll(Collection<SwitchInfo> switchInfos) {
        Map<String, Map<String, String>> switchMap = switchInfos.stream()
                .collect(Collectors.groupingBy(SwitchInfo::toRedisKey,
                        Collectors.mapping(it -> it, Collectors.toMap(SwitchInfo::getSwitchName, this::convertStatus))));
        switchMap.forEach(switchCacheRedis::hmset);
        updateContextCache(switchMap);
    }

    @Override
    public void delete(String tenantId, String switchType, String objectApiName) {
        String redisKey = SwitchInfo.getRedisKey(tenantId, switchType, objectApiName);
        switchCacheRedis.del(redisKey);
    }

    private void updateContextCache(Map<String, Map<String, String>> switchMap) {
        if (CollectionUtils.empty(switchMap)) {
            return;
        }
        switchMap.forEach((key, hash) -> hash.forEach((fieldName, value) -> updateContextCache(key, fieldName, cacheStatusIsOpen(value).orElse(null))));
    }

    private Boolean updateContextCache(String switchName, String redisKey, Boolean open) {
        ContextCacheUtil.hset(redisKey, switchName, open);
        return open;
    }

    private String convertStatus(SwitchInfo switchInfo) {
        return switchInfo.isSwitchStatus() ? "1" : "0";
    }

    private Optional<Boolean> cacheStatusIsOpen(String switchStatus) {
        return Optional.ofNullable(switchStatus).map("1"::equals);
    }

}
