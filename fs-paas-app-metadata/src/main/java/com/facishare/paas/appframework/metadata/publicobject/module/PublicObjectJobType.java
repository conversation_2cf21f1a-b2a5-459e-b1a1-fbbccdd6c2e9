package com.facishare.paas.appframework.metadata.publicobject.module;

import lombok.Getter;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/12/12
 */
public enum PublicObjectJobType {
    /**
     * 校验能否开启公共对象的任务
     */
    VERIFY_JOB("verify_job"),
    /**
     * 开启公共对象的任务
     */
    OPEN_JOB("open_job"),
    /**
     * 关闭公共对象的任务
     */
    CLOSE_JOB("close_job"),
    /**
     * 邀请互联自管理企业开启公共对象的任务
     */
    INVITATION_JOB("invitation_job"),
    /**
     * 给互联代管企业升级公共对象的任务
     */
    ENABLE_JOB("enable_job"),
    /**
     * 解绑公共对象的任务
     */
    DISABLE_JOB("disable_job"),
    /**
     * 追加字段的任务
     */
    APPEND_FIELD("append_field"),
    UNKNOWN("Unknown"),
    ;

    @Getter
    private final String type;

    PublicObjectJobType(String type) {
        this.type = type;
    }

    private static final Map<String, PublicObjectJobType> typeMap = Stream.of(values())
            .collect(Collectors.toMap(PublicObjectJobType::getType, Function.identity()));

    public static PublicObjectJobType fromType(String type) {
        return typeMap.getOrDefault(type, UNKNOWN);
    }

    public boolean isJob() {
        return this != UNKNOWN && this != APPEND_FIELD;
    }

    public boolean isOpenJOb() {
        return this == OPEN_JOB || this == ENABLE_JOB || this == INVITATION_JOB;
    }

    public boolean isCloseJob() {
        return this == CLOSE_JOB || this == DISABLE_JOB;
    }
}
