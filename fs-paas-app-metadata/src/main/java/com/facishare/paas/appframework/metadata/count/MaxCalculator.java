package com.facishare.paas.appframework.metadata.count;

import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.Count;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * Created by zhouwr on 2018/4/25
 */
public class MaxCalculator extends AbstractCountFieldCalculator {

    @Override
    protected BigDecimal doCalculate(BigDecimal result, List<IObjectData> detailDataList, Count countField,
                                     CountValues countValues) {
        boolean notPartIn = FieldDescribeExt.isDateTypeField(countField.getReturnType()) || Boolean.FALSE.equals(countField.isCountToZero());
        for (IObjectData data : detailDataList) {
            if (notPartIn) {
                Object value = data.get(countField.getCountFieldApiName());
                if (Objects.isNull(value)) {
                    continue;
                }
            }

            BigDecimal value = getFieldValue(data, countField.getCountFieldApiName(), countField.getReturnType(),
                    false);
            if (result == null) {
                result = value;
            } else if (value != null && value.compareTo(result) > 0) {
                result = value;
            }
        }

        return result;
    }

}
