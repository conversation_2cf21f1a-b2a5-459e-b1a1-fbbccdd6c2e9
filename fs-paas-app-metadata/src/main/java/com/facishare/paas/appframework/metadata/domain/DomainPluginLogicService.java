package com.facishare.paas.appframework.metadata.domain;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.RequestType;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.repository.model.DomainPluginInstance;
import com.facishare.paas.appframework.metadata.repository.model.DomainPluginParam;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.metadata.api.QueryResult;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by zhouwr on 2021/11/9.
 */
public interface DomainPluginLogicService {
    QueryResult<DomainPluginInstance> findPluginInstanceByQuery(User user, Query query);

    List<DomainPluginDefinition> findAvailablePluginDefinitionsForManagement(String tenantId, String objectApiName);

    // 按照插件定义信息过滤企业下适用于某个对象的插件
    List<DomainPluginInstance> filterPluginInstancesByDefinitions(String tenantId, List<DomainPluginInstance> instanceList);

    Map<String, DomainPluginDefinition> findPluginDefinitionByApiNames(List<String> pluginApiNames, boolean processI18NProps);

    Map<String, String> findPluginLabelByApiNames(List<String> pluginApiNames);

    List<DomainPluginInstance> findPluginInstances(String tenantId, String objectApiName, String agentType, List<String> recordTypeList);

    List<DomainPluginInstance> findPluginInstances(String tenantId, List<String> objectApiNames, String agentType, List<String> recordTypeList);

    List<DomainPluginInstance> findPluginInstancesByRunTimeStatus(String tenantId, List<String> objectApiNames, String agentType,
                                                                    List<String> recordTypeList);

    List<SimpleDomainPluginDescribe> findSimplePluginByActionCode(String tenantId, String objectApiName, String actionCode,
                                                                  List<String> recordTypeList, RequestType requestType);

    DomainPluginInstance findPluginInstanceByApiName(String tenantId, String objectApiName, String pluginApiName);

    DomainPluginInstance findPluginInstanceByApiName(String tenantId, String objectApiName, String pluginApiName,
                                                     String fieldApiName, boolean includeDisable);

    List<DomainPluginInstance> findPluginInstanceByPluginApiName(String tenantId, String pluginApiName);

    DomainPluginInstance findPluginInstanceById(String tenantId, String id, boolean includeDisable);

    DomainPluginParam findPluginParam(String tenantId, String objectApiName, String pluginApiName);

    void createPluginInstance(User user, DomainPluginInstance instance);

    void updatePluginInstanceStatus(User user, String objectApiName, String pluginApiName, String fieldApiName, boolean isActive);

    void updatePluginInstanceStatusById(User user, String id, boolean isActive);

    void updatePluginInstance(User user, DomainPluginInstance instance);

    void invalidPluginInstance(User user, String objectApiName, String pluginApiName, String fieldApiName);

    void invalidPluginInstanceById(User user, String id);

    Map<String, Boolean> findPluginStatus(String tenantId, String objectApiName, List<String> pluginApiNames);

    void validateByDependencies(String tenantId, DomainPluginInstance instance);

    FieldConfig findFieldConfigByDomainPlugin(String tenantId,
                                              String objectApiName,
                                              List<String> detailObjectApiNames,
                                              List<String> recordTypeList,
                                              String pageType);

    Set<String> getHiddenFieldsByDomainPluginDescribe(String pageType, List<SimpleDomainPluginDescribe> pluginDescribes);

    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    @Data
    class FieldConfig {
        private Map<String, Set<String>> readOnlyFieldMap;
        private Map<String, Set<String>> hiddenFieldMap;

        public Set<String> getReadOnlyFields(String objectApiName) {
            if (CollectionUtils.empty(readOnlyFieldMap)) {
                return null;
            }
            return readOnlyFieldMap.get(objectApiName);
        }

        public Set<String> getHiddenFields(String objectApiName) {
            if (CollectionUtils.empty(hiddenFieldMap)) {
                return null;
            }
            return hiddenFieldMap.get(objectApiName);
        }
    }
}
