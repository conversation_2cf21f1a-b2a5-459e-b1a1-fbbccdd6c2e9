package com.facishare.paas.appframework.metadata.initscene;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.SearchTemplateCode;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.api.service.ISearchTemplateService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component("defaultSceneProvider")
public class DefaultSceneProvider implements SceneInitProvider {
    @Autowired
    protected ISearchTemplateService searchTemplateService;

    @Override
    public String getApiName() {
        return null;
    }

    @Override
    public List<ISearchTemplate> getDefaultSearchTemplateList(User user, String apiName, String extendAttribute) {
        try {
            return searchTemplateService.findByObjectDescribeAPINameAndCode(user.getTenantId(), apiName, Sets.newHashSet(
                    SearchTemplateCode.ALL,
                    SearchTemplateCode.IN_CHARGE,
                    SearchTemplateCode.INVOLVED,
                    SearchTemplateCode.IN_CHARGE_DEPT,
                    SearchTemplateCode.SUB_IN_CHARGE,
                    SearchTemplateCode.SUB_INVOLVED,
                    SearchTemplateCode.SHARED,
                    SearchTemplateCode.FOLLOW,
                    SearchTemplateCode.RECENT_VISITS));
        } catch (MetadataServiceException e) {
            log.error("Error in init DefaultSearchTemplate, ei:{}, apiName:{}", user.getTenantId(), apiName);
        }
        return Lists.newArrayList();
    }
}
