package com.facishare.paas.appframework.metadata.publicobject;

import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.EncryptUtil;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.publicobject.dto.ConnectedEnterpriseDTO;
import com.facishare.paas.appframework.metadata.publicobject.dto.PublicObjectJobParamDTO;
import com.facishare.paas.appframework.metadata.publicobject.module.*;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.service.impl.MetadataTransactionService;
import com.facishare.paas.timezone.DateTimeFormatUtils;
import com.facishare.paas.timezone.TimeZoneContextHolder;
import com.facishare.uc.api.model.enterprise.EnterpriseInfoDto;
import com.facishare.uc.api.model.enterprise.arg.GetSimpleEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetSimpleEnterpriseDataResult;
import com.facishare.uc.api.model.fscore.SimpleEnterpriseData;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.facishare.uc.api.service.EnterpriseRemoteService;
import com.fxiaoke.enterpriserelation2.arg.SendCrossHelperMessagesArg;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.service.SendMessageServiceProxy;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by zhaooju on 2023/12/24
 */
@Slf4j
@Service
public class PublicObjectInviteServiceImpl implements PublicObjectInviteService {
    /**
     * 邀请开启公共对象的页面url
     */
    private static final String PUBLIC_OBJECT_INVITE_PAGE_URL = "{0}/hcrm/avah5#uipaas_custom/object_detail/pages/public-object-invite/index?apiName={1}&token={2}";

    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private SendMessageServiceProxy sendMessageServiceProxy;
    @Autowired
    private OrgService orgService;
    @Autowired
    private EnterpriseEditionService enterpriseEditionService;
    @Autowired
    private PublicObjectJobService publicObjectJobService;
    @Autowired
    private MetadataTransactionService metadataTransactionService;
    @Autowired
    private PublicObjectEnterpriseRelationService publicObjectEnterpriseRelationService;
    @Autowired
    private EnterpriseRemoteService enterpriseRemoteService;

    @Override
    @Transactional
    public void sendInvitationMessage(User user, PublicObjectJobInfo publicObjectJobInfo, String jobId) {
        if (publicObjectJobInfo.getJobStatus() != PublicObjectJobStatus.INVITATION) {
            return;
        }
        /*
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.INVITATION_MESSAGE_SUPPORT_I18N_GRAY, user.getTenantId())) {
            String objectApiName = publicObjectJobInfo.getObjectApiName();
            String downstreamTenantId = getDownstreamTenantId(publicObjectJobInfo);


            SendCrossHelperCardMessagesArg arg = new SendCrossHelperCardMessagesArg();
            PublicEmployeeSimple employeeSimple = batchGetRelationOwnerByOutTenant(user, publicObjectJobInfo);
            arg.setEmployeeIds(employeeSimple.getEmployeeIds());
            arg.setEnterpriseAccount(employeeSimple.getEnterpriseAccount());

            SendCrossHelperCardMessagesArg.TextCardMessage message = new SendCrossHelperCardMessagesArg.TextCardMessage();
            arg.setMessage(message);

            // 设置标题和时间
            SendCrossHelperCardMessagesArg.TextCardMessageHead head = new SendCrossHelperCardMessagesArg.TextCardMessageHead();
            head.setTime(System.currentTimeMillis());
            SendCrossHelperCardMessagesArg.TextCardElement titleElement = buildInvitationTitleMessage();
            head.setTitleElement(titleElement);
            message.setHead(head);

            // 设置邀请内容
            SendCrossHelperCardMessagesArg.TextCardMessageBody body = new SendCrossHelperCardMessagesArg.TextCardMessageBody();
            SendCrossHelperCardMessagesArg.TextCardElement contentElement = buildInvitationContentMessage(user, downstreamTenantId, objectApiName);
            body.setContentElement(contentElement);
            message.setBody(body);

            SendCrossHelperCardMessagesArg.TextCardMessageFoot foot = new SendCrossHelperCardMessagesArg.TextCardMessageFoot();
            message.setFoot(foot);

            // 设置跳转的链接
            String url = generateInvitationUrl(user, jobId, objectApiName, downstreamTenantId);
            message.setInnerPlatformWebUrl(url);
            message.setInnerPlatformMobileUrl(url);

            HeaderObj header = HeaderObj.newInstance(user.getTenantIdInt());
            publicObjectJobService.initPublicObjectJobDetail(user, jobId, objectApiName, PublicObjectJobStatus.INVITATION, employeeSimple.getEnterpriseId());
            // 发送消息
            sendMessageServiceProxy.sendCrossHelperTextCardMessages(header, arg);
            return;
        }
        */
        String objectApiName = publicObjectJobInfo.getObjectApiName();
        SendCrossHelperMessagesArg.OTTemplateMessage message = new SendCrossHelperMessagesArg.OTTemplateMessage();
        // 设置标题和时间
        SendCrossHelperMessagesArg.OTTemplateMessage.Title title = new SendCrossHelperMessagesArg.OTTemplateMessage.Title();
        title.setContent(I18NExt.text(I18NKey.INVITES_UPGRADE_PUBLIC_OBJECT));
        title.setTime(DateTimeFormatUtils.formatWithTimezoneInfo(System.currentTimeMillis(), TimeZoneContextHolder.getTenantTimeZone(), IFieldType.DATE_TIME));
        message.setTitle(title);
        String downstreamTenantId = getDownstreamTenantId(publicObjectJobInfo);
        // 设置按钮名称和跳转的路径
        SendCrossHelperMessagesArg.OTTemplateMessage.Button button = new SendCrossHelperMessagesArg.OTTemplateMessage.Button();
        button.setTitle(I18NExt.text(I18NKey.VIEW_DETAILS));
        String url = generateInvitationUrl(user, jobId, objectApiName, downstreamTenantId);
        button.setUrl(url);
        message.setButton(button);
        // 查询对象名称，设置邀请内容
        SendCrossHelperMessagesArg.OTTemplateMessage.Frist first = new SendCrossHelperMessagesArg.OTTemplateMessage.Frist();
        String displayName = findDisplayName(downstreamTenantId, objectApiName);
        String invitationMessage = getInvitationMessage(user, displayName);
        first.setContent(invitationMessage);
        message.setFirst(first);
        // 接受消息的企业和人
        SendCrossHelperMessagesArg arg = new SendCrossHelperMessagesArg();
        PublicEmployeeSimple employeeSimple = batchGetRelationOwnerByOutTenant(user, publicObjectJobInfo);
        arg.setEmployeeIds(employeeSimple.getEmployeeIds());
        arg.setEnterpiseAccount(employeeSimple.getEnterpriseAccount());
        arg.setMessage(message);

        HeaderObj header = HeaderObj.newInstance(user.getTenantIdInt());

        publicObjectJobService.initPublicObjectJobDetail(user, jobId, objectApiName, PublicObjectJobStatus.INVITATION, employeeSimple.getEnterpriseId());
        // 发送消息
        sendMessageServiceProxy.sendCrossHelperMessages(header, arg);
    }

    private String generateInvitationUrl(User user, String jobId, String objectApiName, String downstreamTenantId) {
        String token = generateToken(user, jobId);
        String enterpriseDomain = findEnterpriseDomain(downstreamTenantId);
        return MessageFormat.format(PUBLIC_OBJECT_INVITE_PAGE_URL, enterpriseDomain, objectApiName, token);
    }

    /**
     * 查询企业域名
     *
     * @param tenantId 企业id
     * @return 企业域名
     */
    private String findEnterpriseDomain(String tenantId) {
        EnterpriseInfoDto.FindEIRequest arg = new EnterpriseInfoDto.FindEIRequest();
        arg.setEnterpriseId(Integer.parseInt(tenantId));
        EnterpriseInfoDto.FindEIResult enterpriseInfo = enterpriseRemoteService.findEnterpriseByEI(arg);
        return Optional.ofNullable(enterpriseInfo).map(EnterpriseInfoDto.FindEIResult::getEnterpriseInfo).map(EnterpriseInfoDto.EnterpriseInfo::getDomain).orElse("");
    }

    @Override
    public PublicObjectJobInvitationInfo findInvitationInfo(User user, IObjectDescribe describe, String token) {
        InviteTokenInfo tokenInfo = decodeToken(token);
        PublicObjectJobInfo objectJobInfo = findAndVerifyPublicObjectJobBeforeInvite(user, describe.getApiName(), tokenInfo, false);
        String message = getInvitationMessage(tokenInfo.toUpstreamUser(), describe.getDisplayName());
        return PublicObjectJobInvitationInfo.fromPublicObjectJobInfo(objectJobInfo, message);
    }

    @Override
    public InviteTokenInfo agreeInvitation(User user, IObjectDescribe describe, String token) {
        return updateInvitationStatus(user, describe, token, PublicObjectJobStatus.WAITING);
    }

    @Override
    @Transactional
    public InviteTokenInfo rejectInvitation(User user, IObjectDescribe describe, String token) {
        return updateInvitationStatus(user, describe, token, PublicObjectJobStatus.CANCEL);
    }

    private PublicObjectJobInfo findAndVerifyPublicObjectJobBeforeInvite(User user, String describeApiName, InviteTokenInfo tokenInfo, boolean update) {
        PublicObjectJobInfo objectJobInfo = publicObjectJobService.queryPublicObjectJobById(User.systemUser(tokenInfo.getUpstreamTenantId()), describeApiName, tokenInfo.getJobId());
        if (objectJobInfo.getJobType() != PublicObjectJobType.INVITATION_JOB) {
            log.warn("verify job status fail! jobId:{}, jobType:{}", tokenInfo.getJobId(), objectJobInfo.getJobType());
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        if (update && objectJobInfo.getJobStatus() != PublicObjectJobStatus.INVITATION) {
            log.warn("verify job status fail! jobId:{}, jobStatus:{}", tokenInfo.getJobId(), objectJobInfo.getJobStatus());
            throw new ValidateException(I18NExt.text(I18NKey.INVITATION_PROCESSED));
        }
        PublicObjectJobParamDTO jobParam = objectJobInfo.getJobParam();
        List<ConnectedEnterpriseDTO> enterpriseInfos = jobParam.getEnterpriseInfos();
        PublicObjectJobParamVerifyInfo.EnterpriseHelper enterpriseHelper = PublicObjectJobParamVerifyInfo.EnterpriseHelper.from(enterpriseInfos);
        if (!enterpriseHelper.inTenantIds(user.getTenantId())) {
            log.warn("findAndVerifyPublicObjectJobBeforeInvite fail! tenantId:{}, jobParam:{}", user.getTenantId(), jobParam.toJsonString());
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        return objectJobInfo;
    }

    private InviteTokenInfo updateInvitationStatus(User user, IObjectDescribe describe, String token, PublicObjectJobStatus jobStatus) {
        InviteTokenInfo tokenInfo = decodeToken(token);
        findAndVerifyPublicObjectJobBeforeInvite(user, describe.getApiName(), tokenInfo, true);
        User upstreamUser = tokenInfo.toUpstreamUser();
        String jobId = publicObjectJobService.updatePublicObjectJobStatus(upstreamUser, describe.getApiName(), tokenInfo.getJobId(), jobStatus);
        publicObjectJobService.updatePublicObjectDetailJobStatus(upstreamUser, describe.getApiName(), jobId, jobStatus, user);
        return tokenInfo;
    }

    private String getInvitationMessage(User user, String displayName) {
        String enterpriseName = getEnterpriseName(user.getTenantId());
        String userName = getUserNameByUser(user.getTenantId(), user.getUserId());
        return I18NExt.text(I18NKey.INVITES_UPGRADE_PUBLIC_OBJECT_MESSAGE, enterpriseName, userName, displayName);
    }

    /*
        private SendCrossHelperCardMessagesArg.TextCardElement buildInvitationTitleMessage() {
            SendCrossHelperCardMessagesArg.InternationalItem headTextInfo = new SendCrossHelperCardMessagesArg.InternationalItem(I18NKey.INVITES_UPGRADE_PUBLIC_OBJECT);
            return new SendCrossHelperCardMessagesArg.TextCardElement(I18NExt.text(I18NKey.INVITES_UPGRADE_PUBLIC_OBJECT), headTextInfo);
        }

        private SendCrossHelperCardMessagesArg.TextCardElement buildInvitationContentMessage(User user, String downstreamTenantId, String objectApiName) {
            String displayName = findDisplayName(downstreamTenantId, objectApiName);
            String enterpriseName = getEnterpriseName(user.getTenantId());
            String userName = getUserNameByUser(user.getTenantId(), user.getUserId());

            String describeDisplayNameKey = GetI18nKeyUtil.getDescribeDisplayNameKey(objectApiName);
            List<String> internationalParameters = Lists.newArrayList(enterpriseName, userName, I18NExt.getI18NKey(describeDisplayNameKey));
            Map<String, String> defaultParameterValues = Maps.newHashMap();
            defaultParameterValues.put(describeDisplayNameKey, displayName);

            SendCrossHelperCardMessagesArg.InternationalItem contentTextInfo = SendCrossHelperCardMessagesArg.InternationalItem.buildInternationalItem(
                    I18NKey.INVITES_UPGRADE_PUBLIC_OBJECT_MESSAGE, internationalParameters, defaultParameterValues);
            return new SendCrossHelperCardMessagesArg.TextCardElement(I18NExt.text(I18NKey.INVITES_UPGRADE_PUBLIC_OBJECT_MESSAGE,
                    enterpriseName, userName, displayName), contentTextInfo);
        }
    */
    private String generateToken(User user, String jobId) {
        InviteTokenInfo tokenInfo = InviteTokenInfo.of(user.getTenantId(), user.getUserId(), jobId);
        try {
            return EncryptUtil.encode(tokenInfo.toJsonString());
        } catch (Exception e) {
            log.warn("getToken fail! ei:{}, jobId:{}", user.getTenantId(), jobId, e);
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
    }

    private InviteTokenInfo decodeToken(String token) {
        try {
            String decode = EncryptUtil.decode(token);
            return InviteTokenInfo.fromJsonString(decode);
        } catch (Exception e) {
            log.warn("decodeToken fail! token:{}", token, e);
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
    }

    private String findDisplayName(String tenantId, String objectApiName) {
        try {
            Map<String, IObjectDescribe> describeMap = metadataTransactionService.executeWithOutMetadataTransaction(() -> describeLogicService.findObjectsWithoutCopy(tenantId, Lists.newArrayList(objectApiName)));
            IObjectDescribe objectDescribe = describeMap.get(objectApiName);
            if (Objects.isNull(objectDescribe)) {
                String enterpriseName = getEnterpriseName(tenantId);
                throw new ValidateException(I18NExt.text(I18NKey.ENTERPRISE_OBJECT_NOT_EXIST, enterpriseName, objectApiName));
            }
            return objectDescribe.getDisplayName();
        } catch (MetadataServiceException e) {
            log.warn("findDisplayName fail! tenantId:{}, objectApiName:{}", tenantId, objectApiName);
            throw new MetaDataBusinessException(e);
        }
    }

    private String getUserNameByUser(String tenantId, String userId) {
        User user = orgService.getUser(tenantId, userId);
        return user.getUserName();
    }

    private String getEnterpriseName(String tenantId) {
        GetSimpleEnterpriseDataArg arg = new GetSimpleEnterpriseDataArg();
        arg.setEnterpriseId(Integer.parseInt(tenantId));
        GetSimpleEnterpriseDataResult result = enterpriseEditionService.getSimpleEnterpriseData(arg);
        SimpleEnterpriseData simpleEnterpriseData = result.getEnterpriseData();
        return simpleEnterpriseData.getEnterpriseName();
    }

    private PublicEmployeeSimple batchGetRelationOwnerByOutTenant(User user, PublicObjectJobInfo publicObjectJobInfo) {
        PublicObjectJobParamVerifyInfo.EnterpriseHelper enterpriseHelper = PublicObjectJobParamVerifyInfo.EnterpriseHelper.from(publicObjectJobInfo.getJobParamEnterpriseInfos());
        List<String> downstreamTenantIds = enterpriseHelper.getDownstreamTenantIds();
        String downstreamTenantId = downstreamTenantIds.get(0);
        List<PublicEmployeeSimpleInfo> employeeSimpleInfos = publicObjectEnterpriseRelationService.findEnterpriseRelationAdminByDownstreamTenantId(user, downstreamTenantId);
        if (CollectionUtils.empty(employeeSimpleInfos)) {
            String enterpriseName = getEnterpriseName(downstreamTenantId);
            log.warn("batchGetRelationOwnerByOutTenant fail! downstreamTenantId:{}, enterpriseName:{}", downstreamTenantId, enterpriseName);
            throw new ValidateException(I18NExt.text(I18NKey.ENTERPRISE_NOT_HAVE_RELATION_OWNER, enterpriseName));
        }
        return PublicEmployeeSimple.from(employeeSimpleInfos);
    }

    private String getDownstreamTenantId(PublicObjectJobInfo publicObjectJobInfo) {
        return Optional.ofNullable(publicObjectJobInfo.getJobParam()).map(PublicObjectJobParamDTO::getEnterpriseInfos).orElse(Collections.emptyList()).stream().filter(ConnectedEnterpriseDTO::tenant).map(ConnectedEnterpriseDTO::getId).findFirst().orElseThrow(() -> new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR)));
    }

    @Data
    static class PublicEmployeeSimple {
        private String enterpriseId;
        private String enterpriseAccount;
        private List<Integer> employeeIds;

        public static PublicEmployeeSimple from(List<PublicEmployeeSimpleInfo> publicEmployeeSimpleInfos) {
            String enterpriseId = null;
            String enterpriseAccount = null;
            Set<String> employeeIds = Sets.newHashSet();
            for (PublicEmployeeSimpleInfo employeeSimpleInfo : publicEmployeeSimpleInfos) {
                if (Objects.isNull(enterpriseId)) {
                    enterpriseId = employeeSimpleInfo.getUpstreamTenantId();
                }
                if (Objects.isNull(enterpriseAccount)) {
                    enterpriseAccount = employeeSimpleInfo.getEnterpriseAccount();
                }
                employeeIds.add(employeeSimpleInfo.getUpstreamUserId());
            }
            PublicEmployeeSimple result = new PublicEmployeeSimple();
            result.setEmployeeIds(employeeIds.stream().map(Integer::valueOf).collect(Collectors.toList()));
            result.setEnterpriseId(enterpriseId);
            result.setEnterpriseAccount(enterpriseAccount);
            return result;
        }
    }
}
