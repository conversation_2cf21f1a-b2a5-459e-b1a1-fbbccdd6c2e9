package com.facishare.paas.appframework.metadata.dto.sfa;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Created by zhaopx on 2017/11/21.
 */
public interface BatchDeletedTeamMember {
    @Data
    @Builder
    class Arg {
        @SerializedName("ObjectType")
        @JSONField(name = "ObjectType")
        private Integer object_type;

        @SerializedName("DataIDs")
        @JSONField(name = "DataIDs")
        private List<String> object_ids;

        @SerializedName("EmployeeIDs")
        @JSONField(name = "EmployeeIDs")
        private List<String> employee_ids;
    }

    @Getter
    @Setter
    class Result extends BaseResult {
        
    }
}
