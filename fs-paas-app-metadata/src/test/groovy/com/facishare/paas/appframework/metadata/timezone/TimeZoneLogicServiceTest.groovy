package com.facishare.paas.appframework.metadata.timezone

import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.util.PaasBeanProxy
import com.facishare.social.personnel.PersonnelObjService
import com.facishare.social.personnel.model.FindByQuery
import com.facishare.social.personnel.model.PersonnelDto
import org.springframework.beans.factory.annotation.Autowired
import spock.lang.Specification

import java.time.ZoneId

/**
 * create by z<PERSON><PERSON> on 2021/05/10
 */
//@ContextConfiguration(value = "classpath:applicationContext.xml")
class TimeZoneLogicServiceTest extends Specification {
    @Autowired
    private PersonnelObjService personnelObjService
    @Autowired
    private TimeZoneLogicService timeZoneService

    def "test"() {
        expect:
        1 == 1
    }

    def "test findByQueryUseCacheChain"() {
        when:
        User user = new User(tenantId, userId)
        FindByQuery.Argument argument = new FindByQuery.Argument()
        argument.setTenantId(user.getTenantId())

        PersonnelDto personnelDto = PaasBeanProxy.newProxy(PersonnelDto.class)
        personnelDto.setUserId(user.getUserId())
        argument.setPersonnelDto(personnelDto)
        then:
        List<PersonnelDto> result = personnelObjService.findByQueryUseCacheChain(argument)
        println(result)
        where:
        tenantId | userId || _
        "74255"  | "1000" || _
    }

    def "test findUserTimeZone"() {
        when:
        def user = new User(tenantId, userId)
        then:
        def timeZone = timeZoneService.findUserTimeZone(user)
        println(timeZone.get())
        timeZone.get() == expect
        where:
        tenantId | userId || expect
        "74255"  | "1000" || ZoneId.of("Asia/Shanghai")
    }

    def "test findTenantTimeZone"() {
        expect:
        def timeZone = timeZoneService.findTenantTimeZone(tenantId)
        println(timeZone)
        timeZone == expect
        where:
        tenantId || expect
        "74255"  || ZoneId.of("Asia/Shanghai")
    }
}
