{"tenant_id": "74255", "package": "CRM", "is_active": true, "last_modified_time": 1689689497558, "create_time": 1610014319834, "description": "", "last_modified_by": "1000", "display_name": "zxf-主", "created_by": "1031", "version": 87, "is_open_display_name": false, "index_version": 200, "icon_index": 0, "is_deleted": false, "api_name": "object_3BbVb__c", "icon_path": "", "is_udef": true, "define_type": "custom", "short_name": "fCh", "_id": "5ff6de6f599b3e0001803c21", "fields": {"tenant_id": {"describe_api_name": "object_3BbVb__c", "is_index": false, "is_active": true, "create_time": 1610014319834, "pattern": "", "is_unique": false, "description": "tenant_id", "label": "tenant_id", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "tenant_id", "define_type": "system", "index_name": "ei", "max_length": 200, "status": "released"}, "field_s57r7__c": {"describe_api_name": "object_3BbVb__c", "auto_adapt_places": false, "description": "", "is_unique": false, "file_source": [], "type": "image", "is_required": false, "define_type": "custom", "is_single": false, "index_name": "a_4", "support_file_types": ["jpg", "gif", "jpeg", "png"], "is_index": true, "file_amount_limit": 7, "is_active": true, "watermark": [{"type": "variable", "value": "current_user"}, {"type": "variable", "value": "current_time"}, {"type": "variable", "value": "current_address"}], "create_time": 1672218051630, "is_encrypted": false, "label": "图片多个", "is_watermark": false, "field_num": 2, "file_size_limit": 20971520, "is_ocr_recognition": false, "api_name": "field_s57r7__c", "is_need_cdn": false, "_id": "63ac05c373ab8900018ebae4", "is_index_field": false, "identify_type": "", "help_text": "单个图片不得超过20M", "status": "new"}, "lock_rule": {"describe_api_name": "object_3BbVb__c", "is_index": false, "is_active": true, "create_time": 1610014319187, "is_encrypted": false, "auto_adapt_places": false, "description": "锁定规则", "is_unique": false, "rules": [], "default_value": "default_lock_rule", "label": "锁定规则", "type": "lock_rule", "field_num": 1, "is_need_convert": false, "is_required": false, "api_name": "lock_rule", "define_type": "package", "_id": "5ff6de6f599b3e0001803c19", "is_single": false, "label_r": "锁定规则", "is_index_field": false, "index_name": "s_2", "help_text": "", "status": "new"}, "data_own_organization": {"describe_api_name": "object_3BbVb__c", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "type": "department", "is_required": false, "wheres": [], "define_type": "package", "is_single": true, "label_r": "归属组织", "index_name": "a_2", "is_index": true, "is_active": true, "create_time": 1647329963905, "is_encrypted": false, "default_value": "", "label": "归属组织", "is_need_convert": false, "api_name": "data_own_organization", "_id": "623042ac04a720000162e97e", "is_index_field": false, "help_text": "", "status": "released"}, "field_1j8hm__c": {"describe_api_name": "object_3BbVb__c", "is_index": true, "file_amount_limit": 1, "is_active": true, "create_time": 1672224772356, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "file_source": ["local", "net"], "label": "附件", "type": "file_attachment", "field_num": 18, "file_size_limit": 104857600, "is_required": false, "api_name": "field_1j8hm__c", "define_type": "custom", "_id": "63ac200427d46d0001e0e3e9", "is_single": false, "is_index_field": false, "index_name": "a_5", "support_file_types": [], "help_text": "单个文件不得超过100M", "status": "new"}, "field_p3b8C__c": {"describe_api_name": "object_3BbVb__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "enable_clone": true, "define_type": "custom", "input_mode": "", "is_single": false, "index_name": "t_4", "max_length": 100, "is_index": true, "is_active": true, "create_time": 1639040590476, "is_encrypted": false, "default_value": "", "label": "单行文本", "field_num": 8, "api_name": "field_p3b8C__c", "_id": "61b1c64e63a33500016e8c89", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new"}, "lock_user": {"describe_api_name": "object_3BbVb__c", "is_index": false, "is_active": true, "create_time": 1610014319187, "is_encrypted": false, "auto_adapt_places": false, "description": "加锁人", "is_unique": false, "where_type": "field", "label": "加锁人", "type": "employee", "field_num": 5, "is_need_convert": false, "is_required": false, "wheres": [], "api_name": "lock_user", "define_type": "package", "_id": "5ff6de6f599b3e0001803c1b", "is_single": true, "label_r": "加锁人", "is_index_field": false, "index_name": "a_1", "help_text": "", "status": "new"}, "field_m4hw1__c": {"describe_api_name": "object_3BbVb__c", "auto_adapt_places": false, "description": "", "is_unique": false, "file_source": [], "type": "image", "is_required": false, "define_type": "custom", "is_single": false, "index_name": "a_3", "support_file_types": ["jpg", "gif", "jpeg", "png"], "is_index": true, "file_amount_limit": 1, "is_active": true, "watermark": [{"type": "variable", "value": "current_user"}, {"type": "variable", "value": "current_time"}, {"type": "variable", "value": "current_address"}], "create_time": 1649831295701, "is_encrypted": false, "label": "图片", "is_watermark": false, "field_num": 16, "file_size_limit": 20971520, "is_ocr_recognition": true, "api_name": "field_m4hw1__c", "is_need_cdn": false, "_id": "62566d7f1287fd000163f6f6", "is_index_field": false, "identify_type": "VatInvoice", "help_text": "单个图片不得超过20M", "status": "new"}, "mc_exchange_rate": {"describe_api_name": "object_3BbVb__c", "default_is_expression": false, "auto_adapt_places": false, "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "number", "decimal_places": 6, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "label_r": "汇率", "index_name": "d_1", "max_length": 16, "is_index": true, "is_active": true, "create_time": 1610014319847, "is_encrypted": false, "display_style": "input", "step_value": 1, "length": 10, "default_value": "", "label": "汇率", "field_num": 7, "api_name": "mc_exchange_rate", "_id": "5ff6de6f599b3e0001803c1e", "is_index_field": false, "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new"}, "field_e2d2P__c": {"describe_api_name": "object_3BbVb__c", "is_index": false, "is_active": true, "create_time": 1689240531388, "is_encrypted": false, "auto_adapt_places": false, "quote_field_type": "currency", "remove_mask_roles": {}, "description": "", "is_unique": false, "label": "引用字段-金额", "type": "quote", "quote_field": "field_ZEnbo__c__r.field_kXqA9__c", "is_required": false, "api_name": "field_e2d2P__c", "define_type": "custom", "_id": "64afc3d32519f70001fdd203", "is_single": false, "is_index_field": false, "index_name": "d_6", "is_show_mask": false, "help_text": "", "status": "new", "decimal_places": 7, "length": 7}, "is_deleted": {"describe_api_name": "object_3BbVb__c", "is_index": false, "create_time": 1610014319834, "is_unique": false, "description": "is_deleted", "default_value": false, "label": "is_deleted", "type": "true_or_false", "is_need_convert": false, "is_required": false, "api_name": "is_deleted", "options": [], "define_type": "system", "index_name": "is_del", "status": "released"}, "field_dy79C__c": {"describe_api_name": "object_3BbVb__c", "is_index": true, "is_active": true, "create_time": 1688632314786, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "default_value": false, "label": "关闭标识", "type": "true_or_false", "field_num": 20, "is_required": false, "enable_clone": true, "api_name": "field_dy79C__c", "options": [{"label": "是", "value": true}, {"label": "否", "value": false}], "define_type": "custom", "_id": "64a67bfa6087bb00010fe8ff", "is_single": false, "is_index_field": false, "index_name": "b_1", "help_text": "", "status": "new"}, "life_status_before_invalid": {"describe_api_name": "object_3BbVb__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "description": "作废前生命状态", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "label_r": "作废前生命状态", "index_name": "t_2", "max_length": 256, "is_index": false, "is_active": true, "create_time": 1610014319188, "is_encrypted": false, "default_value": "", "label": "作废前生命状态", "field_num": 9, "is_need_convert": false, "api_name": "life_status_before_invalid", "_id": "5ff6de6f599b3e0001803c1c", "is_index_field": false, "help_text": "", "status": "new"}, "object_describe_api_name": {"describe_api_name": "object_3BbVb__c", "is_index": false, "is_active": true, "create_time": 1610014319834, "pattern": "", "is_unique": false, "description": "object_describe_api_name", "label": "object_describe_api_name", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "object_describe_api_name", "define_type": "system", "index_name": "api_name", "max_length": 200, "status": "released"}, "owner_department": {"describe_api_name": "object_3BbVb__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": true, "label_r": "负责人主属部门", "index_name": "owner_dept", "max_length": 100, "is_index": true, "is_active": true, "create_time": 1610014319833, "is_encrypted": false, "default_value": "", "label": "负责人主属部门", "is_need_convert": false, "api_name": "owner_department", "_id": "5ff6de6f599b3e0001803c0c", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new"}, "out_owner": {"describe_api_name": "object_3BbVb__c", "is_index": true, "is_active": true, "create_time": 1610014319834, "is_unique": false, "label": "外部负责人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "out_owner", "define_type": "system", "is_single": true, "index_name": "o_owner", "config": {"display": 1}, "status": "released"}, "field_xga2D__c": {"describe_api_name": "object_3BbVb__c", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "type": "date", "default_to_zero": false, "is_required": false, "enable_clone": true, "define_type": "custom", "is_single": false, "index_name": "l_1", "is_index": true, "is_active": true, "create_time": 1643013488823, "is_encrypted": false, "default_value": "", "label": "日期", "time_zone": "GMT+8", "field_num": 15, "api_name": "field_xga2D__c", "date_format": "yyyy-MM-dd", "_id": "61ee65706698240001c8266c", "is_index_field": false, "help_text": "", "status": "new"}, "field_r10fy__c": {"expression_type": "js", "return_type": "currency", "describe_api_name": "object_3BbVb__c", "auto_adapt_places": false, "remove_mask_roles": {}, "is_unique": false, "type": "formula", "decimal_places": 2, "default_to_zero": true, "is_required": false, "define_type": "custom", "is_single": false, "index_name": "d_5", "is_index": true, "is_active": true, "expression": "$field_anp1B__c$", "create_time": 1689240451792, "is_encrypted": false, "label": "计算字段-金额", "field_num": 23, "api_name": "field_r10fy__c", "_id": "64afc3842519f70001fdcb9b", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new"}, "mc_functional_currency": {"describe_api_name": "object_3BbVb__c", "is_index": false, "is_active": true, "create_time": 1610014319848, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "default_value": "", "label": "本位币", "type": "select_one", "field_num": 11, "is_required": false, "api_name": "mc_functional_currency", "options": [{"label": "CNY - 人民币", "value": "CNY"}], "define_type": "package", "_id": "5ff6de6f599b3e0001803c1f", "is_single": false, "label_r": "本位币", "is_index_field": false, "index_name": "s_5", "config": {}, "help_text": "", "status": "new"}, "owner": {"describe_api_name": "object_3BbVb__c", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "type": "employee", "is_required": true, "wheres": [{"connector": "OR", "filters": [{"value_type": 0, "operator": "ISN", "field_name": "vice_departments", "field_values": []}]}], "define_type": "package", "is_single": true, "label_r": "负责人", "index_name": "owner", "is_index": true, "is_active": true, "create_time": 1610014319832, "is_encrypted": false, "default_value": "", "label": "负责人", "is_need_convert": false, "api_name": "owner", "_id": "5ff6de6f599b3e0001803c0b", "is_index_field": false, "help_text": "", "status": "new"}, "field_B0c4B__c": {"return_type": "currency", "describe_api_name": "object_3BbVb__c", "auto_adapt_places": false, "remove_mask_roles": {}, "is_unique": false, "type": "count", "decimal_places": 2, "sub_object_describe_apiname": "object_ssu7t__c", "is_required": false, "wheres": [], "define_type": "custom", "is_single": false, "index_name": "d_7", "field_api_name": "field_O1lbo__c", "is_index": true, "default_result": "d_null", "is_active": true, "create_time": 1689689497062, "is_encrypted": false, "count_type": "sum", "count_field_api_name": "field_6U0pa__c", "label": "汇总收款金额", "field_num": 25, "count_to_zero": false, "api_name": "field_B0c4B__c", "count_field_type": "currency", "_id": "64b69d992054fd00017498a2", "is_index_field": false, "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new"}, "field_Q51E3__c": {"describe_api_name": "object_3BbVb__c", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "where_type": "field", "type": "object_reference_many", "is_required": false, "wheres": [], "enable_clone": true, "define_type": "custom", "is_single": false, "index_name": "a_6", "is_index": true, "is_active": true, "create_time": 1688033677380, "is_encrypted": false, "default_value": "", "label": "查找关联(多选)", "target_api_name": "object_Uh1x4__c", "target_related_list_name": "target_related_list_AaXda__c", "field_num": 19, "target_related_list_label": "zxf-主", "action_on_target_delete": "set_null", "api_name": "field_Q51E3__c", "_id": "649d598e96389500017f507b", "is_index_field": false, "help_text": "", "status": "new", "max_num": 10, "is_open_display_name": true}, "lock_status": {"describe_api_name": "object_3BbVb__c", "default_is_expression": false, "auto_adapt_places": false, "description": "锁定状态", "is_unique": false, "type": "select_one", "default_to_zero": false, "is_required": false, "options": [{"label": "未锁定", "value": "0"}, {"label": "锁定", "value": "1"}], "define_type": "package", "is_single": false, "label_r": "锁定状态", "index_name": "s_4", "is_index": true, "is_active": true, "create_time": 1610014319187, "is_encrypted": false, "default_value": "0", "label": "锁定状态", "field_num": 3, "is_need_convert": false, "api_name": "lock_status", "_id": "5ff6de6f599b3e0001803c1a", "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "package": {"describe_api_name": "object_3BbVb__c", "is_index": false, "is_active": true, "create_time": 1610014319834, "pattern": "", "is_unique": false, "description": "package", "label": "package", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "package", "define_type": "system", "index_name": "pkg", "max_length": 200, "status": "released"}, "last_modified_time": {"describe_api_name": "object_3BbVb__c", "is_index": true, "create_time": 1610014319834, "is_unique": false, "description": "last_modified_time", "label": "最后修改时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "last_modified_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "index_name": "md_time", "status": "released"}, "create_time": {"describe_api_name": "object_3BbVb__c", "is_index": true, "create_time": 1610014319834, "is_unique": false, "description": "create_time", "label": "创建时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "create_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "index_name": "crt_time", "status": "released"}, "field_pD1cc__c": {"describe_api_name": "object_3BbVb__c", "auto_location": false, "auto_adapt_places": false, "description": "", "is_unique": false, "type": "location", "is_required": false, "enable_clone": true, "define_type": "custom", "is_single": false, "index_name": "t_5", "is_index": true, "is_active": true, "create_time": 1671534640473, "is_encrypted": false, "label": "定位", "is_geo_index": false, "field_num": 17, "api_name": "field_pD1cc__c", "range_limit": false, "_id": "63a19830fbab970001c33184", "radius_range": 100, "is_index_field": false, "help_text": "", "status": "new"}, "life_status": {"describe_api_name": "object_3BbVb__c", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "type": "select_one", "default_to_zero": false, "is_required": false, "options": [{"label": "未生效", "value": "ineffective"}, {"label": "审核中", "value": "under_review"}, {"label": "正常", "value": "normal"}, {"label": "变更中", "value": "in_change"}, {"label": "作废", "value": "invalid"}], "define_type": "package", "is_single": false, "label_r": "生命状态", "index_name": "s_3", "is_index": true, "is_active": true, "create_time": 1610014319840, "is_encrypted": false, "default_value": "normal", "label": "生命状态", "field_num": 4, "is_need_convert": false, "api_name": "life_status", "_id": "5ff6de6f599b3e0001803c13", "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "field_anp1B__c": {"describe_api_name": "object_3BbVb__c", "default_is_expression": false, "auto_adapt_places": false, "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "number", "decimal_places": 2, "default_to_zero": true, "is_required": false, "enable_clone": true, "define_type": "custom", "is_single": false, "index_name": "d_2", "max_length": 14, "is_index": true, "is_active": true, "create_time": 1639040616524, "is_encrypted": false, "display_style": "input", "step_value": 1, "length": 12, "default_value": "", "label": "数字", "field_num": 14, "api_name": "field_anp1B__c", "_id": "61b1c66863a33500016e8ddd", "is_index_field": false, "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new", "calculate_relation": {"relate_fields": {"object_3BbVb__c": [{"fieldName": "field_r10fy__c", "order": 1}, {"fieldName": "field_79Iq9__c", "order": 1}]}, "calculate_fields": {"object_3BbVb__c": ["field_79Iq9__c", "field_r10fy__c"]}}}, "last_modified_by": {"describe_api_name": "object_3BbVb__c", "is_index": true, "is_active": true, "create_time": 1610014319834, "is_unique": false, "label": "最后修改人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "last_modified_by", "define_type": "system", "is_single": true, "index_name": "md_by", "status": "released"}, "out_tenant_id": {"describe_api_name": "object_3BbVb__c", "is_index": false, "is_active": true, "create_time": 1610014319834, "pattern": "", "is_unique": false, "description": "out_tenant_id", "label": "外部企业", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "out_tenant_id", "define_type": "system", "index_name": "o_ei", "config": {"display": 0}, "max_length": 200, "status": "released"}, "mc_currency": {"describe_api_name": "object_3BbVb__c", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "type": "select_one", "default_to_zero": false, "is_required": false, "options": [{"not_usable": true, "label": "BBD - 巴巴多斯元", "value": "BBD"}, {"not_usable": true, "label": "ARS - 阿根廷比索", "value": "ARS"}, {"not_usable": true, "label": "BGN - Bulgarian Lev", "value": "BGN"}, {"not_usable": true, "label": "AOA - 安哥拉宽扎", "value": "AOA"}, {"not_usable": true, "label": "AZN - 阿塞拜疆马纳特", "value": "AZN"}, {"not_usable": true, "label": "ANG - 荷属安地列斯盾", "value": "ANG"}, {"not_usable": true, "label": "AUD - Australian Dollar", "value": "AUD"}, {"label": "BDT - 孟加拉国塔卡", "value": "BDT"}, {"label": "BHD - <PERSON><PERSON>", "value": "BHD"}, {"label": "AED - UAE Dirham", "value": "AED"}, {"label": "AWG - 阿鲁巴岛弗罗林", "value": "AWG"}, {"label": "USD - U.S. Dollar", "value": "USD"}, {"label": "AFN - Afghanistan Afghani (New)", "value": "AFN"}, {"label": "AMD - 亚美尼亚打兰", "value": "AMD"}, {"label": "ALL - 阿尔巴尼亚列克", "value": "ALL"}, {"label": "BAM - 自由兑换马克", "value": "BAM"}, {"label": "CNY - China Yuan", "value": "CNY"}], "define_type": "package", "is_single": false, "label_r": "币种", "index_name": "s_6", "is_index": true, "is_active": true, "create_time": 1610014319846, "is_encrypted": false, "default_value": "", "label": "币种", "field_num": 6, "api_name": "mc_currency", "_id": "5ff6de6f599b3e0001803c1d", "is_index_field": false, "config": {}, "help_text": "", "status": "new", "calculate_relation": {"relate_fields": {"object_3BbVb__c": [{"fieldName": "mc_exchange_rate_version", "order": 2}, {"fieldName": "mc_exchange_rate", "order": 2}, {"fieldName": "mc_functional_currency", "order": 2}], "object_Nc1Ek__c": [{"fieldName": "mc_exchange_rate_version", "order": 3}, {"fieldName": "mc_currency", "order": 2}, {"fieldName": "mc_exchange_rate", "order": 3}, {"fieldName": "mc_functional_currency", "order": 3}]}, "calculate_fields": {"object_3BbVb__c": ["mc_exchange_rate_version", "mc_functional_currency", "mc_exchange_rate"], "object_Nc1Ek__c": ["mc_exchange_rate_version", "mc_functional_currency", "mc_currency", "mc_exchange_rate"]}}}, "version": {"describe_api_name": "object_3BbVb__c", "is_index": false, "create_time": 1610014319834, "length": 8, "is_unique": false, "description": "version", "label": "version", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "version", "define_type": "system", "index_name": "version", "round_mode": 4, "status": "released"}, "created_by": {"describe_api_name": "object_3BbVb__c", "is_index": true, "is_active": true, "create_time": 1610014319834, "is_unique": false, "label": "创建人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "created_by", "define_type": "system", "is_single": true, "index_name": "crt_by", "status": "released"}, "relevant_team": {"describe_api_name": "object_3BbVb__c", "embedded_fields": {"teamMemberEmployee": {"is_index": true, "is_need_convert": true, "is_required": false, "api_name": "teamMemberEmployee", "is_unique": false, "define_type": "package", "description": "成员员工", "label": "成员员工", "type": "employee", "is_single": true, "help_text": "成员员工"}, "teamMemberRole": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberRole", "options": [{"label": "负责人", "value": "1"}, {"label": "普通成员", "value": "4"}], "is_unique": false, "define_type": "package", "description": "成员角色", "label": "成员角色", "type": "select_one", "help_text": "成员角色"}, "teamMemberPermissionType": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberPermissionType", "options": [{"label": "只读", "value": "1"}, {"label": "读写", "value": "2"}], "is_unique": false, "define_type": "package", "description": "成员权限类型", "label": "成员权限类型", "type": "select_one", "help_text": "成员权限类型"}}, "is_index": true, "is_active": true, "create_time": 1610014319845, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "label": "相关团队", "type": "embedded_object_list", "is_need_convert": false, "is_required": false, "api_name": "relevant_team", "define_type": "package", "_id": "5ff6de6f599b3e0001803c18", "is_single": false, "label_r": "相关团队", "is_index_field": false, "index_name": "a_team", "help_text": "相关团队", "status": "new"}, "record_type": {"describe_api_name": "object_3BbVb__c", "is_index": true, "is_active": true, "create_time": 1610014319839, "is_encrypted": false, "auto_adapt_places": false, "description": "record_type", "is_unique": false, "label": "业务类型", "type": "record_type", "is_need_convert": false, "is_required": false, "api_name": "record_type", "options": [{"is_active": true, "api_name": "default__c", "description": "预设业务类型", "label": "预设业务类型", "value": "default__c"}, {"is_active": true, "api_name": "record_2PqA1__c", "label": "业务类型(示例)", "value": "record_2PqA1__c"}], "define_type": "package", "_id": "5ff6de6f599b3e0001803c12", "is_single": false, "label_r": "业务类型", "is_index_field": false, "index_name": "r_type", "config": {}, "help_text": "", "status": "released"}, "field_2sXf1__c": {"describe_api_name": "object_3BbVb__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "description": "", "is_unique": false, "type": "long_text", "default_to_zero": false, "is_required": false, "enable_clone": true, "define_type": "custom", "is_single": false, "index_name": "t_1", "max_length": 2000, "is_index": true, "is_active": true, "create_time": 1639040594143, "is_encrypted": false, "min_length": 0, "default_value": "", "label": "多行文本", "field_num": 12, "api_name": "field_2sXf1__c", "_id": "61b1c65263a33500016e8cc8", "is_index_field": false, "help_text": "", "status": "new"}, "field_ZEnbo__c": {"describe_api_name": "object_3BbVb__c", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "where_type": "field", "type": "object_reference", "relation_outer_data_privilege": "not_related", "related_where_type": "", "is_required": false, "wheres": [], "enable_clone": true, "define_type": "custom", "input_mode": "", "is_single": false, "index_name": "s_8", "is_index": true, "is_active": true, "create_time": 1689240506592, "is_encrypted": false, "default_value": "", "label": "查找关联-zxf", "target_api_name": "object_Uh1x4__c", "target_related_list_name": "target_related_list_2Jpd0__c", "field_num": 24, "target_related_list_label": "zxf", "action_on_target_delete": "set_null", "related_wheres": [], "api_name": "field_ZEnbo__c", "_id": "64afc3ba2519f70001fdcef4", "is_index_field": true, "help_text": "", "status": "new", "is_open_display_name": true, "calculate_relation": {"relate_fields": {"object_3BbVb__c": [{"fieldName": "field_e2d2P__c", "order": 1000}]}, "calculate_fields": {"object_3BbVb__c": ["field_e2d2P__c"]}}}, "field_Z1phM__c": {"describe_api_name": "object_3BbVb__c", "default_is_expression": false, "auto_adapt_places": false, "remove_mask_roles": {}, "is_unique": false, "type": "currency", "decimal_places": 2, "default_to_zero": true, "is_required": false, "enable_clone": true, "define_type": "custom", "is_single": false, "index_name": "d_3", "max_length": 14, "is_index": true, "is_active": true, "create_time": 1689240379635, "is_encrypted": false, "length": 12, "default_value": "", "label": "金额", "currency_unit": "￥", "field_num": 21, "api_name": "field_Z1phM__c", "_id": "64afc33b2519f70001fdbcc5", "is_index_field": false, "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new"}, "data_own_department": {"describe_api_name": "object_3BbVb__c", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "type": "department", "is_required": false, "wheres": [], "define_type": "package", "is_single": true, "label_r": "归属部门", "index_name": "data_owner_dept_id", "is_index": true, "is_active": true, "create_time": 1610014319834, "is_encrypted": false, "default_value": "", "label": "归属部门", "is_need_convert": false, "api_name": "data_own_department", "_id": "62566e311287fd000163f7e7", "is_index_field": false, "help_text": "", "status": "released"}, "field_79Iq9__c": {"expression_type": "js", "return_type": "number", "describe_api_name": "object_3BbVb__c", "auto_adapt_places": false, "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "formula", "decimal_places": 2, "default_to_zero": true, "is_required": false, "define_type": "custom", "is_single": false, "index_name": "d_4", "is_index": true, "is_active": true, "expression": "$field_anp1B__c$", "create_time": 1689240408498, "is_encrypted": false, "label": "计算字段-数字", "field_num": 22, "api_name": "field_79Iq9__c", "_id": "64afc3592519f70001fdc171", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new"}, "name": {"describe_api_name": "object_3BbVb__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "name", "is_unique": true, "type": "text", "default_to_zero": false, "is_required": true, "define_type": "system", "input_mode": "", "is_single": false, "label_r": "主属性", "index_name": "name", "max_length": 100, "is_index": true, "is_active": true, "create_time": 1610014327870, "is_encrypted": false, "default_value": "", "label": "主属性", "api_name": "name", "_id": "5ff6de6f599b3e0001803c0a", "is_index_field": false, "is_show_mask": false, "help_text": "", "status": "new"}, "order_by": {"describe_api_name": "object_3BbVb__c", "is_index": false, "create_time": 1610014319834, "length": 8, "is_unique": false, "description": "order_by", "label": "order_by", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "order_by", "define_type": "system", "index_name": "l_by", "round_mode": 4, "status": "released"}, "field_22eNM__c": {"describe_api_name": "object_3BbVb__c", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "type": "select_one", "default_to_zero": false, "is_required": false, "enable_clone": true, "options": [{"label": "1", "value": "KQ7o2uwS7"}, {"label": "2", "value": "vPNnnj9Qm"}, {"label": "3", "value": "tvgsB14F5"}, {"label": "4", "value": "pPny8e2eQ"}, {"not_usable": true, "label": "其他", "value": "other"}], "define_type": "custom", "is_single": false, "index_name": "s_7", "is_index": true, "is_active": true, "create_time": 1639040611376, "is_encrypted": false, "default_value": "", "label": "单选", "field_num": 13, "api_name": "field_22eNM__c", "_id": "61b1c66363a33500016e8dda", "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "mc_exchange_rate_version": {"describe_api_name": "object_3BbVb__c", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "label_r": "汇率版本", "index_name": "t_3", "max_length": 256, "is_index": false, "is_active": true, "create_time": 1610014319849, "is_encrypted": false, "default_value": "", "label": "汇率版本", "field_num": 10, "api_name": "mc_exchange_rate_version", "_id": "5ff6de6f599b3e0001803c20", "is_index_field": false, "help_text": "", "status": "new"}, "_id": {"describe_api_name": "object_3BbVb__c", "is_index": false, "is_active": true, "create_time": 1610014319834, "pattern": "", "is_unique": false, "description": "_id", "label": "_id", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "_id", "define_type": "system", "index_name": "_id", "max_length": 200, "status": "released"}}, "release_version": "6.4", "actions": {}, "calculate_relation": {"relate_fields": {"object_3BbVb__c": [{"fieldName": "mc_exchange_rate_version", "order": 2}, {"fieldName": "mc_exchange_rate", "order": 2}, {"fieldName": "mc_functional_currency", "order": 2}]}, "calculate_fields": {"object_3BbVb__c": ["mc_exchange_rate_version", "mc_functional_currency", "mc_exchange_rate"]}}}