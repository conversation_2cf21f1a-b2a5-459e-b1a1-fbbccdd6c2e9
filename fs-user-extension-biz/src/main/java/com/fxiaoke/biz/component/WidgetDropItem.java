package com.fxiaoke.biz.component;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.webpage.customer.core.component.Component;
import com.facishare.webpage.customer.core.config.ComponentNameConfig;
import com.fxiaoke.api.constant.LayoutField;
import com.fxiaoke.biz.model.ComponentVO;
import com.fxiaoke.biz.model.WidgetVO;
import com.fxiaoke.biz.utils.DropItemTypeUtils;
import com.google.common.base.Strings;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

/**
 * Created by zhangyu on 2020/6/3
 */
@Data
@AllArgsConstructor
public class WidgetDropItem extends Component {

    private ComponentVO componentVO;

    private ComponentNameConfig componentNameConfig;

    @Override
    public String getId() {
        return componentVO.getId();
    }

    @Override
    public String getPId() {
        return componentVO.getParentId();
    }

    @Override
    public String getIcon(){
        WidgetVO widget = componentVO.getWidget();
        return widget.getIcon();
    }

    @Override
    public String getName() {
        WidgetVO widget = componentVO.getWidget();
        if (widget != null) {
            return widget.getName();
        }
        return componentVO.getTitle();
    }

    @Override
    public String getDropItemType() {
        return DropItemTypeUtils.dropListCompType;
    }

    @Override
    public String getApiName() {
        WidgetVO widget = componentVO.getWidget();
        if (widget == null){
            return null;
        }
        if (widget.getCardId() == null) {
            return widget.getId();
        }
        if (Strings.isNullOrEmpty(widget.getAppId())) {
            return widget.getCardId();
        }
        return widget.getCardId() + "-" + widget.getAppId();

    }

    @Override
    public String getType() {
        WidgetVO widget = componentVO.getWidget();
        if (Strings.isNullOrEmpty(widget.getCardId())) {
            return widget.getId();
        }
        return componentNameConfig.getComponentName(widget.getCardId(), widget.getWidgetType());
    }

    @Override
    public int getLimit() {
        WidgetVO widget = componentVO.getWidget();
        return widget.getLimit();
    }

    @Override
    public int getGrayLimit() {
        return 0;
    }


    @Override
    public JSONObject getProps() {
        JSONObject jsonObject = new JSONObject();

        WidgetVO widget = componentVO.getWidget();

        if (StringUtils.isNotEmpty(widget.getCardId())) {
            jsonObject.put("cardId", widget.getCardId());
            jsonObject.put("dataId", widget.getCardId());
            jsonObject.put("title", getName());
        }

        jsonObject.put("api_name", getApiName());
        jsonObject.put("header", getName());
        jsonObject.put("limit", getLimit());
        jsonObject.put("type", widget.getWidgetType());
        jsonObject.put("appId", widget.getAppId());
        if (widget.getHeight() != 0) {
            JSONObject styleJson = new JSONObject();
            styleJson.put("height", widget.getHeight() + "px");
            jsonObject.put("style", styleJson);
        }
        if (widget.getId().equals("slideImage")) {
            JSONArray jsonArray = new JSONArray();
            jsonObject.put(LayoutField.IMGS, jsonArray);
        }

        if (widget.getExtProp() != null) {
            jsonObject.putAll(widget.getExtProp());
        }
        if (widget.getWidgetScope() != null){
            jsonObject.putAll(widget.getWidgetScope());
        }
        return jsonObject;
    }

}
