package com.fxiaoke.biz.service;

import com.alibaba.fastjson.JSONObject;
import com.facishare.qixin.common.monitor.GlobalStopWatch;
import com.facishare.qixin.common.monitor.SlowLog;
import com.facishare.qixin.i18n.QixinI18nService;
import com.facishare.qixin.i18n.model.Key;
import com.facishare.webpage.customer.api.constant.BizType;
import com.facishare.webpage.customer.api.exception.ValidateException;
import com.facishare.webpage.customer.api.model.SimpleLinkAppVO;
import com.facishare.webpage.customer.api.model.arg.GetLinkAppByAppIdArg;
import com.facishare.webpage.customer.api.model.arg.QueryMenusByCollectionIds;
import com.facishare.webpage.customer.api.model.core.Menu;
import com.facishare.webpage.customer.api.service.LinkAppRestService;
import com.facishare.webpage.customer.api.service.MenusRegisterRestService;
import com.facishare.webpage.customer.core.business.ComponentListManager;
import com.facishare.webpage.customer.core.config.MenusConfig;
import com.facishare.webpage.customer.core.config.PageTemplateConfig;
import com.facishare.webpage.customer.core.model.ComponentDto;
import com.facishare.webpage.customer.core.model.ComponentTypConst;
import com.facishare.webpage.customer.core.model.DropListItem;
import com.facishare.webpage.customer.core.model.Widget;
import com.fxiaoke.api.model.FullMenu;
import com.fxiaoke.api.model.component.Component;
import com.fxiaoke.api.model.type.MenuType;
import com.fxiaoke.biz.component.ComponentWrapper;
import com.fxiaoke.biz.config.CrmObjectConfig;
import com.fxiaoke.biz.config.UiPaasConfig;
import com.fxiaoke.biz.constant.Constant;
import com.fxiaoke.biz.convert.MenuConvert;
import com.fxiaoke.biz.model.ComponentVO;
import com.fxiaoke.biz.model.MenusContext;
import com.fxiaoke.biz.model.PageMenuDropInfo;
import com.fxiaoke.biz.model.QueryMenusArg;
import com.fxiaoke.biz.util.GraySwitch;
import com.fxiaoke.biz.utils.ApiNamesUtils;
import com.fxiaoke.biz.utils.DropListAppId;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.fxiaoke.biz.utils.I18nUtils.NO_MENU_PERMISSION;


/**
 * Created by shecheng on 19/12/21.
 */

@Service("dropListService")
public class DropListServiceImpl implements DropListService {

    private static final Logger log = LoggerFactory.getLogger(DropListServiceImpl.class);
    @Resource
    private MenusManager menusManager;
    @Resource
    private ComponentListManager componentListManager;
    @Resource
    private ComponentListManager webMenuListManager;
    @Resource
    private AppBlackListConfig appBlackListConfig;
    @Resource
    private QixinI18nService qixinI18nService;
    @Resource
    private CrossAppIdListConfig crossAppIdListConfig;
    @Resource
    private DropListItemConvertService dropListItemConvertService;
    @Resource
    private UiPaasConfig uiPaasConfig;
    @Resource
    private PageTemplateConfig pageTemplateConfig;
    @Resource
    private MenusRegisterRestService menusRegisterRestService;
    @Resource
    private LinkAppRestService linkAppRestService;
    @Resource
    private UserFunctionPermissionService userFunctionPermissionService;
    @Autowired
    private CrmObjectConfig crmObjectConfig;
    @Autowired
    private DescribeService describeService;
    @Autowired
    private RecordTyeService recordTyeService;
    @Autowired
    private NewMenuService newMenuService;
    @Resource
    private MenusConfig menusConfig;

    @Autowired
    QueryAppInfoService queryAppInfoService;


    private final String CONFIG_SUFFIX = "pageMenu";

    private List<String> filterMenuCollectionIds(List<ComponentDto> appComponentList) {
        return appComponentList.stream().filter(componentDto -> ComponentTypConst.MENU_COLLECTION_TYPE == componentDto.getComponentType())
                .map(componentDto -> componentDto.getId()).collect(Collectors.toList());
    }

    private Map<String, List<Menu>> queryRegisterMenus(int enterpriseId, List<ComponentDto> appComponentList) {
        List<String> collectionIds = filterMenuCollectionIds(appComponentList);
        QueryMenusByCollectionIds.Arg arg = new QueryMenusByCollectionIds.Arg();
        arg.setTenantId(enterpriseId);
        arg.setCollectionIds(collectionIds);
        QueryMenusByCollectionIds.Result result = menusRegisterRestService.queryMenusByCollectionIds(String.valueOf(enterpriseId), arg);
        return result.getMenus();
    }

    private Map<String, String> queryLanguageMenus(int enterpriseId, List<ComponentDto> componentDtos, Locale locale) {
        List<Key> keyCollection = Lists.newArrayList();
        componentDtos.stream().forEach(componentDto -> {
            if (!Strings.isNullOrEmpty(componentDto.getTitleI18nKey())) {
                Key key = new Key(componentDto.getTitleI18nKey(), componentDto.getTitle());
                keyCollection.add(key);
            }
            if (componentDto.getMenus() != null) {
                List<Key> menusKey = componentDto.getMenus().stream().filter(menu -> !Strings.isNullOrEmpty(menu.getNameI18nKey())).map(menu -> new Key(menu.getNameI18nKey(), menu.getName())).collect(Collectors.toList());
                keyCollection.addAll(menusKey);
            }
            Widget widget = componentDto.getWidget();
            if (widget != null && !Strings.isNullOrEmpty(widget.getNameI18nKey())) {
                keyCollection.add(new Key(widget.getNameI18nKey(), widget.getName()));
            }
        });
        Map<String, String> i118Result = qixinI18nService.getMultiI18nValueDefault(enterpriseId, keyCollection, locale);
        return i118Result;
    }

    private void setMultiLanguageMenus(int enterpriseId, List<ComponentDto> componentDtos, Locale locale) {
        Map<String, String> i118Result = queryLanguageMenus(enterpriseId, componentDtos, locale);
        componentDtos.stream().forEach(componentDto -> {
            if (componentDto.getTitleI18nKey() != null) {
                String title = i118Result.get(componentDto.getTitleI18nKey());
                if (!Strings.isNullOrEmpty(title)) {
                    componentDto.setTitle(title);
                }
            }
            if (componentDto.getMenus() != null) {
                componentDto.getMenus().stream().forEach(menu -> {
                    if (menu.getNameI18nKey() != null) {
                        String name = i118Result.get(menu.getNameI18nKey());
                        if (!Strings.isNullOrEmpty(name)) {
                            menu.setName(name);
                        }
                    }
                });
            }
            Widget widget = componentDto.getWidget();
            if (widget != null && widget.getNameI18nKey() != null) {
                String name = i118Result.get(widget.getNameI18nKey());
                if (!Strings.isNullOrEmpty(name)) {
                    widget.setName(name);
                }
            }
        });
    }

    @Override
    public List<ComponentVO> getDropList(int enterpriseId, String enterpriseAccount, int employeeId, String appId, Locale locale) {
        return getDropList(enterpriseId, enterpriseAccount, employeeId, appId, locale, appId, null);
    }

    @Override
    public List<ComponentVO> getDropList(int enterpriseId, String enterpriseAccount, int employeeId, String appId,
                                         Locale locale, String configAppId, String customerLinkAppId) {
        SlowLog slowLog = GlobalStopWatch.create("getDropList", 100L);
        //读取组件的配置文件, 并将collection中的 menus 扁平化
        List<ComponentDto> componentDtos = componentListManager.getComponentDtoListByAppId(configAppId);
        slowLog.lap("getComponentDtoListByAppId");

        QueryMenusArg menusArg = buildQueryMenusArg(appId, enterpriseId, enterpriseAccount, employeeId, locale);
        Map<String, List<Menu>> registerMenus = queryRegisterMenus(enterpriseId, componentDtos);    // 按照collectionId查询自定义菜单项
        slowLog.lap("queryRegisterMenus");

        componentDtos = componentListManager.mergeRegisterComponentDtoListByAppId(configAppId, registerMenus);  // merge补充[自定义菜单项]这一分组(菜单才会有), 其余逻辑和 collection  menus 扁平化一样
        slowLog.lap("mergeRegisterComponentDtoListByAppId");

        setMultiLanguageMenus(enterpriseId, componentDtos, locale);
        slowLog.lap("setMultiLanguageMenus");
        switch (appId) {
            case DropListAppId.VENDOR:
                menusArg.setCross(true);
                break;
            default:
                break;
        }
        menusArg.setCross(pageTemplateConfig.checkCrossApp(appId));

        if (crossAppIdListConfig.isCrossApp(appId) || StringUtils.isNotEmpty(customerLinkAppId)) {
            menusArg.setCross(true);
        }
        // DTO转化为VO, 同时补充了自定义组件等, 判断了license和灰度
        List<ComponentVO> componentVOS = menusManager.convert2ComponentVOList(menusArg, componentDtos, appId, slowLog, customerLinkAppId);
        slowLog.stop("getDropList over");

        return componentVOS;
    }

    @Override
    public List<DropListItem> getPageMenuDropList(int enterpriseId, String enterpriseAccount, int employeeId, String appId, String business,
                                                  Locale locale, String customerLinkAppId) {
        SlowLog slowLog = GlobalStopWatch.create("getDropList", 100L);
        //读取组件的配置文件, 并将collection中的 menus 扁平化
        List<ComponentDto> componentDtos = webMenuListManager.getComponentDtoListByAppId(getConfigKey(appId, business));
        slowLog.lap("getComponentDtoListByAppId");

        //应用的为空的话，则默认走base
        if (CollectionUtils.isEmpty(componentDtos)) {
            componentDtos.addAll(webMenuListManager.getComponentDtoListByAppId(getConfigKey("base", business)));
        }

        //查询自定义菜单项（看逻辑是配置中没有配的，但是数据库里存了，所以要查出来，在补回去）
        QueryMenusArg menusArg = buildQueryMenusArg(appId, enterpriseId, enterpriseAccount, employeeId, locale);
        menusArg.setClientType(com.facishare.webpage.customer.core.constant.Constant.WEB);
        Map<String, List<Menu>> registerMenus = queryRegisterMenus(enterpriseId, componentDtos);    // 按照collectionId查询自定义菜单项
        slowLog.lap("queryRegisterMenus");


        componentDtos = webMenuListManager.mergeRegisterComponentDtoListByAppId(getConfigKey(appId, business), registerMenus);  // merge补充[自定义菜单项]这一分组(菜单才会有), 其余逻辑和 collection  menus 扁平化一样
        if (CollectionUtils.isEmpty(componentDtos)) {
            componentDtos.addAll(webMenuListManager.getComponentDtoListByAppId(getConfigKey("base", business)));
        }

        slowLog.lap("mergeRegisterComponentDtoListByAppId");

        setMultiLanguageMenus(enterpriseId, componentDtos, locale);
        slowLog.lap("setMultiLanguageMenus");
        switch (appId) {
            case DropListAppId.VENDOR:
                menusArg.setCross(true);
                break;
            default:
                break;
        }
        menusArg.setCross(pageTemplateConfig.checkCrossApp(appId));

        if (crossAppIdListConfig.isCrossApp(appId) || StringUtils.isNotEmpty(customerLinkAppId)) {
            menusArg.setCross(true);
        }
        // DTO转化为VO, 同时补充了自定义组件等, 判断了license和灰度
        List<ComponentVO> componentVOS = menusManager.convert2ComponentVOList(menusArg, componentDtos, appId, slowLog, customerLinkAppId);
        slowLog.stop("getDropList over");
        List<DropListItem> listItems = Lists.newArrayList();
        for (ComponentVO componentVO : componentVOS) {

            List<com.facishare.webpage.customer.core.component.Component> vos = dropListItemConvertService.buildComponentVo(componentVO); // flatMap, collection -> widgets
            List<DropListItem> items = vos.stream().map(com.facishare.webpage.customer.core.component.Component::buildDropListItem).collect(Collectors.toList());
            listItems.addAll(items);
        }
        return listItems;
    }

    private String getConfigKey(String appId, String businessCode) {
        return appId + "-" + businessCode + "-" + CONFIG_SUFFIX;
    }


    private QueryMenusArg buildQueryMenusArg(String appId, int enterpriseId, String enterpriseAccount, int employeeId, Locale locale) {
        QueryMenusArg queryMenusArg = new QueryMenusArg();
        queryMenusArg.setAppId(appId);
        queryMenusArg.setEnterpriseId(enterpriseId);
        queryMenusArg.setEmployeeId(employeeId);
        queryMenusArg.setEnterpriseAccount(enterpriseAccount);
        queryMenusArg.setLocale(locale);
        return queryMenusArg;
    }

    @Override
    public List<DropListItem> getDropListV2(int enterpriseId, String enterpriseAccount, int employeeId, String appId,
                                            String customerLinkAppId, Locale locale, int appType) {
        String configId = appId + "-V2";
        String grayUseV3DropList = String.join("-", GraySwitch.USE_V3_DROP_LIST, appId);
        if (uiPaasConfig.useV3DropList(appId) && GraySwitch.isAllowByBusiness(grayUseV3DropList, enterpriseId)) {
            configId = appId + "-V3";
        }
        return getDropListItems(enterpriseId, enterpriseAccount, employeeId, appId, locale, configId, customerLinkAppId, appType);
    }

    // 独立站点, 按照降级逻辑取得正确的 configAppId
    private String getWebSiteConfigAppId(String appId, String configAppId, String customerLinkAppId, int appType) {
        BizType bizTypeValue = BizType.getBizTypeValue(appType);    // 真正的页面类型
        if (bizTypeValue == null) {
            return configAppId;
        }

        String realAppId = Constant.CROSS_PaaS.equals(appId) ? customerLinkAppId : appId;   // 接口最开始对这两个参数进行了兼容处理, 独立站点需要取得真实的互联应用id
        String v2 = "V2";  // 没有意义, 只是为了和之前app端的key样式保持一致
        String separator = com.facishare.webpage.customer.api.constant.Constant.SEPARATOR;

        // 该页面属于特殊页面, 该应用的特殊页面灰度使用本应用独立的配置,且配置存在
        configAppId = String.join(separator, bizTypeValue.getDefaultAppId(), realAppId, v2);
        if (BizType.isSpecialWebsite(appType) && !componentListManager.isComponentEmpty(configAppId)) {
            return configAppId;
        }

        // 该页面属于特殊页面, 使用这类页面的通用配置, 且配置存在
        configAppId = String.join(separator, bizTypeValue.getDefaultAppId(), v2);
        if (BizType.isSpecialWebsite(appType) && !componentListManager.isComponentEmpty(configAppId)) {
            return configAppId;
        }

        // 该应用的站点页面使用应用单独的, 且存在这样的配置
        configAppId = String.join(separator, BizType.WEBSITE.getDefaultAppId(), realAppId, v2);
        if (!componentListManager.isComponentEmpty(configAppId)) {
            return configAppId;
        }

        // C端门户页面通用的drop
        configAppId = String.join(separator, BizType.WEBSITE.getDefaultAppId(), v2);
        return configAppId;
    }

    private List<DropListItem> getDropListItems(int enterpriseId, String enterpriseAccount, int employeeId,
                                                String appId, Locale locale, String configAppId, String customerLinkAppId, int appType
    ) {
        if (BizType.isWebsite(appType)) {
            configAppId = getWebSiteConfigAppId(appId, configAppId, customerLinkAppId, appType);
        }
        return getDropListItems(enterpriseId, enterpriseAccount, employeeId, appId, locale, configAppId, customerLinkAppId);
    }


    @NotNull    // app页面设计器的 菜单入口/组件 drop, 页面底导航数据与菜单入口一致
    private List<DropListItem> getDropListItems(int enterpriseId,
                                                String enterpriseAccount,
                                                int employeeId,
                                                String appId,   // 前端传递的 appid 或者 如果是自定义互联应用, 是
                                                Locale locale,
                                                String configAppId, // 配置文件key
                                                String customerLinkAppId    // 自定义互联应用的appId
    ) {
        log.info("getDropListItems, appId:{}, configAppId:{}, customerLinkAppId:{}", appId, configAppId, customerLinkAppId);
        List<ComponentVO> componentVOs = getDropList(enterpriseId, enterpriseAccount, employeeId, appId, locale, configAppId, customerLinkAppId);
        List<DropListItem> dropListItems = Lists.newArrayList();

        for (ComponentVO componentVO : componentVOs) {
            if ("filters".equals(componentVO.getId()) && !GraySwitch.isAllowByBusiness(GraySwitch.Filter, enterpriseId)) {
                continue;
            }
            List<com.facishare.webpage.customer.core.component.Component> vos = dropListItemConvertService.buildComponentVo(componentVO); // flatMap, collection -> widgets
            List<DropListItem> items = vos.stream().map(com.facishare.webpage.customer.core.component.Component::buildDropListItem).collect(Collectors.toList());
            dropListItems.addAll(items);
        }

        // 灰度过滤
        return dropListItems.stream().filter(dropListItem ->
                !appBlackListConfig.isGrayComponent(dropListItem.getId()) || GraySwitch.isAllowByBusiness(dropListItem.getId(), enterpriseAccount)
        ).collect(Collectors.toList());
    }

    @Override
    public List<DropListItem> getAppMenuDropList(int enterpriseId, String enterpriseAccount, int employeeId, String appId, Locale locale, String customerLinkAppId) {
        String appIdV2 = appId + "-menuEntry";
        return getDropListItems(enterpriseId, enterpriseAccount, employeeId, appId, locale, appIdV2, customerLinkAppId);
    }


    @Override
    public List<DropListItem> getUserAppMenuDropList(int enterpriseId, String enterpriseAccount, int employeeId, String appId, Locale locale) {
        String configAppId = String.join("-", "AppUserMenu", appId);
        return getDropListItems(enterpriseId, enterpriseAccount, employeeId, appId, locale, configAppId, null);
    }

    @Override
    public List<DropListItem> getMenuTemplateDropList(int enterpriseId, String enterpriseAccount, int employeeId, String appId, Locale locale) {
        return getDropListItems(enterpriseId, enterpriseAccount, employeeId, appId, locale, appId, null);
    }

    @Override
    public String getConfigAppId(String sourceType, int enterpriseId) {
        String configAppId = DropListAppId.getDropId(sourceType);
        if ("vendor".equalsIgnoreCase(configAppId)) {
            configAppId = crossAppIdListConfig.getVendorAppId();
        }
        if (StringUtils.isNotEmpty(configAppId) && configAppId.startsWith(DropListAppId.CROSS_APPID_PRE)) {
            GetLinkAppByAppIdArg getLinkAppByAppIdArg = new GetLinkAppByAppIdArg();
            getLinkAppByAppIdArg.setAppId(configAppId);
            getLinkAppByAppIdArg.setType(2);
            Map<String, String> headers = new HashMap<>();
            headers.put("x-fs-ei", String.valueOf(enterpriseId));
            SimpleLinkAppVO simpleLinkAppVO = linkAppRestService.getlinkAppVOByLinkAppId(headers, getLinkAppByAppIdArg);
            if (simpleLinkAppVO != null && simpleLinkAppVO.getType() == 2) {
                configAppId = Constant.CROSS_PaaS;
            }
        }
        return configAppId;
    }

    @Override
    public Component getPageMenuDropInfo(PageMenuDropInfo pageMenuDropInfo) {
        JSONObject componentJsonObj = pageMenuDropInfo.getComponent();
        Component component = new Component(componentJsonObj);
        String menuId = component.getApiName();

        List<Menu> menuList = menusConfig.getMenusByIds(Lists.newArrayList(menuId));

        //补充菜单配置中 不漏出的的端的列表
        if (CollectionUtils.isNotEmpty(menuList)) {
            Menu menu = menuList.get(0);
            if (Objects.nonNull(menu)) {
                List<String> clientTypeBlackList = menu.getClientTypeBlackList();
                component.setClientTypeBlackList(com.facishare.webpage.customer.core.util.CollectionUtils.nullToEmpty(clientTypeBlackList));
            }
        }
        //过滤组件菜单的权限
        List<Component> components = userFunctionPermissionService.filterEnableComponent(pageMenuDropInfo.getTenantId(),
                pageMenuDropInfo.getUserId(), pageMenuDropInfo.getOutTenantId(), pageMenuDropInfo.getOutUserId(),
                pageMenuDropInfo.getAppId(), Lists.newArrayList(component));
        if (CollectionUtils.isEmpty(components)) {
            throw ValidateException.fromI18N(NO_MENU_PERMISSION);
        }
        List<Component> menuComponents = components.stream()
                //过滤根据所在的端过滤菜单
                .filter(x -> x.getType().equals(Component.TYPE_MENU) && (
                                // 用clientTypeBlackList 过滤菜单
                                CollectionUtils.isEmpty(x.getClientTypeBlackList()) || (CollectionUtils.isNotEmpty(x.getClientTypeBlackList()) && !x.getClientTypeBlackList().contains(pageMenuDropInfo.getAdvanceClientType()))
                        )
                )
                //新建对象需要根据该配置过滤fs-ui-paas-add-object-config
                .filter(x -> !(
                                x.getMenuType() == MenuType.NEW_CREATE_OBJECT
                                        &&
                                        //先根据配置文件 过滤一下新建对象  如果action配置的是固定值 "objectNoAction"  则不下发该对象
                                        Constant.OBJECT_NO_ACTION.equals(
                                                crmObjectConfig.getURLFormAddObjectFilterConfig(ApiNamesUtils.getObjectApiName(x.getApiName()), x.getRecordTypeApiName(), pageMenuDropInfo.getAppId(), pageMenuDropInfo.getTenantId(),
                                                        pageMenuDropInfo.getUserId(), pageMenuDropInfo.getClientType().getValue(), pageMenuDropInfo.getAdvanceClientType(), pageMenuDropInfo.getClientVersion(), null, 0, null)
                                        )
                        )
                )
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(menuComponents)) {
            throw ValidateException.fromI18N(NO_MENU_PERMISSION);
        }

        List<com.fxiaoke.api.model.Menu> menus = menuComponents.stream().map(x -> {
            ComponentWrapper componentWrapper = new ComponentWrapper(x);
            return componentWrapper.buildMenu();
        }).collect(Collectors.toList());
        //menuContext 里面有iconType数据
        com.fxiaoke.api.model.Menu menu = menus.get(0);
        if (Objects.isNull(menu)) {
            return component;
        }
        Map<String, FullMenu> fullMenus = MenuConvert.convert2FullMenu(menus, pageMenuDropInfo.getClientType(), pageMenuDropInfo.getClientVersion(),
                        buildMenusContext(pageMenuDropInfo, menu))
                .stream().collect(Collectors.toMap(FullMenu::getValue, Function.identity(), (t1, t2) -> t2));

        FullMenu fullMenu = fullMenus.get(component.getApiName());
        if (Objects.isNull(fullMenu)) {
            throw ValidateException.fromI18N(NO_MENU_PERMISSION);
        }
        ComponentWrapper componentWrapper = new ComponentWrapper(component);
        componentWrapper.addComponentProperty(fullMenu, null);//赋值属性
        return componentWrapper.getComponent();
    }


    private MenusContext buildMenusContext(PageMenuDropInfo pageMenuDropInfo, com.fxiaoke.api.model.Menu menu) {

        Integer menuType = menu.getMenuType();

        MenusContext context = new MenusContext();
        context.setEnterpriseAccount(pageMenuDropInfo.getEnterpriseAccount());
        context.setEmployeeId(pageMenuDropInfo.getUserId());
        context.setEnterpriseId(pageMenuDropInfo.getTenantId());
        context.setLocale(pageMenuDropInfo.getLocale());
        context.setOutTenantId(pageMenuDropInfo.getOutTenantId());
        context.setAppId(pageMenuDropInfo.getAppId());

        switch (menuType) {
            case MenuType.PAAS_APP:
                context.setPaasAppMap(queryAppInfoService.queryUserPaasAppList(String.valueOf(pageMenuDropInfo.getTenantId()),
                        pageMenuDropInfo.getUserId(), pageMenuDropInfo.getLocale(), "web"));
                break;
            case MenuType.INNER_APP:
                context.setInnerApp(queryAppInfoService.queryWebInnerApp(pageMenuDropInfo.getEnterpriseAccount(), String.valueOf(pageMenuDropInfo.getTenantId()),
                        pageMenuDropInfo.getUserId(), pageMenuDropInfo.getLocale()));
                break;
            case MenuType.CRM:
            case MenuType.NEW_CREATE_OBJECT:
                context.setDescribes(describeService.queryDescribes(pageMenuDropInfo.getTenantId(), -10000, Lists.newArrayList(menu.getObjectApiName()), pageMenuDropInfo.getLocale()));
                context.setRecordTypeNames(recordTyeService.queryRecordTypes(pageMenuDropInfo.getTenantId(), Lists.newArrayList(menu), pageMenuDropInfo.getLocale()));
                break;
            case MenuType.ENTERPRISE_RELATION:
                context.setEnableApp(queryAppInfoService.batchGetCrossAppVos(pageMenuDropInfo.getEnterpriseAccount(), pageMenuDropInfo.getUserId(), Lists.newArrayList(menu),
                        pageMenuDropInfo.getTenantId(), pageMenuDropInfo.getAdvanceClientType(), pageMenuDropInfo.getClientVersion()));
                break;
            default:
                break;

        }
        if (menu.getMenuType() == MenuType.APP || menu.getMenuType() == MenuType.ENTERPRISE_RELATION) {
            context.setNewMenus(newMenuService.queryMenus(pageMenuDropInfo.getTenantId(), Sets.newHashSet(menu.getValue()), pageMenuDropInfo.getLocale()));
        }
        return context;
    }


}
