package com.fxiaoke.biz.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

public interface CreateOrUpdatePageTemplateVo {
    @Data
    class Arg implements Serializable {
        private String pageTemplateId;
        /**
         * 页面模板名称
         */
        private String name;

        /**
         * 普通图标(自定义页面,下拉框 使用)
         */
        private String icon;
        /**
         * 未选中图标
         */
        private String background;
        /**
         * 选中图标
         */
        private String selectIcon;

        /**
         * 设置的Icon类型
         */
        private int iconType;

        /**
         * 不需要 {@link com.fxiaoke.biz.vo.MenuVo} 的三图
         */
        private PageDataVo data;
        private String appId;
    }

    @NoArgsConstructor
    @Data
    @AllArgsConstructor
    class Result implements Serializable {
        private String id;
    }
}
