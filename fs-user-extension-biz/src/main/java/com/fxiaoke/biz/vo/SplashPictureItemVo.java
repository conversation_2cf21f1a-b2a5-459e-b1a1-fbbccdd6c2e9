package com.fxiaoke.biz.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR> create by liy on 2021/12/7
 */
@Data
public class SplashPictureItemVo {
    /**
     * 文件id
     */
    private String fileId;
    /**
     * 宽
     */
    private int width;
    /**
     * 高
     */
    private int height;

    public boolean validate() {
        if (StringUtils.isEmpty(fileId) || width <= 0 || height <= 0) {
            return false;
        }
        return true;
    }
}
