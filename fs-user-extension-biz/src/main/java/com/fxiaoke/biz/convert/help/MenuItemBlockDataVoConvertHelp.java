package com.fxiaoke.biz.convert.help;

import com.facishare.cep.plugin.enums.ClientTypeEnum;
import com.facishare.webpage.customer.api.model.core.Icon;
import com.facishare.webpage.customer.core.config.ObjectConfig;
import com.fxiaoke.api.model.FullMenu;
import com.fxiaoke.api.model.Menu;
import com.fxiaoke.api.model.MenuBlockData;
import com.fxiaoke.api.model.type.DataBlockType;
import com.fxiaoke.biz.controller.BaseController;
import com.fxiaoke.biz.convert.MenuConvert;
import com.fxiaoke.biz.model.PageDataBlockDataConvertArg;
import com.fxiaoke.biz.model.PageDataBlockDataConvertResult;
import com.fxiaoke.biz.service.ConfigCoreService;
import com.fxiaoke.biz.service.NewMenuService;
import com.fxiaoke.biz.service.PictureTokenService;
import com.fxiaoke.biz.service.UserFunctionPermissionService;
import com.fxiaoke.biz.utils.ComponentUtils;
import com.fxiaoke.biz.vo.MenuItemBlockDataVo;
import com.fxiaoke.biz.vo.MenuItemVo;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Component
public class MenuItemBlockDataVoConvertHelp extends PageDataBlockDataVoConvertHelp {

    private final Logger LOGGER = LoggerFactory.getLogger(MenuItemBlockDataVoConvertHelp.class);

    @Autowired
    private ConfigCoreService configCoreService;

    @Resource
    private ObjectConfig objectConfig;

    @Resource
    private NewMenuService newMenuService;

    @Autowired
    private PictureTokenService pictureTokenService;

    @Autowired
    private UserFunctionPermissionService userFunctionPermissionService;

    @Override
    public String convertDataBlockType() {
        return DataBlockType.ITEM;
    }


    @Override
    public PageDataBlockDataConvertResult convert2Vo(PageDataBlockDataConvertArg arg) {

        String blockData = arg.getBlockData();
        ClientTypeEnum clientType = arg.getClientType();
        long clientVersion = arg.getClientVersion();
        MenuBlockData menuBlockData = MenuBlockData.convert2Dto(blockData);

        List<Menu> items = menuBlockData.getItems();
        if (!arg.isManager()) {
            items = userFunctionPermissionService.filterEnableMenu(menuBlockData.getItems(), arg.getApiNameFunctions());
        }

        MenuItemBlockDataVo menuItemBlockDataVo = new MenuItemBlockDataVo();
        menuItemBlockDataVo.setTemplateType(menuBlockData.getTemplateType());
        menuItemBlockDataVo.setHeader(menuBlockData.getHeader());

        Map<Integer, Map<String, FullMenu>> menuMap = MenuConvert.getFullMenuMap(items, clientType, clientVersion, arg.getMenusContext());

        String ea = BaseController.getEnterpriseAccount();
        int employeeId = BaseController.getEnterpriseId();

        String pageIconFontSize = configCoreService.getPageIconFontSize();
        List<MenuItemVo> itemVos = Lists.newArrayList();
        List<com.fxiaoke.api.model.component.Component> components = Lists.newArrayList();
        for (Menu menu : items) {
            Map<String, FullMenu> map = menuMap.get(menu.getMenuType());
            if (MapUtils.isEmpty(map)) {
                continue;
            }
            FullMenu appMenuItemVo = map.get(menu.getValue());
            if (Objects.isNull(appMenuItemVo)) {
                continue;
            }
            MenuItemVo menuItemVo = new MenuItemVo();
            menuItemVo.setApiName(menu.getValue());
            menuItemVo.setNewAppId(menu.getAppId());
            menuItemVo.setAppId(menu.getMenuType() + "_" + menu.getValue());
            menuItemVo.setName(appMenuItemVo.getName());
            menuItemVo.setNameStyle(pageIconFontSize);
            String img = appMenuItemVo.getIcon();
            if (menu.getIconIndex() != null) {
                Icon icon = objectConfig.getIconByIndex(menu.getIconIndex());
                String indexIcon = newMenuService.getIconByType(icon, arg.getMenusContext().getIconType());
                if (StringUtils.isNotBlank(indexIcon)){
                    img = indexIcon;
                }
            }
            menuItemVo.setImg(img);
            if (!arg.isManager()) {
                String imgToken = pictureTokenService.createShareToken(ea, employeeId, img);
                menuItemVo.setImgToken(imgToken);
            }
            menuItemVo.setAction(appMenuItemVo.getAction());
            menuItemVo.setCountSourceType(appMenuItemVo.getCountSourceType());
            menuItemVo.setCountSourceData(appMenuItemVo.getCountSourceData());
            itemVos.add(menuItemVo);
            components.add(ComponentUtils.convert2Component(menuItemVo, menu.getNewName(), menu.getMenuType()));
        }
        menuItemBlockDataVo.setItems(itemVos);
        PageDataBlockDataConvertResult result = new PageDataBlockDataConvertResult();
        result.setData(menuItemBlockDataVo);
        result.setHeader(menuItemBlockDataVo.getHeader());
        result.setComponents(components);
        return result;
    }

    @Override
    public boolean isNotEmpty(Object blockData) {
        if (!(blockData instanceof MenuItemBlockDataVo)) {
            return false;
        }

        return CollectionUtils.isNotEmpty(((MenuItemBlockDataVo) blockData).getItems());
    }
}
