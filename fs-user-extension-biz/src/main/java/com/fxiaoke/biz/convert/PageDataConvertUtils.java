package com.fxiaoke.biz.convert;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.webpage.customer.core.util.BIUrlUtil;
import com.fxiaoke.api.constant.LayoutField;
import com.fxiaoke.api.model.*;
import com.fxiaoke.api.model.component.Component;
import com.fxiaoke.api.model.component.LayoutData;
import com.fxiaoke.api.model.type.DataBlockType;
import com.fxiaoke.api.model.type.MenuType;
import com.fxiaoke.biz.component.ComponentWrapper;
import com.fxiaoke.biz.service.ConfigCoreService;
import com.fxiaoke.biz.vo.WebViewBlockDataVo;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@org.springframework.stereotype.Component("convertComponentUtils")
public class PageDataConvertUtils {
    private static final Set<String> componentTypes = Sets.newHashSet(Component.TYPE_SLIDEIMAGE, Component.TYPE_MENU_GROUP, Component.TYPE_WEATHER, Component.TYPE_WEBVIEW);

    @Resource
    private BIUrlUtil biUrlUtil;

    @Autowired
    private ConfigCoreService configCoreService;


    public PageData getPageData(PageTemplate pageTemplate) {
        if (Strings.isNullOrEmpty(pageTemplate.getLayout())) {
            return pageTemplate.getData();
        }
        LayoutData layoutData = new LayoutData(JSON.parseObject(pageTemplate.getLayout()));
        PageData pageData = new PageData();
        pageData.setPageDataBlocks(buildPageDataBlockList(layoutData));
        pageData.setTitle(pageTemplate.getData().getTitle());
        pageData.setUrl(pageTemplate.getData().getUrl());
        pageData.setMenuType(pageTemplate.getData().getMenuType());
        pageData.setQuickCreateItemList(layoutData.getQuickCreateItemList());
        return pageData;
    }

    public JSONObject buildWidgetItem(Component component, String cardId) {

        JSONObject blockDataObject = new JSONObject();
        blockDataObject.put("CardID", cardId);

        String url = biUrlUtil.buildUrl(cardId, 0);
        blockDataObject.put("URL", url);

        blockDataObject.put("Title", component.getComponentJSON().get("title"));
        blockDataObject.put("appId", component.getAppId());
        blockDataObject.put("HomePageLayoutFilters", component.getComponentJSON().get("filters"));

        ComponentWrapper componentWrapper = new ComponentWrapper(component);

        Integer proType = componentWrapper.getPropType();
        if (proType == null && cardId != null && cardId.startsWith("BI_")) {
            proType = 1;
        }
        blockDataObject.put("Type", proType);
        return blockDataObject;
    }

    private List<PageDataBlock> buildPageDataBlockList(LayoutData layoutData) {
        Map<String, Component> componentMap = layoutData.getComponents().stream().collect(Collectors.toMap(
                component -> component.getApiName(), Function.identity()));

        List<PageDataBlock> pageDataBlocks = Lists.newArrayList();
        JSONArray itemsObjects = null;
        // 连续的bi组件要合并成一个，方便加载
        for (String apiName : layoutData.getComponentApiNames()) {
            Component component = componentMap.get(apiName);
            // cardId bi 拼接url使用
            String cardId = component.getComponentJSON().getString("cardId");
            if (componentTypes.contains(component.getType())) {
                PageDataBlock pageDataBlock = convert2BlockData(component, componentMap);
                pageDataBlocks.add(buildWidgetDataBlock(itemsObjects));
                pageDataBlocks.add(pageDataBlock);
                itemsObjects = null;
            } else if (cardId == null) {
                pageDataBlocks.add(buildWidgetDataBlock(itemsObjects));
                pageDataBlocks.add(buildDefaultDataBlock(component));
                itemsObjects = null;
            } else {
                JSONObject widgetItem = buildWidgetItem(component, cardId);
                if (itemsObjects == null) {
                    itemsObjects = new JSONArray();
                }
                itemsObjects.add(widgetItem);
            }
        }
        if (itemsObjects != null) {
            pageDataBlocks.add(buildWidgetDataBlock(itemsObjects));
        }
        pageDataBlocks = pageDataBlocks.stream().filter(Objects::nonNull).collect(Collectors.toList());
        return pageDataBlocks;
    }

    private PageDataBlock buildWidgetDataBlock(JSONArray itemsObjects) {
        if (itemsObjects == null) {
            return null;
        }
        PageDataBlock widgetDataBlock = new PageDataBlock();
        widgetDataBlock.setType(DataBlockType.CRM_WIDGET);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("items", itemsObjects);
        widgetDataBlock.setBlockData(jsonObject.toJSONString());
        return widgetDataBlock;
    }

    private PageDataBlock buildDefaultDataBlock(Component component) {
        PageDataBlock pageDataBlock = new PageDataBlock();
        pageDataBlock.setType(component.getType());
        JSONArray itemsObjects = new JSONArray();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("items", itemsObjects);
        pageDataBlock.setBlockData(jsonObject.toJSONString());
        return pageDataBlock;
    }

    public PageDataBlock convert2BlockData(Component component, Map<String, Component> componentMap) {
        String type = component.getType();
        switch (type) {
            case Component.TYPE_SLIDEIMAGE:
                return convert2SlideImageDataBlock(component);
            case Component.TYPE_MENU_GROUP:
                return convert2MenusDataBlock(component, componentMap);
            case Component.TYPE_WEATHER:
                return buildWebViewPageDataBlock(type, component, configCoreService.getWeatherUrl());
            case Component.TYPE_WEBVIEW:
                return buildWebViewPageDataBlock(type, component, null);
            default:
                return null;
        }
    }


    public PageDataBlock convert2SlideImageDataBlock(Component component) {
        SlideImageBlockData slideImageBlockData = new SlideImageBlockData();
        JSONArray imgs = component.getComponentJSON().getJSONArray(LayoutField.IMGS);
        List<SlideImage> items = imgs.stream().map(img -> {
            JSONObject imgJson = (JSONObject) img;
            String imgPath = imgJson.getString(LayoutField.IMG);
            SlideImage slideImage = new SlideImage();
            slideImage.setAppId(imgJson.getString("appId"));
            slideImage.setImg(imgPath);
            Integer menuType = imgJson.getInteger("menuType");
            if (menuType == null) {
                menuType = MenuType.NOT_ACTION;
            } else if (menuType.equals(MenuType.CUSTOMIZE_URL)) {
                slideImage.setValue(imgJson.getString("action"));
            } else {
                slideImage.setValue(imgJson.getString("api_name"));
            }
            slideImage.setMenuType(menuType);
            return slideImage;
        }).collect(Collectors.toList());
        slideImageBlockData.setItems(items);
        PageDataBlock pageDataBlock = new PageDataBlock();
        pageDataBlock.setType(DataBlockType.SLIDE_IMAGE);
        pageDataBlock.setHeight(component.getHeight());
        pageDataBlock.setImgHeight(component.getImgHeight());
        pageDataBlock.setHeader(component.getHeader());
        pageDataBlock.setBlockData(JSON.toJSONString(slideImageBlockData));
        return pageDataBlock;
    }

    public Menu buildMenu(Component menuComponent) {
        Menu menu = new Menu();
        menu.setMenuType(menuComponent.getMenuType());
        menu.setAppId(menuComponent.getAppId());
        menu.setValue(menuComponent.getApiName());
        menu.setNewName(menuComponent.getNewHeader());
        menu.setName(menuComponent.getHeader());
        menu.setRecordTypeApiName(menuComponent.getRecordTypeApiName());
        menu.setIconIndex(menuComponent.getIconIndex());
        return menu;
    }

    public PageDataBlock convert2MenusDataBlock(Component component, Map<String, Component> componentMap) {
        MenuBlockData menuBlockData = new MenuBlockData();
        menuBlockData.setHeader(component.getHeader());

        Integer column = component.getColumn();
        if (column != null) {
            menuBlockData.setTemplateType(column);
        }
        List<String> componentApiNames = component.getComponentList();
        List<Menu> menus = componentApiNames.stream().map(apiName -> {
            Component menuComponent = componentMap.get(apiName);
            if (menuComponent == null) {
                return null;
            }
            return buildMenu(menuComponent);
        }).filter(Objects::nonNull).collect(Collectors.toList());
        menuBlockData.setItems(menus);

        PageDataBlock pageDataBlock = new PageDataBlock();
        pageDataBlock.setType(DataBlockType.ITEM);
        pageDataBlock.setBlockData(JSON.toJSONString(menuBlockData));
        return pageDataBlock;
    }


    public PageDataBlock buildWebViewPageDataBlock(String dataBlockType, Component component, String weatherUrl) {
        PageDataBlock pageDataBlock = new PageDataBlock();
        pageDataBlock.setType(dataBlockType);
        String url = component.getUrl();
        if (weatherUrl != null) {
            url = weatherUrl;
        }
        WebViewBlockDataVo viewBlockDataVo = new WebViewBlockDataVo();
        viewBlockDataVo.setUrl(url);
        pageDataBlock.setBlockData(JSON.toJSONString(viewBlockDataVo));
        pageDataBlock.setHeight(component.getHeight());
        pageDataBlock.setHeader(component.getHeader());
        return pageDataBlock;
    }




}
