<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
        xmlns:p="http://www.springframework.org/schema/p"
        xmlns="http://www.springframework.org/schema/beans"
        xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context.xsd
        ">

<context:annotation-config/>
<context:component-scan base-package="com.fxiaoke"/>
<context:property-placeholder location="classpath:*.properties"/>
        <import resource="classpath:applicationContext.xml"/>

<!--<bean id="dubboConf" class="com.github.autoconf.spring.reloadable.ReloadableContext" p:configName="dubbo-support-provider-A"/>-->
        </beans>