package com.test.remote;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.biz.remote.ChannelTerminalNavigationRemoteService;
import com.fxiaoke.biz.vo.GetAppConfigVo;
import com.test.BaseTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class ChannelTerminalNavigationServiceTest extends BaseTest {

    @Autowired
    private ChannelTerminalNavigationRemoteService channelTerminalNavigationRemoteService;

    @Test
    public void test() {
        String upstreamEa = "74164";
        Integer upstreamEi = 74164;
        Long outerTenantId = 300021337L;
        Long outerUid = 300102624L;
        GetAppConfigVo.Result  result = channelTerminalNavigationRemoteService.getTerminalNavigationMenuListData(upstreamEa, upstreamEi,outerTenantId,outerUid);
        System.out.println(JSON.toJSON(result));
    }
}
