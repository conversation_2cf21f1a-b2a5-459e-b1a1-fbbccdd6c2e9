package com.facishare.job.schedule.biz.util

import com.github.trace.TraceContext
import spock.lang.Specification
/**
 * <AUTHOR> create by liy on 2025/3/4
 */
class TraceUtilsTest extends Specification {


    void setup() {
        //此处不要自己mock，会导致线程中的threaLocal对象被mock，导致线程中的traceId被mock
        //ThreadLocal threadLocal = Mock(ThreadLocal.class)
        //TraceContext traceContext = Mock(TraceContext.class)
        //Whitebox.setInternalState(TraceContext, "context", threadLocal)
    }

    def "FillTraceId"() {
        given: "准备测试数据"
        def env = "gray"

        when: "执行fillTraceId方法"
        TraceUtils.fillTraceId(env)
        String traceId = TraceContext.get().getTraceId()

        then: "验证traceId是否正确设置，并且格式符合预期"
        traceId.startsWith("fs-paas-job-schedule_${env}_")
        traceId.length() == "fs-paas-job-schedule_gray_".length() + 32 // UUID去掉横线后的长度为32
    }

    def "FillTraceId When Input Is Empty"() {
        given:

        when: "传入空的traceId"
        TraceUtils.fillTraceId("")
        String traceId = TraceContext.get().getTraceId()

        then: "应该生成只带前缀的traceId"
        traceId.startsWith("fs-paas-job-schedule_")
        traceId.length() == "fs-paas-job-schedule_".length() + 32
    }

    def "FillTraceId When Input Is Null"() {
        given:

        when: "传入null的traceId"
        TraceUtils.fillTraceId(null)
        String traceId = TraceContext.get().getTraceId()

        then: "应该生成只带前缀的traceId"
        traceId.startsWith("fs-paas-job-schedule_")
        traceId.length() == "fs-paas-job-schedule_".length() + 32
    }
}
