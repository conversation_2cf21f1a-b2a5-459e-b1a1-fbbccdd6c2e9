package com.facishare.paas.appframework.core.predef.domain;

import com.facishare.paas.appframework.core.model.domain.ControllerDomainPlugin;
import com.facishare.paas.appframework.core.model.domain.DomainPlugin;
import com.facishare.paas.appframework.core.predef.controller.BaseListController;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.appframework.core.rest.BaseAPIResult;
import lombok.*;
import lombok.experimental.SuperBuilder;

/**
 * Created by zhouwr on 2024/5/21.
 */
public interface RelatedListControllerDomainPlugin extends ControllerDomainPlugin<RelatedListControllerDomainPlugin.Arg, RelatedListControllerDomainPlugin.Result> {
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    @ToString(callSuper = true)
    @EqualsAndHashCode(callSuper = true)
    class Arg extends DomainPlugin.Arg {
        private StandardRelatedListController.Arg arg;
        private BaseListController.Result result;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    @ToString(callSuper = true)
    @EqualsAndHashCode(callSuper = true)
    class Result extends DomainPlugin.Result {
        private StandardRelatedListController.Arg arg;
        private BaseListController.Result result;
    }

    @Data
    @ToString(callSuper = true)
    @EqualsAndHashCode(callSuper = true)
    class RestResult extends BaseAPIResult {
        private Result data;
    }
}
