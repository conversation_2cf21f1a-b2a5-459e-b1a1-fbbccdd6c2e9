package com.facishare.paas.appframework.core.predef.service.dto.calculate;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Created by zhouwr on 2018/12/21
 */
public interface CalculateAndUpdateFormulaFields {
    @Data
    class Arg {
        private String objectApiName;
        private List<String> fieldApiNames;
        private List<String> dataIds;
        private Boolean calcRelateField;

        public boolean calcRelateField() {
            return Boolean.TRUE.equals(calcRelateField);
        }
    }

    @Data
    @Builder
    class Result {
        private Map<String, Map<String, Object>> result;
    }
}
