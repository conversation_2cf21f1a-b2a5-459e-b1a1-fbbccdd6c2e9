package com.facishare.paas.appframework.core.predef.service.dto.brushData;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public interface BatchFillDuplicateRuleInfo {

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class Arg {
        //指定的对象
        List<String> objectApiNameList;
    }
}
