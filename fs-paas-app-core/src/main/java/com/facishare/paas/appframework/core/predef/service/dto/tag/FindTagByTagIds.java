package com.facishare.paas.appframework.core.predef.service.dto.tag;

import com.facishare.paas.appframework.core.model.TagDocument;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;

public interface FindTagByTagIds {
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class Arg {
        Set<String> tagIds;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class Result {
        List<TagDocument> tagList;
    }
}
