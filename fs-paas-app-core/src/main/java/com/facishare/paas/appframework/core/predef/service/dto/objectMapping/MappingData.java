package com.facishare.paas.appframework.core.predef.service.dto.objectMapping;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectMappingService;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Maps;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public interface MappingData {
    @Data
    class Arg {
        @J<PERSON>NField(name = "M1")
        @JsonProperty("object_data")
        @SerializedName("object_data")
        ObjectDataDocument objectData;

        @JSONField(name = "M2")
        @JsonProperty("details")
        @SerializedName("details")
        Map<String, List<ObjectDataDocument>> details;

        @JSONField(name = "M3")
        @JsonProperty("rule_api_name")
        @SerializedName("rule_api_name")
        String ruleApiName;

        public ObjectMappingService.MappingDataArg MappingDataArg() {
            ObjectMappingService.MappingDataArg arg = new ObjectMappingService.MappingDataArg();
            arg.setRuleApiName(ruleApiName);
            arg.setObjectData(ObjectDataExt.of(objectData));

            Map<String, List<IObjectData>> detailObjectData = Maps.newHashMap();
            if (!CollectionUtils.empty(arg.getDetails())) {
                details.forEach((key, value) -> detailObjectData.put(key, value.stream()
                        .map(ObjectDataDocument::toObjectData)
                        .collect(Collectors.toList())));
            }
            arg.setDetails(detailObjectData);

            return arg;
        }

    }

    @Builder
    @Data
    class Result {
        @JSONField(name = "M1")
        @JsonProperty("object_data")
        @SerializedName("object_data")
        ObjectDataDocument objectData;

        @JSONField(name = "M2")
        @JsonProperty("details")
        @SerializedName("details")
        Map<String, List<ObjectDataDocument>> details;

        public static Result from(ObjectMappingService.MappingDataResult result) {
            return Result.builder()
                    .details(ObjectDataDocument.ofMap(result.getDetails()))
                    .objectData(ObjectDataDocument.of(result.getObjectData()))
                    .build();
        }
    }
}
