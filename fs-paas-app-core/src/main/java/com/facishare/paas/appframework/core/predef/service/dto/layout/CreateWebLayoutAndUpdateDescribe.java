package com.facishare.paas.appframework.core.predef.service.dto.layout;

import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

public interface CreateWebLayoutAndUpdateDescribe {
    @Data
    class Arg {
        private LayoutDocument layoutData;
        private ObjectDescribeDocument describeData;
        private ObjectDescribeDocument draftData;
        private boolean active;
        private ObjectDescribeDocument describeExtra;
        private String persistentDataCalc;
        private List<Map<String, Object>> fieldsExtra;
        private String appId;
    }

    @Data
    @Builder
    class Result {
        private LayoutDocument layout;
        private ObjectDescribeDocument objectDescribeDraft;
        private ObjectDescribeDocument objectDescribe;
        private ObjectDescribeDocument describeExtra;
        private List<Map<String, Object>> fieldsExtra;
    }
}
