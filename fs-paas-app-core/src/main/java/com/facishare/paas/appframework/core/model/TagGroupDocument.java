package com.facishare.paas.appframework.core.model;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.DocumentBaseEntity;
import com.facishare.paas.metadata.api.describe.ITagDescribe;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class TagGroupDocument extends DocumentBaseEntity {
    private static final String ID = "_id";
    private static final String DESCRIBE_API_NAME = "describe_api_name";
    private static final String TYPE = "type";
    private static final String TAG_DEFINE_TYPE = "tag_define_type";
    private static final String IS_DELETED = "is_deleted";
    private static final String TAG_RANGE = "tag_range";
    private static final String IS_ALL = "is_all";
    private static final String API_NAME = "api_name";
    private static final String RANGES = "ranges";
    private static final String IS_MUTEX = "is_mutex";
    private static final String IS_ACTIVE = "is_active";
    private static final String GROUP_DESCRIPTION = "group_description";
    private static final String CREATE_TIME = "create_time";
    private static final String LAST_MODIFIED_TIME = "last_modified_time";


    private TagGroupDocument(Map<String, Object> map) {
        super(map);
    }

    public static TagGroupDocument of(ITagDescribe tag) {
        Map<String, Object> map = Maps.newHashMap();
        map.put(ID, tag.getId());
        map.put(DESCRIBE_API_NAME, tag.getDescribeApiName());
        map.put(TYPE, tag.getType());
        map.put(TAG_DEFINE_TYPE, tag.getTagDefineType());
        map.put(IS_DELETED, tag.isDeleted());
        map.put(TAG_RANGE, tag.getRange());
        map.put(IS_ALL, tag.getIsAll());
        map.put(API_NAME, tag.getApiName());
        map.put(RANGES, tag.getRange());
        map.put(IS_MUTEX, tag.getIsMutex());
        map.put(IS_ACTIVE, tag.getIsActive());
        map.put(GROUP_DESCRIPTION, tag.getGroupDescription());
        map.put(CREATE_TIME, tag.getCreateTime());
        map.put(LAST_MODIFIED_TIME, tag.getLastModifiedTime());
        return new TagGroupDocument(map);
    }

    public static List<TagGroupDocument> ofList(List<ITagDescribe> tags) {
        return CollectionUtils.nullToEmpty(tags).stream().map(TagGroupDocument::of).collect(Collectors.toList());
    }


}
