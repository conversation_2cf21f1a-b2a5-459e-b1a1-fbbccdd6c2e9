package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.appframework.common.util.ObjectAction;

import java.util.List;
import java.util.stream.Collectors;

/**
 * create by <PERSON><PERSON><PERSON> on 2019/10/12
 */
public class StandardAsyncBulkDeletePartnerAction extends AbstractStandardAsyncBulkAction<StandardDeletePartnerAction.Arg, StandardDeletePartnerAction.Arg> {

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardAction.DeletePartner.getFunPrivilegeCodes();
    }

    @Override
    protected String getDataIdByParam(StandardDeletePartnerAction.Arg param) {
        return param.getDataIds().get(0);
    }

    @Override
    protected List<StandardDeletePartnerAction.Arg> getButtonParams() {
        return arg.getDataIds().stream()
                .map(id -> StandardDeletePartnerAction.Arg.of(id, arg.getPartnerId(), arg.getRemoveOutOwner()))
                .collect(Collectors.toList());
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.DELETE_PARTNER.getButtonApiName();
    }

    @Override
    protected String getActionCode() {
        return ObjectAction.DELETE_PARTNER.getActionCode();
    }
}
