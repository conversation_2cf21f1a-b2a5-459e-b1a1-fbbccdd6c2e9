package com.facishare.paas.appframework.core.predef.service.dto.dataMultiLang;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public interface GetSupportObject {
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class Arg {
        @Builder.Default
        Boolean includeEnable = Boolean.TRUE;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class Result {
        List<DescribeInfo> describeList;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class DescribeInfo {
        private String apiName;
        private String displayName;
    }


}
