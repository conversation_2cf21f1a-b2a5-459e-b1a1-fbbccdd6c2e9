package com.facishare.paas.appframework.core.predef.service.dto.queryscene;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * Created by liyiguang on 2017/10/23.
 */
public interface UpdateCustomScene {
    @Data
    class Arg {
        @JSONField(name = "M1")
        String scene_data;
    }
    @Builder
    @Data
    class Result {
        @JSONField(name = "M1")
        boolean success;

        @JSONField(name = "M2")
        Map searchTemplate;
    }
}
