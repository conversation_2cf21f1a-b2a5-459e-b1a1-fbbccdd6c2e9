package com.facishare.paas.appframework.core.predef.service.dto.gdpr;

import lombok.Builder;
import lombok.Data;

public interface DeleteGdprLegalBaseDetail {

    @Data
    class Arg {
        private String apiName;
        private String dataId;
        private String id;
    }

    @Data
    @Builder
    class Result {
        private String id;
        private String apiName;
        private String dataId;
        private Boolean openStatus;
        private String legalBase;
        private String legalBaseStatus;
        private String remark;
        private Long agreeDate;
        private String contactWay;
        private String agreePerson;
    }
}
