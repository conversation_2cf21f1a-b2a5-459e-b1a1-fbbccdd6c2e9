package com.facishare.paas.appframework.core.predef.action.uievent;

import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import org.springframework.util.CollectionUtils;

import java.util.Map;
import java.util.Objects;

/**
 * 提供一些UI事件可能用到的公共方法
 *
 * <AUTHOR>
 * @date 2019-09-25 11:03
 */
public final class UIEventUtils {
    private UIEventUtils() { }

    /**
     * 判断从对象数据是否为新建的
     *
     * @param detailMap 从对象数据Map形式
     * @return true : 新建 false : 非新建
     */
    public static boolean isNewDetail(Map<String, Object> detailMap) {
        if (CollectionUtils.isEmpty(detailMap)) {
            return false;
        }
        Object mark = detailMap.get(ObjectDataExt.MARK_API_NAME);
        return Objects.isNull(mark);
    }

    /**
     * 判断从对象数据是否为新建的
     *
     * @param detailData 从对象数据
     * @return true : 新建 false : 非新建
     */
    public static boolean isNewDetail(IObjectData detailData) {
        if (Objects.isNull(detailData)) {
            return false;
        }
        return isNewDetail(ObjectDataExt.of(detailData).toMap());
    }
}
