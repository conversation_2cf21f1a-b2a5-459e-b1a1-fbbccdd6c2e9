package com.facishare.paas.appframework.function

import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.metadata.api.IUdefFunction
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.dom4j.Document
import org.dom4j.DocumentHelper
import org.dom4j.Element
import org.dom4j.Node
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import java.lang.reflect.Field

class VSCodeExtUtilsTest extends Specification {

    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
    }

    def "test buildXml success"() {
        given: "一个包含参数的函数"
        IUdefFunction function = Mock(IUdefFunction)
        FunctionVSCodeExt.Parameter parameter = new FunctionVSCodeExt.Parameter(
                name: "param1",
                type: "String",
                remark: "测试参数",
                defaultValue: "defaultValue"
        )
        function.getParameters() >> [parameter.toJSON()]

        when: "构建XML"
        String xml = VSCodeExtUtils.buildXml(function)

        then: "XML不为空且包含参数信息"
        xml != null
        xml.contains("<parameter>")
        xml.contains("<type>String</type>")
        xml.contains("<name>param1</name>")
        xml.contains("<default>defaultValue</default>")
        xml.contains("<label>测试参数</label>")
    }

    def "test buildXml with empty parameters"() {
        given: "一个没有参数的函数"
        IUdefFunction function = Mock(IUdefFunction)
        function.getParameters() >> []

        when: "构建XML"
        String xml = VSCodeExtUtils.buildXml(function)

        then: "返回null"
        xml == null
    }

    def "test parseXML success"() {
        given: "有效的XML字符串和用户"
        def tenantId = "74255"
        User user = User.systemUser(tenantId)
        String validXml = """
            <APLBundle>
                <parameters>
                    <parameter>
                        <type>String</type>
                        <name>testParam</name>
                        <default>defaultValue</default>
                        <label>测试参数</label>
                    </parameter>
                </parameters>
            </APLBundle>
        """

        when: "解析XML"
        IUdefFunction function = VSCodeExtUtils.parseXML(user, validXml)

        then: "函数信息被正确解析"
        function != null
        function.getTenantId() == tenantId
        function.getCreatedBy() == user.getUserId()
        function.getLastModifiedBy() == user.getUserId()
        function.getParameters().size() == 1
        function.getParameters()[0].contains("testParam")
        function.getParameters()[0].contains("String")
        function.getParameters()[0].contains("defaultValue")
        function.getParameters()[0].contains("测试参数")
    }

    def "test parseXML with invalid XML"() {
        given: "无效的XML和用户"
        def tenantId = "74255"
        User user = User.systemUser(tenantId)
        String invalidXml = "<invalid><xml>"

        when: "解析无效XML"
        VSCodeExtUtils.parseXML(user, invalidXml)

        then: "抛出验证异常"
        thrown(ValidateException)
    }

    def "test parseXML with empty XML"() {
        given: "空XML和用户"
        def tenantId = "74255"
        User user = User.systemUser(tenantId)
        String emptyXml = ""

        when: "解析空XML"
        IUdefFunction function = VSCodeExtUtils.parseXML(user, emptyXml)

        then: "返回默认函数对象"
        function != null
        function.getTenantId() == tenantId
        function.getCreatedBy() == user.getUserId()
        function.getParameters() != null
    }

    def "test requireNonNull with null node"() {
        given: "一个根节点和标签"
        Node root = Mock(Node)
        root.selectSingleNode("label") >> null
        String label = "label"

        when: "获取必需节点但节点为null"
        VSCodeExtUtils.requireNonNull(root, label)

        then: "抛出验证异常"
        thrown(ValidateException)
    }

    def "test requireNonNull with valid node"() {
        given: "一个有效的根节点和标签"
        Node node = Mock(Node)
        node.getText() >> "nodeValue"
        
        Node root = Mock(Node)
        root.selectSingleNode("label") >> node
        String label = "label"

        when: "获取有效节点"
        String result = VSCodeExtUtils.requireNonNull(root, label)

        then: "返回节点值"
        result == "nodeValue"
    }
} 