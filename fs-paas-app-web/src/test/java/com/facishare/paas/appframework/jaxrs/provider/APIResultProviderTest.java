package com.facishare.paas.appframework.jaxrs.provider;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.MultivaluedHashMap;
import javax.ws.rs.core.MultivaluedMap;
import java.io.ByteArrayOutputStream;
import java.lang.annotation.Annotation;
import java.lang.reflect.Type;

import static org.junit.jupiter.api.Assertions.*;

/**
 * APIResultProvider单元测试
 * GenerateByAI
 */
@ExtendWith(MockitoExtension.class)
class APIResultProviderTest {

    private APIResultProvider apiResultProvider;
    private MultivaluedMap<String, Object> httpHeaders;
    private ByteArrayOutputStream outputStream;
    private final Object testObject = new TestData("test", 123);

    @BeforeEach
    void setUp() {
        apiResultProvider = new APIResultProvider();
        httpHeaders = new MultivaluedHashMap<>();
        outputStream = new ByteArrayOutputStream();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isWriteable方法对所有类型都返回true
     */
    @Test
    @DisplayName("正常场景 - isWriteable对所有类型返回true")
    void testIsWriteable_AllTypes_ReturnsTrue() {
        // 准备测试数据
        Class<?>[] testTypes = {String.class, Object.class, Integer.class, TestData.class};
        Type[] genericTypes = {String.class, Object.class, Integer.class, TestData.class};
        Annotation[] annotations = new Annotation[0];
        MediaType[] mediaTypes = {MediaType.APPLICATION_JSON_TYPE, MediaType.TEXT_PLAIN_TYPE};

        // 执行测试验证
        for (Class<?> type : testTypes) {
            for (Type genericType : genericTypes) {
                for (MediaType mediaType : mediaTypes) {
                    boolean result = apiResultProvider.isWriteable(type, genericType, annotations, mediaType);
                    assertTrue(result, "isWriteable应对所有类型返回true");
                }
            }
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getSize方法始终返回-1
     */
    @Test
    @DisplayName("正常场景 - getSize始终返回-1")
    void testGetSize_AlwaysReturnsMinusOne() {
        // 准备测试数据
        Object[] testObjects = {testObject, "string", 123, null};

        // 执行测试验证
        for (Object obj : testObjects) {
            long size = apiResultProvider.getSize(obj, Object.class, Object.class,
                    new Annotation[0], MediaType.APPLICATION_JSON_TYPE);
            assertEquals(-1L, size, "getSize应始终返回-1");
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试APIResultProvider类的基本属性和注解
     */
    @Test
    @DisplayName("正常场景 - 验证类的基本属性和注解")
    void testClassBasicProperties() {
        // 验证类上的注解
        assertTrue(APIResultProvider.class.isAnnotationPresent(org.springframework.stereotype.Component.class),
                "应有@Component注解");
        assertTrue(APIResultProvider.class.isAnnotationPresent(javax.ws.rs.ext.Provider.class),
                "应有@Provider注解");
        assertTrue(APIResultProvider.class.isAnnotationPresent(javax.ws.rs.Produces.class),
                "应有@Produces注解");

        // 验证实现的接口
        Class<?>[] interfaces = APIResultProvider.class.getInterfaces();
        boolean implementsMessageBodyWriter = false;
        for (Class<?> iface : interfaces) {
            if (iface.getName().contains("MessageBodyWriter")) {
                implementsMessageBodyWriter = true;
                break;
            }
        }
        assertTrue(implementsMessageBodyWriter, "应实现MessageBodyWriter接口");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试writeTo方法在没有SerializerManager依赖时的基本行为
     */
    @Test
    @DisplayName("边界场景 - writeTo方法的基本行为验证")
    void testWriteTo_BasicBehavior() {
        // 当SerializerManager为null时，调用writeTo应该抛出异常（因为依赖注入问题）
        assertThrows(NullPointerException.class, () -> {
            apiResultProvider.writeTo(testObject, Object.class, Object.class,
                    new Annotation[0], MediaType.APPLICATION_JSON_TYPE, httpHeaders, outputStream);
        }, "没有SerializerManager依赖时应抛出NullPointerException");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试各种MediaType对isWriteable方法的影响
     */
    @Test
    @DisplayName("正常场景 - 不同MediaType对isWriteable的影响")
    void testIsWriteable_DifferentMediaTypes() {
        MediaType[] mediaTypes = {
                MediaType.APPLICATION_JSON_TYPE,
                MediaType.TEXT_PLAIN_TYPE,
                MediaType.APPLICATION_XML_TYPE,
                new MediaType("application", "simplejson")
        };

        for (MediaType mediaType : mediaTypes) {
            boolean result = apiResultProvider.isWriteable(String.class, String.class,
                    new Annotation[0], mediaType);
            assertTrue(result, "所有MediaType都应返回true");
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试null参数对方法的影响
     */
    @Test
    @DisplayName("边界场景 - null参数处理")
    void testMethodsWithNullParameters() {
        // 测试isWriteable的null参数处理
        assertTrue(apiResultProvider.isWriteable(null, null, null, null),
                "isWriteable应处理null参数");

        // 测试getSize的null参数处理
        long size = apiResultProvider.getSize(null, null, null, null, null);
        assertEquals(-1L, size, "getSize对null参数应返回-1");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试方法签名的正确性
     */
    @Test
    @DisplayName("正常场景 - 验证方法签名正确性")
    void testMethodSignatures() throws NoSuchMethodException {
        // 验证isWriteable方法签名
        assertNotNull(APIResultProvider.class.getMethod("isWriteable",
                        Class.class, Type.class, Annotation[].class, MediaType.class),
                "isWriteable方法签名应正确");

        // 验证getSize方法签名
        assertNotNull(APIResultProvider.class.getMethod("getSize",
                        Object.class, Class.class, Type.class, Annotation[].class, MediaType.class),
                "getSize方法签名应正确");

        // 验证writeTo方法签名
        assertNotNull(APIResultProvider.class.getMethod("writeTo",
                        Object.class, Class.class, Type.class, Annotation[].class,
                        MediaType.class, MultivaluedMap.class, java.io.OutputStream.class),
                "writeTo方法签名应正确");
    }

    /**
     * 测试数据类
     */
    private static class TestData {
        private final String name;
        private final Integer value;

        public TestData(String name, Integer value) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public Integer getValue() {
            return value;
        }

        @Override
        public String toString() {
            return String.format("TestData{name='%s', value=%d}", name, value);
        }
    }
} 