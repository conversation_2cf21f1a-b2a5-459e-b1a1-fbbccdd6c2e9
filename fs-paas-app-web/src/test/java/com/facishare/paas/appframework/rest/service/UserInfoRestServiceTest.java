package com.facishare.paas.appframework.rest.service;

import com.facishare.paas.appframework.common.service.dto.UserInfo;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.privilege.util.PrivilegeConstants;
import com.facishare.paas.appframework.rest.dto.data.FindAdminUsers;
import com.facishare.paas.appframework.rest.dto.data.FindUserNameByIds;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.NullSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * 用户信息服务单元测试
 */
@ExtendWith(MockitoExtension.class)
class UserInfoRestServiceTest {

    @Mock
    private ServiceFacade serviceFacade;

    @InjectMocks
    private UserInfoRestService userInfoRestService;

    private RequestContext requestContext;
    private User user;

    @BeforeEach
    void setUp() {
        user = new User("74255", "1000");
        requestContext = RequestContext.builder()
                .user(user)
                .tenantId("74255")
                .build();
    }

    @Test
    void testFindNameByIds_Success() {
        // Given
        FindUserNameByIds.Arg arg = FindUserNameByIds.Arg.builder().build();
        List<String> userIdList = Lists.newArrayList("user1", "user2", "user3");
        arg.setUserIdList(userIdList);

        List<UserInfo> mockUserInfoList = Lists.newArrayList();
        UserInfo userInfo1 = new UserInfo();
        userInfo1.setId("user1");
        userInfo1.setName("User One");
        mockUserInfoList.add(userInfo1);

        UserInfo userInfo2 = new UserInfo();
        userInfo2.setId("user2");
        userInfo2.setName("User Two");
        mockUserInfoList.add(userInfo2);

        UserInfo userInfo3 = new UserInfo();
        userInfo3.setId("user3");
        userInfo3.setName("User Three");
        mockUserInfoList.add(userInfo3);

        when(serviceFacade.getUserNameByIds("74255", "1000", userIdList))
                .thenReturn(mockUserInfoList);

        // When
        FindUserNameByIds.Result result = userInfoRestService.findNameByIds(arg, requestContext);

        // Then
        assertNotNull(result);
        assertNotNull(result.getUserInfoList());
        assertEquals(3, result.getUserInfoList().size());
        assertEquals("User One", result.getUserInfoList().get(0).getName());
        assertEquals("User Two", result.getUserInfoList().get(1).getName());
        assertEquals("User Three", result.getUserInfoList().get(2).getName());
        verify(serviceFacade, times(1)).getUserNameByIds("74255", "1000", userIdList);
    }

    @Test
    void testFindNameByIds_EmptyUserIdList() {
        // Given
        FindUserNameByIds.Arg arg = FindUserNameByIds.Arg.builder().build();
        arg.setUserIdList(Collections.emptyList());

        when(serviceFacade.getUserNameByIds("74255", "1000", Collections.emptyList()))
                .thenReturn(Collections.emptyList());

        // When
        FindUserNameByIds.Result result = userInfoRestService.findNameByIds(arg, requestContext);

        // Then
        assertNotNull(result);
        assertNotNull(result.getUserInfoList());
        assertTrue(result.getUserInfoList().isEmpty());
        verify(serviceFacade, times(1)).getUserNameByIds("74255", "1000", Collections.emptyList());
    }

    @ParameterizedTest
    @NullSource
    void testFindNameByIds_NullUserIdList(List<String> userIdList) {
        // Given
        FindUserNameByIds.Arg arg = FindUserNameByIds.Arg.builder().build();
        arg.setUserIdList(userIdList);

        when(serviceFacade.getUserNameByIds("74255", "1000", userIdList))
                .thenReturn(Collections.emptyList());

        // When
        FindUserNameByIds.Result result = userInfoRestService.findNameByIds(arg, requestContext);

        // Then
        assertNotNull(result);
        assertNotNull(result.getUserInfoList());
        assertTrue(result.getUserInfoList().isEmpty());
        verify(serviceFacade, times(1)).getUserNameByIds("74255", "1000", userIdList);
    }

    @Test
    void testFindNameByIds_ServiceFacadeReturnsNull() {
        // Given
        FindUserNameByIds.Arg arg = FindUserNameByIds.Arg.builder().build();
        arg.setUserIdList(Lists.newArrayList("user1"));

        when(serviceFacade.getUserNameByIds("74255", "1000", Lists.newArrayList("user1")))
                .thenReturn(null);

        // When
        FindUserNameByIds.Result result = userInfoRestService.findNameByIds(arg, requestContext);

        // Then
        assertNotNull(result);
        assertNull(result.getUserInfoList());
    }

    @Test
    void testFindAdminUsers_Success() {
        // Given
        List<String> mockAdminUsers = Lists.newArrayList("admin1", "admin2", "admin3");
        when(serviceFacade.getUsersByRole(user, PrivilegeConstants.ADMIN_ROLE_CODE))
                .thenReturn(mockAdminUsers);

        // When
        FindAdminUsers.Result result = userInfoRestService.findAdminUsers(requestContext);

        // Then
        assertNotNull(result);
        assertNotNull(result.getAdminUsers());
        assertEquals(3, result.getAdminUsers().size());
        assertTrue(result.getAdminUsers().contains("admin1"));
        assertTrue(result.getAdminUsers().contains("admin2"));
        assertTrue(result.getAdminUsers().contains("admin3"));
        verify(serviceFacade, times(1)).getUsersByRole(user, PrivilegeConstants.ADMIN_ROLE_CODE);
    }

    @Test
    void testFindAdminUsers_EmptyResult() {
        // Given
        when(serviceFacade.getUsersByRole(user, PrivilegeConstants.ADMIN_ROLE_CODE))
                .thenReturn(Collections.emptyList());

        // When
        FindAdminUsers.Result result = userInfoRestService.findAdminUsers(requestContext);

        // Then
        assertNotNull(result);
        assertNotNull(result.getAdminUsers());
        assertTrue(result.getAdminUsers().isEmpty());
    }

    @Test
    void testFindAdminUsers_ServiceFacadeReturnsNull() {
        // Given
        when(serviceFacade.getUsersByRole(user, PrivilegeConstants.ADMIN_ROLE_CODE))
                .thenReturn(null);

        // When
        FindAdminUsers.Result result = userInfoRestService.findAdminUsers(requestContext);

        // Then
        assertNotNull(result);
        assertNull(result.getAdminUsers());
    }

    @Test
    void testFindNameByIds_NullArg() {
        // When & Then
        assertThrows(NullPointerException.class, () -> {
            userInfoRestService.findNameByIds(null, requestContext);
        });
    }

    @Test
    void testFindNameByIds_NullRequestContext() {
        // Given
        FindUserNameByIds.Arg arg = FindUserNameByIds.Arg.builder().build();
        arg.setUserIdList(Lists.newArrayList("user1"));

        // When & Then
        assertThrows(NullPointerException.class, () -> {
            userInfoRestService.findNameByIds(arg, null);
        });
    }

    @Test
    void testFindAdminUsers_NullRequestContext() {
        // When & Then
        assertThrows(NullPointerException.class, () -> {
            userInfoRestService.findAdminUsers(null);
        });
    }

    @Test
    void testFindNameByIds_ServiceFacadeThrowsException() {
        // Given
        FindUserNameByIds.Arg arg = FindUserNameByIds.Arg.builder().build();
        arg.setUserIdList(Lists.newArrayList("user1"));

        when(serviceFacade.getUserNameByIds("74255", "1000", Lists.newArrayList("user1")))
                .thenThrow(new RuntimeException("Service error"));

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            userInfoRestService.findNameByIds(arg, requestContext);
        });
    }

    @Test
    void testFindAdminUsers_ServiceFacadeThrowsException() {
        // Given
        when(serviceFacade.getUsersByRole(user, PrivilegeConstants.ADMIN_ROLE_CODE))
                .thenThrow(new RuntimeException("Service error"));

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            userInfoRestService.findAdminUsers(requestContext);
        });
    }

    @Test
    void testFindNameByIds_SingleUser() {
        // Given
        FindUserNameByIds.Arg arg = FindUserNameByIds.Arg.builder().build();
        arg.setUserIdList(Lists.newArrayList("singleUser"));

        UserInfo userInfo = new UserInfo();
        userInfo.setId("singleUser");
        userInfo.setName("Single User");

        when(serviceFacade.getUserNameByIds("74255", "1000", Lists.newArrayList("singleUser")))
                .thenReturn(Lists.newArrayList(userInfo));

        // When
        FindUserNameByIds.Result result = userInfoRestService.findNameByIds(arg, requestContext);

        // Then
        assertNotNull(result);
        assertNotNull(result.getUserInfoList());
        assertEquals(1, result.getUserInfoList().size());
        assertEquals("singleUser", result.getUserInfoList().get(0).getId());
        assertEquals("Single User", result.getUserInfoList().get(0).getName());
    }

    @Test
    void testFindNameByIds_LargeUserIdList() {
        // Given
        FindUserNameByIds.Arg arg = FindUserNameByIds.Arg.builder().build();
        List<String> largeUserIdList = Lists.newArrayList();
        List<UserInfo> largeUserInfoList = Lists.newArrayList();

        for (int i = 0; i < 100; i++) {
            String userId = "user" + i;
            largeUserIdList.add(userId);

            UserInfo userInfo = new UserInfo();
            userInfo.setId(userId);
            userInfo.setName("User " + i);
            largeUserInfoList.add(userInfo);
        }

        arg.setUserIdList(largeUserIdList);

        when(serviceFacade.getUserNameByIds("74255", "1000", largeUserIdList))
                .thenReturn(largeUserInfoList);

        // When
        FindUserNameByIds.Result result = userInfoRestService.findNameByIds(arg, requestContext);

        // Then
        assertNotNull(result);
        assertNotNull(result.getUserInfoList());
        assertEquals(100, result.getUserInfoList().size());
        assertEquals("user0", result.getUserInfoList().get(0).getId());
        assertEquals("user99", result.getUserInfoList().get(99).getId());
    }

    @Test
    void testFindAdminUsers_SingleAdmin() {
        // Given
        when(serviceFacade.getUsersByRole(user, PrivilegeConstants.ADMIN_ROLE_CODE))
                .thenReturn(Lists.newArrayList("singleAdmin"));

        // When
        FindAdminUsers.Result result = userInfoRestService.findAdminUsers(requestContext);

        // Then
        assertNotNull(result);
        assertNotNull(result.getAdminUsers());
        assertEquals(1, result.getAdminUsers().size());
        assertEquals("singleAdmin", result.getAdminUsers().get(0));
    }

    @Test
    void testFindNameByIds_VerifyServiceFacadeParameters() {
        // Given
        FindUserNameByIds.Arg arg = FindUserNameByIds.Arg.builder().build();
        List<String> userIdList = Lists.newArrayList("user1", "user2");
        arg.setUserIdList(userIdList);

        when(serviceFacade.getUserNameByIds("74255", "1000", userIdList))
                .thenReturn(Collections.emptyList());

        // When
        userInfoRestService.findNameByIds(arg, requestContext);

        // Then
        verify(serviceFacade, times(1)).getUserNameByIds(
                eq("74255"),    // tenantId from requestContext
                eq("1000"),      // userId from requestContext.getUser()
                eq(userIdList)         // userIdList from arg
        );
    }
}
