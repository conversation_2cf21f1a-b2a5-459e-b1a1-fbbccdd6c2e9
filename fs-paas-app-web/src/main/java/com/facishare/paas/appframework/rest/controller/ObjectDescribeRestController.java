package com.facishare.paas.appframework.rest.controller;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.jaxrs.annotation.RestAPI;
import com.facishare.paas.appframework.rest.dto.data.*;
import com.facishare.paas.appframework.rest.dto.describe.FindSimpleDetailDescribes;
import com.facishare.paas.appframework.rest.dto.describe.QueryDisplayNameByApiNames;
import com.facishare.paas.appframework.rest.service.ObjectDescribeRestService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.ws.rs.*;
import java.util.List;
import java.util.Optional;

/**
 * 获取对象描述信息处理类
 * Created By Yuanxl on 2018/4/27
 */
@Controller
@Path("/v1/inner/rest/object_describe")
@Slf4j
@RestAPI
public class ObjectDescribeRestController {

    @Autowired
    private ObjectDescribeRestService objectDescribeRestService;

    /**
     * 根据企业Id查询对象描述列表
     *
     * @param packageName      默认为CRM
     * @param includeUnActived 是否包含未激活对象描述
     * @return 对象描述列表
     */
    @GET
    @Path("/")
    // TODO: 2018/7/18 需要讨论是否添加字段描述
    public FindDescribeByEid.Result findByTenantId(@QueryParam("package") String packageName,
                                                   @QueryParam("includeUnActived") boolean includeUnActived,
                                                   @QueryParam("isExcludeDetailObj") boolean isExcludeDetailObj,
                                                   @QueryParam("isExcludeDetailWithMasterCreated") boolean isExcludeDetailWithMasterCreated,
                                                   @QueryParam("includeChangeOrderObj") Boolean includeChangeOrderObj,
                                                   @QueryParam("sourceInfo") String sourceInfo) {
        RequestContext context = RequestContextManager.getContext();
        FindDescribeByEid.Arg arg = FindDescribeByEid.Arg.builder()
                .packageName(Optional.ofNullable(packageName).orElse("CRM"))
                .includeUnActived(includeUnActived)
                .isExcludeDetailObj(includeUnActived)
                .isExcludeDetailWithMasterCreated(isExcludeDetailWithMasterCreated)
                .sourceInfo(sourceInfo)
                .includeChangeOrderObj(includeChangeOrderObj)
                .build();
        return objectDescribeRestService.findByTenantId(arg, context);
    }

    /**
     * 根据接口名称和企业Id查询对象描述
     *
     * @param apiName               接口名称
     * @param include_ref_describe  是否包含关联对象的describe信息
     * @param includeDetailDescribe 是否包含对象详细描述
     * @return 对象描述列表
     */
    @GET
    @Path("/{apiName}")
    public FindDescribeByEidAndApiName.Result findByTenantIdAndDescribeAPIName(@PathParam("apiName") String apiName,
                                                                               @QueryParam("include_ref_describe")
                                                                               String include_ref_describe,
                                                                               @QueryParam("include_detail_describe")
                                                                               Boolean includeDetailDescribe,
                                                                               @QueryParam("include_statistics")
                                                                               Boolean includeStatistics) {
        RequestContext context = RequestContextManager.getContext();
        FindDescribeByEidAndApiName.Arg arg = FindDescribeByEidAndApiName.Arg.builder()
                .apiName(apiName)
                .includeRefDesc(Optional.ofNullable(include_ref_describe).map(Boolean::new).orElse(Boolean.FALSE))
                .includeDetailDesc(Optional.ofNullable(includeDetailDescribe).orElse(Boolean.FALSE))
                .includeStatistics(Optional.ofNullable(includeStatistics).orElse(Boolean.FALSE)).build();
        return objectDescribeRestService.findByTenantIdAndDescribeAPIName(arg, context);
    }

    /**
     * 查询关联它的对象
     *
     * @param apiName 接口名称
     * @return 对象描述
     */
    @GET
    @Path("/{apiName}/ref")
    public FindReferenceDescribes.Result findReferenceObjects(@PathParam("apiName") String apiName) {
        RequestContext context = RequestContextManager.getContext();
        FindReferenceDescribes.Arg arg = FindReferenceDescribes.Arg.builder()
                .apiName(apiName).build();
        return objectDescribeRestService.findByReferenceObjects(arg, context);
    }

    /**
     * 根据api List 查询对象描述
     *
     * @param json api list
     * @return 对象描述列表
     */
    @POST
    @Path("/list")
    public FindDescribeByApiNameList.Result findDescribeByApiNameList(String json, @QueryParam("includeFields") boolean includeFields) {
        RequestContext context = RequestContextManager.getContext();
        List<String> nameList = JSON.parseArray(json, String.class);
        FindDescribeByApiNameList.Arg arg = FindDescribeByApiNameList.Arg.builder()
                .nameList(nameList)
                .includeFields(includeFields)
                .build();
        return objectDescribeRestService.findByApiNameList(arg, context);
    }

    /**
     * 根据id获取自定义对象描述
     *
     * @return 对象描述
     */
    @GET
    @Path("/find_by_id")
    public FindByEidAndDescribeId.Result findByTenantIdAndDescribeId(@QueryParam("describeId")
                                                                     String describeId) {
        RequestContext context = RequestContextManager.getContext();
        FindByEidAndDescribeId.Arg arg = FindByEidAndDescribeId.Arg.builder()
                .describeId(describeId).build();
        return objectDescribeRestService.findByDescribeId(arg, context);
    }

    /**
     * 根据api name更新自定义对象
     *
     * @param describeJson 自定义对象描述
     * @return 更新后的自定义对象
     */
    @PUT
    @Path("/update")
    public UpdateObjectDescribe.Result updateObjectDescribe(String describeJson) {
        RequestContext context = RequestContextManager.getContext();
        UpdateObjectDescribe.Arg arg = UpdateObjectDescribe.Arg.builder()
                .describeJson(describeJson).build();
        return objectDescribeRestService.updateObjectDescribe(arg, context);
    }

    @DELETE
    @Path("/delete_by_id")
    public DeleteObjectDescribe.Result deleteObjectDescribe(@QueryParam("describeId") String describeId) {
        RequestContext context = RequestContextManager.getContext();
        DeleteObjectDescribe.Arg arg = DeleteObjectDescribe.Arg.builder()
                .describeId(describeId).build();
        return objectDescribeRestService.deleteObjectDescribe(arg, context);

    }

    @GET
    @Path("/all")
    public List<FindDisplayNames.Result> findDisplayNames(@QueryParam("isOnlyIncludeCustomObj") boolean onlyIncludeCustomObj,
                                                          @QueryParam("isListPrivilege") boolean listPrivilege,
                                                          @QueryParam("isOnlyActivate") boolean isOnlyActivate,
                                                          @QueryParam("isExcludeDetailObj") boolean isExcludeDetailObj,
                                                          @QueryParam("isExcludeDetailWithMasterCreated") boolean isExcludeDetailWithMasterCreated,
                                                          @QueryParam("checkDetailObjectButton") boolean checkDetailObjectButton,
                                                          @QueryParam("sourceInfo") String sourceInfo,
                                                          @QueryParam("includeChangeOrderObj") Boolean includeChangeOrderObj,
                                                          @QueryParam("includeBigObject") boolean includeBigObject) {
        RequestContext context = RequestContextManager.getContext();
        FindDisplayNames.Arg arg = FindDisplayNames.Arg.builder()
                .onlyIncludeCustomObj(onlyIncludeCustomObj)
                .isOnlyActivate(isOnlyActivate)
                .isExcludeDetailObj(isExcludeDetailObj)
                .isExcludeDetailWithMasterCreated(isExcludeDetailWithMasterCreated)
                .listPrivilege(listPrivilege)
                .checkDetailObjectButton(checkDetailObjectButton)
                .sourceInfo(sourceInfo)
                .includeChangeOrderObj(includeChangeOrderObj)
                .includeBigObject(includeBigObject)
                .build();
        return objectDescribeRestService.findDisplayNames(arg, context);
    }

    @POST
    @Path("/{describeAPIName}/field/{fieldAPIName}")
    public UpdateCustomFieldDescribe.Result updateCustomField(@PathParam("describeAPIName") String describeApiName,
                                                              @PathParam("fieldAPIName") String fieldApiName,
                                                              String json) {
        RequestContext context = RequestContextManager.getContext();
        UpdateCustomFieldDescribe.Arg arg = UpdateCustomFieldDescribe.Arg.builder()
                .describeApiName(describeApiName)
                .fieldApiName(fieldApiName)
                .fieldDescribeJson(json).build();
        return objectDescribeRestService.updateCustomField(arg, context);
    }

    @GET
    @Path("/{describeAPIName}/field/{fieldAPIName}")
    public FindFieldDescribe.Result findFieldDescribe(@PathParam("describeAPIName") String describeApiName,
                                                      @PathParam("fieldAPIName") String fieldApiName) {
        RequestContext context = RequestContextManager.getContext();
        FindFieldDescribe.Arg arg = FindFieldDescribe.Arg.builder()
                .describeApiName(describeApiName)
                .fieldApiName(fieldApiName).build();
        return objectDescribeRestService.findFieldDescribe(arg, context);
    }

    @POST
    @Path("/findBySelectFields")
    public FindBySelectFields.Result findBySelectFields(FindBySelectFields.Arg arg) {
        RequestContext context = RequestContextManager.getContext();
        return objectDescribeRestService.findBySelectFields(arg, context);
    }

    @POST
    @Path("/findAllObjectsByTenantId")
    public FindDescribeApiNames.Result findAllObjectsByTenantId() {
        RequestContext context = RequestContextManager.getContext();
        return objectDescribeRestService.findAllDescribeApiNames(context);
    }

    @POST
    @Path("/queryDisplayNameByApiNames")
    public QueryDisplayNameByApiNames.Result queryDisplayNameByApiNames(QueryDisplayNameByApiNames.Arg arg) {
        RequestContext context = RequestContextManager.getContext();
        return objectDescribeRestService.queryDisplayNameByApiNames(arg, context);
    }

    @POST
    @Path("/findSimpleDetailDescribes")
    public FindSimpleDetailDescribes.Result findSimpleDetailDescribes(FindSimpleDetailDescribes.Arg arg) {
        RequestContext context = RequestContextManager.getContext();
        return objectDescribeRestService.findSimpleDetailDescribes(arg, context);
    }

    @POST
    @Path("/findSimpleDescribes")
    public List<FindDisplayNames.Result> findSimpleDescribes(FindDisplayNames.Arg arg) {
        RequestContext context = RequestContextManager.getContext();
        return objectDescribeRestService.findDisplayNames(arg, context);
    }

    @POST
    @Path("refRecord")
    public RefRecord.Result refRecord(RefRecord.Arg arg) {
        RequestContext context = RequestContextManager.getContext();
        return objectDescribeRestService.recordRef(arg, context);
    }
}
