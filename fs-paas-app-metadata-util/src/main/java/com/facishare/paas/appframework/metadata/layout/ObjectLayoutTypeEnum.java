package com.facishare.paas.appframework.metadata.layout;

import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.ImmutableSet;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @Date : 2024/12/13
 * @Description : 按照存储结构划分的layout type, 对应mt_ui_component的最小存储单元,
 * 区别于 {@link com.facishare.paas.appframework.metadata.layout.LayoutTypes} 是对mt_ui_component.layout_type的枚举
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ObjectLayoutTypeEnum {
    DEFAULT("", ""),
    DETAIL("详情页", "detail"),
    EDIT("新建编辑页", "edit"),
    LIST_LIST("列表页-列表页布局", "list_list"),
    LIST_MOBILE_SUMMARY("列表页-移动端摘要", "list_mobile_summary"),
    PROCESS("流程布局", "process"),
    TODO_APPROVAL_LIST("待办-审批流程-列表页", "todo_approval_list"),
    TODO_APPROVAL_MOBILE_SUMMARY("待办-审批流程-移动端摘要", "todo_approval_mobile_summary"),
    TODO_BPM_LIST("待办-业务流程-列表页", "todo_bpm_list"),
    TODO_BPM_MOBILE_SUMMARY("待办-业务流程-移动端摘要", "todo_bpm_mobile_summary"),
    TODO_STAGE_LIST("待办-阶段任务-列表页", "todo_stage_list"),
    TODO_STAGE_MOBILE_SUMMARY("待办-阶段任务-移动端摘要", "todo_stage_mobile_summary");

    private final static Set<ObjectLayoutTypeEnum> TODOLayoutTypes = ImmutableSet.of(TODO_APPROVAL_LIST, TODO_APPROVAL_MOBILE_SUMMARY,
            TODO_BPM_LIST, TODO_BPM_MOBILE_SUMMARY,
            TODO_STAGE_LIST, TODO_STAGE_MOBILE_SUMMARY);

    private String description;
    private String key;

    // ref_object_api_name
    private static final String APPROVAL_TASK_OBJ = "ApprovalTaskObj";    // 待处理审批流程
    private static final String BPM_TASK = "BpmTask";    // 待处理业务流程
    private static final String STAGE_TASK_OBJ = "StageTaskObj";  // 待处理阶段任务

    // namespace
    private static final String FLOW = "flow";

    public static ObjectLayoutTypeEnum getILayoutType(ILayout layout) {
        if (Objects.isNull(layout)) {
            return DEFAULT;
        }
        return getILayoutType(LayoutExt.of(layout));
    }

    public static ObjectLayoutTypeEnum getILayoutType(LayoutExt layout) {
        String layoutType = layout.getLayoutType();
        String refObjectApiName = layout.getRefObjectApiName();

        if (APPROVAL_TASK_OBJ.equals(refObjectApiName)) {
            return handleApprovalTask(layoutType);
        } else if (BPM_TASK.equals(refObjectApiName)) {
            return handleBpmTask(layoutType);
        } else if (STAGE_TASK_OBJ.equals(refObjectApiName)) {
            return handleStageTask(layoutType);
        }

        String namespace = layout.getNamespace();
        return handleDefaultLayout(layoutType, namespace);
    }

    private static ObjectLayoutTypeEnum handleApprovalTask(String layoutType) {
        if (LayoutTypes.WHAT_LIST.equals(layoutType)) {
            return TODO_APPROVAL_LIST;
        } else if (LayoutTypes.FLOW_TASK_LIST.equals(layoutType)) {
            return TODO_APPROVAL_MOBILE_SUMMARY;
        } else {
            return DEFAULT;
        }
    }

    private static ObjectLayoutTypeEnum handleBpmTask(String layoutType) {
        if (LayoutTypes.WHAT_LIST.equals(layoutType)) {
            return TODO_BPM_LIST;
        } else if (LayoutTypes.FLOW_TASK_LIST.equals(layoutType)) {
            return TODO_BPM_MOBILE_SUMMARY;
        } else {
            return DEFAULT;
        }
    }

    private static ObjectLayoutTypeEnum handleStageTask(String layoutType) {
        if (LayoutTypes.WHAT_LIST.equals(layoutType)) {
            return TODO_STAGE_LIST;
        } else if (LayoutTypes.FLOW_TASK_LIST.equals(layoutType)) {
            return TODO_STAGE_MOBILE_SUMMARY;
        } else {
            return DEFAULT;
        }
    }

    private static ObjectLayoutTypeEnum handleDefaultLayout(String layoutType, String namespace) {
        switch (layoutType) {
            case LayoutTypes.LIST:
                return LIST_MOBILE_SUMMARY;
            case LayoutTypes.EDIT:
                return EDIT;
            case LayoutTypes.LIST_LAYOUT:
                return LIST_LIST;
            case LayoutTypes.DETAIL:
                if (Objects.isNull(namespace)) {
                    return DETAIL;
                } else if (FLOW.equals(namespace)) {
                    return PROCESS;
                } else {
                    return DEFAULT;
                }
            default:
                return DEFAULT;
        }
    }

    public static String getObjectApiName(ILayout layout, ObjectLayoutTypeEnum layoutType) {
        return getObjectApiName(LayoutExt.of(layout), layoutType);
    }

    public static String getObjectApiName(LayoutExt layout, ObjectLayoutTypeEnum layoutType) {
        if (TODOLayoutTypes.contains(layoutType)) {
            return layout.getWhatApiName();
        }
        return layout.getRefObjectApiName();
    }

    public static String getObjectApiName(LayoutExt layout) {
        return getObjectApiName(layout, getILayoutType(layout));
    }
}
