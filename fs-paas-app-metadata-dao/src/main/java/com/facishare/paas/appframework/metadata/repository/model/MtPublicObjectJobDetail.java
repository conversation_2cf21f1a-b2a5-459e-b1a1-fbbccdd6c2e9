package com.facishare.paas.appframework.metadata.repository.model;

import com.facishare.paas.appframework.metadata.repository.annotation.*;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/12/17
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Entity(apiName = MtPublicObjectJobDetail.MT_PUBLIC_OBJECT_JOB_DETAIL_OBJ_API_NAME)
public class MtPublicObjectJobDetail extends BaseEntity {

    public static final String MT_PUBLIC_OBJECT_JOB_DETAIL_OBJ_API_NAME = "MtPublicObjectJobDetailObj";
    public static final String OBJECT_API_NAME = "object_api_name";
    public static final String JOB_ID = "job_id";
    public static final String JOB_STATUS_API_NAME = "job_status";
    public static final String JOB_TYPE_API_NAME = "job_type";
    public static final String JOB_RESULT_API_NAME = "job_result";
    public static final String ENTERPRISE_RELATION_ID_API_NAME = "enterprise_relation_id";
    public static final String DOWNSTREAM_TENANT_ID = "downstream_tenant_id";


    private static final long serialVersionUID = 3706871792360064478L;

    @TextField(field = @ObjectField(apiName = OBJECT_API_NAME))
    private String objectApiName;

    @TextField(field = @ObjectField(apiName = ENTERPRISE_RELATION_ID_API_NAME))
    private String enterpriseRelationId;

    @TextField(field = @ObjectField(apiName = DOWNSTREAM_TENANT_ID))
    private String downstreamTenantId;

    @TextField(field = @ObjectField(apiName = JOB_ID))
    private String jobId;

    @LongTextField(field = @ObjectField(apiName = JOB_RESULT_API_NAME))
    private String result;

    @SelectOneField(field = @ObjectField(apiName = JOB_TYPE_API_NAME))
    private String type;

    @SelectOneField(field = @ObjectField(apiName = JOB_STATUS_API_NAME))
    private String status;

    @TrueOrFalseField(field = @ObjectField(apiName = IObjectData.IS_DELETED, label = IObjectData.IS_DELETED))
    private Boolean deleted;
}
