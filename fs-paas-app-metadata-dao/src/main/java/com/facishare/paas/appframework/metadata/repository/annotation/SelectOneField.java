package com.facishare.paas.appframework.metadata.repository.annotation;

import com.facishare.paas.appframework.metadata.repository.fieldhandler.ObjectFieldType;
import com.facishare.paas.metadata.api.describe.IFieldType;

import java.lang.annotation.*;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16/8/11.
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
@FieldType(type = IFieldType.SELECT_ONE)
public @interface SelectOneField {
    ObjectField field();

    ObjectFieldType fieldType() default ObjectFieldType.STRING;
}
