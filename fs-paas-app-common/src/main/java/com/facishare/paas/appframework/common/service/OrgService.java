package com.facishare.paas.appframework.common.service;

import com.facishare.paas.appframework.common.service.dto.*;
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByDeptIds.DeptStatusEnum;
import com.facishare.paas.appframework.core.model.User;
import com.google.common.base.Strings;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 组织架构服务 <p> Created by liyiguang on 2017/8/18.
 */
public interface OrgService {
    User getUser(String tenantId, String userId);

    Map<String, String> getDeptName(String tenantId, String userId, List<String> userIds);

    List<UserInfo> getUserNameByIds(String tenantId, String userId, List<String> userIds);

    Map<String, UserInfo> getUserInfoMapByIds(String tenantId, String userId, List<String> userIds);

    Map<String, String> getUserNameMapByIds(String tenantId, String userId, List<String> userIds);

    Map<String, QueryDeptInfoByUserIds.MainDeptInfo> getMainDeptInfo(String tenantId, String userId, List<String> userIds);

    List<QueryDeptInfoByUserIds.MainDeptInfo> getDeptInfoByUserIds(String tenantId, String userId, List<String> userIds);

    List<QueryDeptInfoByDeptIds.DeptInfo> getDeptInfoNameByIds(String tenantId, String userId, List<String> idList);

    List<QueryDeptInfoByDeptIds.DeptInfo> getAllDeptInfoNameByIds(String tenantId, String userId, List<String> idList);

    List<QueryDeptInfoByDeptIds.DeptInfo> getDeptInfoByIdsAndStatus(String tenantId, List<String> idList, QueryDeptInfoByDeptIds.DeptStatusEnum status);

    List<QueryDeptInfoByDeptIds.DeptInfo> getDeptInfoNameByIdsAndStatus(String tenantId, String userId, List<String> idList, DeptStatusEnum deptStatus);


    List<UserInfoExt> getUserExtByIds(String tenantId, String userId, List<String> userIds);

    List<String> queryAllSuperDeptByUserId(String tenantId, String userId, String id);

    List<String> queryGroupByUserIds(String tenantId, String userId);

    List<String> getUserIdsByName(String tenantId, String userName, String userId);

    List<QueryDeptInfoByUserIds.MainDeptInfo> queryDeptByUserBatch(String tenantId, String userId, List<String> userIds);

    /**
     * @see OrgService#getDeptInfoByName
     */
    @Deprecated
    List<QueryDeptByName.DeptInfo> getDeptByName(String tenantId, String userId, List<String> names);

    /**
     * 根据部门名称返回部门信息（不含部门编码）
     *
     * @param tenantId
     * @param userId
     * @param names
     * @return
     */
    List<DeptInfo> getDeptInfoByName(String tenantId, String userId, List<String> names);

    List<UserInfo> getUserByName(String tenantId, String userId, List<String> names);

    List<String> getMembersByDeptIds(User user, List<String> deptIds);

    /**
     * 根据部门获取员工
     *
     * @param user
     * @param deptIds
     * @param userStatus null:全部,0:启用,1:停用
     * @return
     */
    List<String> getMembersByDeptIds(User user, List<String> deptIds, Integer userStatus);

    List<String> getMembersByGroupIds(User user, List<String> groupIds);

    List<String> getDeptLeaders(User user, List<String> deptIds);

    List<String> getDeptLeadersByUserIds(User user, List<String> userIds);

    List<String> getReportingObjectsByUserIds(User user, List<String> userIds);

    List<String> getSubDeptByDeptId(String tenantId, String userId, String deptId, boolean isAll);

    List<UserInfo> getSubordinatesByUserId(String tenantId, String userId, String id, boolean isCascade);

    List<String> getSubordinateIdsByUserId(String tenantId, String userId, String id, boolean isCascade);

    Map<String, List<QueryAllSuperDeptsByDeptIds.DeptInfo>> getAllSuperDeptsByDeptIds(String tenantId, String userId, List<String> deptIds);

    Map<String, List<String>> getAllSuperDeptIdsByDeptIds(String tenantId, String userId, List<String> deptIds);

    List<QueryResponsibleDeptsByUserIds.DeptInfo> getResponsibleDeptsByUserIds(String tenantId, String userId, List<String> managerIds, Integer deptStatus);

    List<QueryDeptInfoByUserId.DeptInfo> getDeptInfoByUserId(String tenantId, String userId, String id);

    Map<String, List<QueryDeptByName.DeptInfo>> getSubDeptsByDeptIds(String tenantId, String userId, List<String> deptIds, Integer childDeptStatus);

    Map<String, List<String>> getSubDeptsByDeptIdsMap(String tenantId, String userId, List<String> deptIds, Integer childDeptStatus);

    Map<String, List<QueryMembersByDeptIds.Member>> getMemberMapByDeptIds(User user, List<String> deptIds);

    Map<String, List<QueryMemberInfosByDeptIds.Member>> getMemberInfoMapByDeptIds(User user, List<String> deptIds, Boolean includeLowDept, Integer userStatus, Integer deptUserType);

    List<GetNDeptPathByUserId.DeptInfo> getNDeptPathByUserId(User user, String userId, int n);

    /**
     * @see OrgService#getDeptInfoByNameAndStatus
     */
    @Deprecated
    List<QueryDeptByName.DeptInfo> getDeptByName(String tenantId, String userId, List<String> names, Integer deptStatus);

    /**
     * 根据部门名称和部门状态返回部门信息（不含部门编码）
     *
     * @param tenantId   租户ID
     * @param userId     用户ID
     * @param names      部门名
     * @param deptStatus 部门状态
     * @return 部门信息（不含部门编码）
     */
    List<DeptInfo> getDeptInfoByNameAndStatus(String tenantId, String userId, List<String> names, Integer deptStatus);


    List<UserInfo> getUserByName(String tenantId, String userId, List<String> names, Integer userStatus);

    List<UserInfo> getUserByCodes(User user, List<String> names);

    Map<String, List<UserInfo>> batchQuerySupervisorByUserId(String tenantId, String userId, List<String> userIds);

    List<UserInfo> batchGetResponsibleEmployeeByUserId(String tenantId, String userId, Set<String> userIds);

    Map<String, String> getGroupNameByIds(String tenantId, String userId, List<String> groupIds);

    /**
     * 查询用户组信息
     *
     * @param tenantId
     * @param userId
     * @param groupIds
     * @return
     */
    List<QueryGroupByIds.UserGroupInfo> getGroupInfoByIds(String tenantId, String userId, List<String> groupIds);


    List<QueryGroupByGroupName.UserGroupInfo> getGroupInfoByGroupName(String tenantId, String userId, String groupName, Integer status);

    List<QueryGroupByGroupName.UserGroupInfo> getGroupInfoByGroupNames(String tenantId, String userId, List<String> groupName, Integer status);

    /**
     * 查询企业组信息
     *
     * @param tenantId
     * @param userId
     * @param tenantGroupIds
     * @return
     */
    List<QueryTenantGroupByIds.TenantGroupInfo> getTenantGroupInfoByIds(String tenantId, String userId, List<String> tenantGroupIds);

    /**
     * 批量获取外部人员信息
     *
     * @param tenantId   上游企业ID
     * @param outUserIds 外部人员ID集合
     * @return 外部人员信息集合
     */
    List<OutUserInfo> batchGetOutUsers(String tenantId, Set<String> outUserIds);

    OrganizationInfo findMainOrgAndDeptByUserId(String tenantId, String userId, Collection<String> userIds);

    List<DeptInfo> batchGetDeptInfosByDeptCodes(User user, List<String> deptCodes);

    List<DeptInfo> batchGetDeptInfosByDeptIds(User user, List<String> deptIds);

    List<QueryDeptInfoByDeptIds.DeptInfo> batchGetOutDeptInfosByDeptIds(User user, List<String> deptIds, QueryDeptInfoByDeptIds.DeptStatusEnum status);

    default User fillUserName(User user) {
        User newUser = user;
        if (user.isOutUser() || Strings.isNullOrEmpty(user.getUserName())) {
            newUser = getUser(user.getTenantId(), user.getUserIdOrOutUserIdIfOutUser());
        }
        return newUser;
    }
}

