package com.facishare.paas.appframework.common.service.dto;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 批量删除引用
 * <AUTHOR>
 * @Description TODO
 * @date 2021/10/18-11:33
 */
public interface BatchDeleteReference {
    @Data
    @Builder
    class Arg {
        private List<ReferenceData> items;
    }
    @Data
    @Builder
    class ReferenceData {
        private String sourceType;
        private String sourceValue;
        private String targetValue;
    }

    @Data
    class Result {
        private Integer code;
        private String message;
    }
}
