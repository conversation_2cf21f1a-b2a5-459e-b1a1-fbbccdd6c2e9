package com.facishare.paas.appframework.fcp;

import com.facishare.fcp.annotation.FcpMethod;
import com.facishare.fcp.annotation.FcpService;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.service.ObjectLayoutService;
import com.facishare.paas.appframework.core.predef.service.dto.layout.*;
import com.facishare.paas.appframework.core.model.ContextManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by zhouwr on 2017/10/26
 */
@Component
@FcpService("layout")
public class LayoutController {

    @Autowired
    private ObjectLayoutService objectLayoutService;

    /**
     * 新建layout
     */
    @FcpMethod("createLayout")
    public CreateLayout.Result createLayout(CreateLayout.Arg arg) {
        ServiceContext context = ContextManager.buildServiceContext("layout", "createLayout");
        return objectLayoutService.createLayout(arg, context);
    }

    /**
     * 新建layout
     */
    @FcpMethod("create")
    public CreateLayout.Result create(CreateLayout.Arg arg) {
        ServiceContext context = ContextManager.buildServiceContext("layout", "createLayout");
        return objectLayoutService.createLayout(arg, context);
    }

    /**
     * 更新layout
     */
    @FcpMethod("updateLayout")
    public UpdateLayout.Result updateLayout(UpdateLayout.Arg arg) {
        ServiceContext context = ContextManager.buildServiceContext("layout", "updateLayout");
        return objectLayoutService.updateLayout(arg, context);
    }

    /**
     * 更新layout
     */
    @FcpMethod("update")
    public UpdateLayout.Result update(UpdateLayout.Arg arg) {
        ServiceContext context = ContextManager.buildServiceContext("layout", "updateLayout");
        return objectLayoutService.updateLayout(arg, context);
    }

    /**
     * 删除layout
     */
    @FcpMethod("deleteLayout")
    public DeleteLayout.Result deleteLayout(DeleteLayout.Arg arg) {
        ServiceContext context = ContextManager.buildServiceContext("layout", "deleteLayout");
        return objectLayoutService.deleteLayout(arg, context);
    }

    /**
     * 查找layout
     */
    @FcpMethod("findLayout")
    public FindLayout.Result findLayout(FindLayout.Arg arg) {
        ServiceContext context = ContextManager.buildServiceContext("layout", "findLayout");
        return objectLayoutService.findLayout(arg, context);
    }

    /**
     * 查找列表类型layout
     */
    @FcpMethod("findListLayout")
    public FindMobileListLayout.Result findMobileListLayout(FindMobileListLayout.Arg arg) {
        ServiceContext context = ContextManager.buildServiceContext("layout", "findListLayout");
        return objectLayoutService.findMobileListLayout(arg, context);
    }

    /**
     * 查找detail类型layout列表
     */
    @FcpMethod("findDetailLayoutList")
    public FindDetailLayoutList.Result findDetailLayoutList(FindDetailLayoutList.Arg arg) {
        ServiceContext context = ContextManager.buildServiceContext("layout", "findDetailLayoutList");
        return objectLayoutService.findDetailLayoutList(arg, context);
    }

    /**
     * 查询对象的布局列表
     *
     * @param arg
     * @return
     */
    @FcpMethod("findByObjectDescribeApiName")
    public FindByObjectApiName.Result findByObjectApiName(FindByObjectApiName.Arg arg) {
        ServiceContext context = ContextManager.buildServiceContext("layout", "findByObjectApiName");
        return objectLayoutService.findByObjectApiName(arg, context);
    }

    /**
     * 创建layout并且更新describe
     */
    @FcpMethod("createAndUpdateDescribe")
    public CreateAndUpdateDescribe.Result createLayoutAndUpdateDescribe(CreateAndUpdateDescribe.Arg arg) {
        ServiceContext context = ContextManager.buildServiceContext("layout", "createAndUpdateDescribe");
        return objectLayoutService.createLayoutAndUpdateDescribe(arg, context);
    }

    /**
     * 更新layout并且更新describe
     */
    @FcpMethod("updateLayoutAndUpdateDescribe")
    public UpdateLayoutAndUpdateDescribe.Result updateLayoutAndUpdateDescribe(UpdateLayoutAndUpdateDescribe.Arg arg) {
        ServiceContext context = ContextManager.buildServiceContext("layout", "updateLayoutAndUpdateDescribe");
        return objectLayoutService.updateLayoutAndUpdateDescribe(arg, context);
    }

    /**
     * 根据对象的apiName查询layout列表
     */
    @FcpMethod("findByObjDescribeApiName")
    public FindByObjDescribeApiName.Result findByObjectDescribeApiName(FindByObjDescribeApiName.Arg arg) {
        ServiceContext context = ContextManager.buildServiceContext("layout", "findByObjDescribeApiName");
        return objectLayoutService.findByObjectDescribeApiName(arg, context);
    }
}
