package com.facishare.paas.appframework.privilege.dto;

import lombok.*;

public class UpdateTeamRoleStatusModel {

  @EqualsAndHashCode(callSuper = true)
  @Data
  @Builder
  @NoArgsConstructor
  @AllArgsConstructor
  public static class Arg extends BasePrivilegeArg {

    private String roleType;
    private Integer status;

  }


  @EqualsAndHashCode(callSuper = true)
  @Data
  public static class Result extends BasePrivilegeResult {

  }
}
