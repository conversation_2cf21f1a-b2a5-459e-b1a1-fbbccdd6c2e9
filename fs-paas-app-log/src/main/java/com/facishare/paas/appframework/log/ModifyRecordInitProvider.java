package com.facishare.paas.appframework.log;

import com.facishare.paas.appframework.common.service.dto.TenantInfo;
import com.facishare.paas.appframework.log.dto.LogInfo;
import com.facishare.paas.appframework.log.dto.ModifyRecord;
import com.facishare.paas.appframework.log.dto.TeamMemberInfo;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.TeamRoleInfo;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

import java.util.List;
import java.util.Map;

public interface ModifyRecordInitProvider {

    String getApiName();

    ModifyRecord getModifyRecord(LogInfo logInfo);

    void logTeamMember(IObjectData oldData,
                       IObjectData data,
                       IObjectDescribe objectDescribe,
                       Map<String, List<TeamMemberInfo.Member>> memberInfos,
                       List<TeamMemberInfo.Msg> msgList,
                       List<LogInfo.LintMessage> textMsg,
                       List<TeamRoleInfo> teamRoleInfos);

    void logTeamMember(IObjectData oldData,
                       IObjectData data,
                       IObjectDescribe objectDescribe,
                       Map<String, List<TeamMemberInfo.Member>> memberInfos,
                       List<TeamMemberInfo.Msg> msgList,
                       List<LogInfo.LintMessage> textMsg,
                       Map<String, TenantInfo> userTenantMap,
                       List<TeamRoleInfo> teamRoleInfos);

}
