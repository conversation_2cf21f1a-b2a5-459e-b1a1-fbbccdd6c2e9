package com.facishare.paas.appframework.log.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public interface LogAnalysis {


    @Data
    class Arg {
        private List<OperationLog> operationLogArgs;
        private Boolean operationLog;
        private Boolean loginLog;
    }

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        private List<OperationLog> operationLogResult;
        private Boolean loginLog;
        private Boolean operationLog;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class OperationLog {
        private String describeApiName;
        private List<String> operation;
        private String displayName;
    }

    enum LogAnalysisType {
        LOGINLOG_ANALYSIS("loginlog_analysis"),
        AUDITLOG_ANALYSIS("auditlog_analysis");

        LogAnalysisType(String type) {
            this.type = type;
        }

        private String type;

        public String getType() {
            return type;
        }
    }
}
