package com.facishare.open.center.fcp.utils;

import com.facishare.open.app.center.api.constants.CommonConstant;
import com.facishare.open.app.center.api.model.OpenAppDO;
import com.facishare.open.app.center.api.model.enums.AppCenterEnum;
import com.facishare.open.app.center.api.model.enums.TargetTypeEnum;
import com.facishare.open.app.center.api.model.enums.TryStatusEnum;
import com.facishare.open.center.fcp.model.vos.ComponentVO;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfigFactory;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since on 2016/1/7.
 */
@Service
public class BizCommonUtil {
    private static IConfigFactory factory = ConfigFactory.getInstance();

    /**
     * 图片地址.
     */
    private static String imageUrl = "http://www.fsfte2.com/open/appcenter/image/view?v=1.2&type=logo";
    /**
     * 应用的明细页跳转地址.
     */
    private static String detailUrl = "http://www.fsfte2.com/open/h5/detail.html?appId=";

    /**
     * 购买页跳转地址.
     */
    private static String targetUrl = "http://www.fsfte2.com/open/h5/copyright.html?appId=";

    /**
     * 用于验证是否官方，官方开发者id 用,分开
     */
    private static List<String> officialDevIds = Lists.newArrayList("1");

    private static void setOfficialDevIds(String officialDevIdsString, List<String> officialDevIdsDefault) {
        officialDevIds = officialDevIdsDefault;
        if (null != officialDevIdsString) {
            officialDevIds = Arrays.asList(officialDevIdsString.split(","));
        }
    }

    /**
     * 营销套件相关配置.
     */
    private static String marketSuiteAppId = "FSAID_MAKETING";
    private static String marketSuiteAppName = "营销套件"; // ignoreI18n
    private static String marketSuiteDetailUrl = "http://www.fsfte2.com/mob/apphtml/moreapp.html";
    private static String marketSuiteDesc = "";
    private static String marketSuiteImageUrl = "http://open.fsfte2.com/fscdn/img?imgId=group1/M00/00/DE/rB9ndlaUw--AH989AAAKe4iOlI4960.png";

    //试用
    private static String TRY_URL = "http://www.fsfte2.com/open/h5/trial.html?appId=";
    //授权
    private static String AUTH_URL = "http://www.fsfte2.com/open/h5/authorization.html?appId=";
    //购买电话号码
    public static String PHONE_NUM = "4001234567";

    public static String BIND_URL_SIG = "#FS_OAUTH_CALLBACK_SIG";

    //version >= 5.4 过滤考勤统计.
    public static String FS_BROKER_ATTENDANCE_STATISTICS_COMPONENT_ID = "FSAID_9896c5";
    //crm组件id
    public static String FS_BROKER_CRM_COMPONENT_ID = "FSAID_5f5e229";

    //pk 助手
    public static String FS_BROKER_PK_COMPONENT_ID = "FSAID_5f5e51d";
    public static String FS_BROKER_PK_APP_ID = "FSAID_5f5e51b";

    //会议助手
    public static String FS_BROKER_MEETING_COMPONENT_ID = "FSAID_5f5e520";
    public static String FS_BROKER_MEETING_APP_ID = "FSAID_5f5e51e";

    //企信组件id
    public static String FS_QI_XIN_COMPONENT_ID = "FSAID_989797";
    //工作台组件id
    public static String FS_WORKBENCH_COMPONENT_ID = "FSAID_989799";

    //crm前端是否展示 tt
    private static String SHOW_CRM = "false";

    //是否走5.2新逻辑
    private static String NEW_LOGICAL = "false";

    //需要过滤的组件 字符串形式：5.3,smpComponentId;5.3,pxzsComponentId
    public static String ALL_FILTER_COMPONENT_RULES = "";

    public static Map<String, String> authKeyConfig = new HashMap<>();

    private static final String GRAY_REL_NAME = "app-center";//灰度名称
    private static final String GRAY_REL_SERVICE_5_4 = "service5_4";//多客服5.4

    // 附近客户的组件id.
    public static String nearbyCustomerComponentId = "FSAID_9896c3";

    //企业钱包的"企业钱包"app组件
    public static String E_WALLET_APP_COMPONENT_ID = "FSAID_989a79";

    public static List<String> E_LINK_APP_IDS = new ArrayList<>();
    public static List<String> WX_LINK_APP_IDS = new ArrayList<>();
    private static ComponentVO E_LINK_COMPONENT_VO = new ComponentVO();
    private static ComponentVO WX_LINK_COMPONENT_VO = new ComponentVO();
    private static ComponentVO BIG_ENTERPRISE_LINK_COMPONENT_VO = new ComponentVO();
    private static ComponentVO BIG_CUSTOMER_LINK_COMPONENT_VO = new ComponentVO();

    // 培训助手app 组件id
    public static String TRAIN_APP_COMPONENT_ID = "FSAID_5f5e52e";

    //需要强制不更新的模块的moduleKey列表.
    private static List<String> FORCE_NOT_UPDATE_MODULE_KEY_LIST = new ArrayList<>();

    static {
        E_LINK_COMPONENT_VO.setComponentId("FSAID_E_LINK");
        E_LINK_COMPONENT_VO.setComponentName("企业互联"); // ignoreI18n
        E_LINK_COMPONENT_VO.setAppId(E_LINK_COMPONENT_VO.getComponentId());
        E_LINK_COMPONENT_VO.setIsNew(CommonConstant.NO);
        E_LINK_COMPONENT_VO.setParentId("");
        E_LINK_COMPONENT_VO.setDesc("");
        E_LINK_COMPONENT_VO.setPosition(2);
        E_LINK_COMPONENT_VO.setIsGroup(CommonConstant.YES);
        E_LINK_COMPONENT_VO.setImageUrl("https://open.fxiaoke.com/fscdn/bj/imgTxtView?imgId=A_201701_21_47c47555e4d840ec9e04fad574b11a93.png&s=670");
        E_LINK_COMPONENT_VO.setCallBackUrl("fs://elink");

        WX_LINK_COMPONENT_VO.setComponentId("FSAID_WX_LINK");
        WX_LINK_COMPONENT_VO.setComponentName("企业互联-微信"); // ignoreI18n
        WX_LINK_COMPONENT_VO.setAppId(WX_LINK_COMPONENT_VO.getComponentId());
        WX_LINK_COMPONENT_VO.setIsNew(CommonConstant.NO);
        WX_LINK_COMPONENT_VO.setParentId("");
        WX_LINK_COMPONENT_VO.setDesc("");
        WX_LINK_COMPONENT_VO.setPosition(2);
        WX_LINK_COMPONENT_VO.setIsGroup(CommonConstant.YES);
        WX_LINK_COMPONENT_VO.setImageUrl("https://open.fxiaoke.com/fscdn/bj/imgTxtView?imgId=A_201701_21_b46cf77cb8cb45ae972c5fcf26476ef5.png&s=670");
        WX_LINK_COMPONENT_VO.setCallBackUrl("fs://elink");


        BIG_ENTERPRISE_LINK_COMPONENT_VO.setComponentId("FSAID_ENETERPRISE_LINK");
        BIG_ENTERPRISE_LINK_COMPONENT_VO.setComponentName("企业互联"); // ignoreI18n
        BIG_ENTERPRISE_LINK_COMPONENT_VO.setAppId(BIG_ENTERPRISE_LINK_COMPONENT_VO.getComponentId());
        BIG_ENTERPRISE_LINK_COMPONENT_VO.setIsNew(CommonConstant.NO);
        BIG_ENTERPRISE_LINK_COMPONENT_VO.setParentId("");
        BIG_ENTERPRISE_LINK_COMPONENT_VO.setDesc("");
        BIG_ENTERPRISE_LINK_COMPONENT_VO.setPosition(2);
        BIG_ENTERPRISE_LINK_COMPONENT_VO.setIsGroup(CommonConstant.YES);
        BIG_ENTERPRISE_LINK_COMPONENT_VO.setImageUrl(
                "https://a0.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201705_22_2cc2d51abda54506bad64e32b97a6488.png&size=150_150&ea=appCenter");
        BIG_ENTERPRISE_LINK_COMPONENT_VO.setCallBackUrl("fs://BigEnterpriseLink");

        BIG_CUSTOMER_LINK_COMPONENT_VO.setComponentId("FSAID_CUSTOMER_LINK");
        BIG_CUSTOMER_LINK_COMPONENT_VO.setComponentName("客户互联"); // ignoreI18n
        BIG_CUSTOMER_LINK_COMPONENT_VO.setAppId(BIG_CUSTOMER_LINK_COMPONENT_VO.getComponentId());
        BIG_CUSTOMER_LINK_COMPONENT_VO.setIsNew(CommonConstant.NO);
        BIG_CUSTOMER_LINK_COMPONENT_VO.setParentId("");
        BIG_CUSTOMER_LINK_COMPONENT_VO.setDesc("");
        BIG_CUSTOMER_LINK_COMPONENT_VO.setPosition(2);
        BIG_CUSTOMER_LINK_COMPONENT_VO.setIsGroup(CommonConstant.YES);
        BIG_CUSTOMER_LINK_COMPONENT_VO.setImageUrl(
                "https://a1.fspage.com/FSC/EM/Avatar/GetAvatar?path=N_201806_25_7056d49ced814f4ca606cfad3674ed59.png&size=150_150&ea=appCenter");
        BIG_CUSTOMER_LINK_COMPONENT_VO.setCallBackUrl("fs://BigCustomerLink");
    }

    private static void setAuthKeyConfig(String config, Map<String, String> authKeyConfigDefault) {
        authKeyConfig = authKeyConfigDefault;
        if (null != config) {
            @SuppressWarnings("unchecked")
            final HashMap<String, String> hashMap = new Gson().fromJson(config, HashMap.class);
            authKeyConfig = hashMap;
        }
    }

    static {
        authKeyConfig.put("MEETING_SEND", "FSAID_5f5e26c");
        factory.getConfig("fs-broker-app-biz", config -> {
            imageUrl = config.get("FS.BROKER.IMAGE.URL", imageUrl);
            detailUrl = config.get("FS.BROKER.APP.DETAIL.URL", detailUrl);
            targetUrl = config.get("FS.BROKER.APP.TARGET.URL", targetUrl);
            setOfficialDevIds(config.get("FS.BROKER.APP.CENTER.APP.OFFICIAL.DEV.IDS", null), officialDevIds);
            marketSuiteAppId = config.get("FS.BROKER.APP.MARKETING.SUITE.APP.ID", marketSuiteAppId);
            marketSuiteAppName = config.get("FS.BROKER.APP.MARKETING.SUITE.APP.NAME", marketSuiteAppName);
            marketSuiteDetailUrl = config.get("FS.BROKER.APP.MARKETING.SUITE.DETAIL.URL", marketSuiteDetailUrl);
            marketSuiteDesc = config.get("FS.BROKER.APP.MARKETING.SUITE.DESC", marketSuiteDesc);
            marketSuiteImageUrl = config.get("FS.BROKER.APP.MARKETING.SUITE.IMAGE.URL", marketSuiteImageUrl);

            TRY_URL = config.get("FS.BROKER.APP.CENTER.TRY.URL", TRY_URL);
            AUTH_URL = config.get("FS.BROKER.APP.CENTER.AUTH.URL", AUTH_URL);
            PHONE_NUM = config.get("FS.BROKER.APP.CENTER.BUY.PHONE.NUM", PHONE_NUM);
            marketSuiteImageUrl = config.get("FS.BROKER.APP.MARKETING.SUITE.IMAGE.URL", marketSuiteImageUrl);
            marketSuiteImageUrl = config.get("FS.BROKER.APP.MARKETING.SUITE.IMAGE.URL", marketSuiteImageUrl);

            BIND_URL_SIG = config.get("FS.BROKER.OAUTH.CALLBACK.SIG", BIND_URL_SIG);
            FS_BROKER_ATTENDANCE_STATISTICS_COMPONENT_ID = config.get("FS_BROKER_ATTENDANCE_STATISTICS_COMPONENT_ID",
                    FS_BROKER_ATTENDANCE_STATISTICS_COMPONENT_ID);
            FS_BROKER_CRM_COMPONENT_ID = config.get("FS.BROKER.CRM.COMPONENT.ID", FS_BROKER_CRM_COMPONENT_ID);
            FS_QI_XIN_COMPONENT_ID = config.get("FS_QI_XIN_COMPONENT_ID", FS_QI_XIN_COMPONENT_ID);
            FS_WORKBENCH_COMPONENT_ID = config.get("FS_WORKBENCH_COMPONENT_ID", FS_WORKBENCH_COMPONENT_ID);
            FS_BROKER_PK_COMPONENT_ID = config.get("FS_BROKER_PK_COMPONENT_ID", FS_BROKER_PK_COMPONENT_ID);
            FS_BROKER_MEETING_COMPONENT_ID = config.get("FS_BROKER_MEETING_COMPONENT_ID", FS_BROKER_MEETING_COMPONENT_ID);
            SHOW_CRM = config.get("SHOW_CRM", SHOW_CRM);
            NEW_LOGICAL = config.get("NEW_LOGICAL", NEW_LOGICAL);
            PHONE_NUM = config.get("PHONE_NUM", PHONE_NUM);
            FS_BROKER_PK_APP_ID = config.get("FS_BROKER_PK_APP_ID", FS_BROKER_PK_APP_ID);
            FS_BROKER_MEETING_APP_ID = config.get("FS_BROKER_MEETING_APP_ID", FS_BROKER_MEETING_APP_ID);
            ALL_FILTER_COMPONENT_RULES = config.get("ALL_FILTER_COMPONENT_RULES", ALL_FILTER_COMPONENT_RULES);
            setAuthKeyConfig(config.get("FS.BROKER.AUTH.KEY.CONFIG", null), authKeyConfig);

            nearbyCustomerComponentId = config.get("nearbyCustomerComponentId", nearbyCustomerComponentId);
            TRAIN_APP_COMPONENT_ID = config.get("TRAIN_APP_COMPONENT_ID", TRAIN_APP_COMPONENT_ID);

            E_LINK_COMPONENT_VO.setComponentId(config.get("E_LINK_COMPONENT_ID", E_LINK_COMPONENT_VO.getComponentId()));
            E_LINK_COMPONENT_VO.setComponentName(config.get("E_LINK_COMPONENT_NAME", E_LINK_COMPONENT_VO.getComponentName()));
            E_LINK_COMPONENT_VO.setAppId(config.get("E_LINK_APP_ID", E_LINK_COMPONENT_VO.getAppId()));
            E_LINK_COMPONENT_VO.setIsNew(config.getInt("E_LINK_IS_NEW", E_LINK_COMPONENT_VO.getIsNew()));
            E_LINK_COMPONENT_VO.setParentId(config.get("E_LINK_PARENT_ID", E_LINK_COMPONENT_VO.getParentId()));
            E_LINK_COMPONENT_VO.setDesc(config.get("E_LINK_DESC", E_LINK_COMPONENT_VO.getDesc()));
            E_LINK_COMPONENT_VO.setImageUrl(config.get("E_LINK_IMAGE_URL", E_LINK_COMPONENT_VO.getImageUrl()));
            E_LINK_COMPONENT_VO.setCallBackUrl(config.get("E_LINK_CALL_BACK_URL", E_LINK_COMPONENT_VO.getCallBackUrl()));

            WX_LINK_COMPONENT_VO.setComponentId(config.get("WX_LINK_COMPONENT_ID", WX_LINK_COMPONENT_VO.getComponentId()));
            WX_LINK_COMPONENT_VO.setComponentName(config.get("WX_LINK_COMPONENT_NAME", WX_LINK_COMPONENT_VO.getComponentName()));
            WX_LINK_COMPONENT_VO.setAppId(config.get("WX_LINK_APP_ID", WX_LINK_COMPONENT_VO.getAppId()));
            WX_LINK_COMPONENT_VO.setIsNew(config.getInt("WX_LINK_IS_NEW", WX_LINK_COMPONENT_VO.getIsNew()));
            WX_LINK_COMPONENT_VO.setParentId(config.get("WX_LINK_PARENT_ID", WX_LINK_COMPONENT_VO.getParentId()));
            WX_LINK_COMPONENT_VO.setDesc(config.get("WX_LINK_DESC", WX_LINK_COMPONENT_VO.getDesc()));
            WX_LINK_COMPONENT_VO.setImageUrl(config.get("WX_LINK_IMAGE_URL", WX_LINK_COMPONENT_VO.getImageUrl()));
            WX_LINK_COMPONENT_VO.setCallBackUrl(config.get("WX_LINK_CALL_BACK_URL", WX_LINK_COMPONENT_VO.getCallBackUrl()));

            BIG_ENTERPRISE_LINK_COMPONENT_VO.setComponentId(
                    config.get("BIG_ENTERPRISE_LINK_COMPONENT_ID", BIG_ENTERPRISE_LINK_COMPONENT_VO.getComponentId()));
            BIG_ENTERPRISE_LINK_COMPONENT_VO.setComponentName(
                    config.get("BIG_ENTERPRISE_LINK_COMPONENT_NAME", BIG_ENTERPRISE_LINK_COMPONENT_VO.getComponentName()));
            BIG_ENTERPRISE_LINK_COMPONENT_VO.setAppId(config.get("BIG_ENTERPRISE_LINK_APP_ID", BIG_ENTERPRISE_LINK_COMPONENT_VO.getAppId()));
            BIG_ENTERPRISE_LINK_COMPONENT_VO.setIsNew(config.getInt("BIG_ENTERPRISE_LINK_IS_NEW", BIG_ENTERPRISE_LINK_COMPONENT_VO.getIsNew()));
            BIG_ENTERPRISE_LINK_COMPONENT_VO.setParentId(
                    config.get("BIG_ENTERPRISE_LINK_PARENT_ID", BIG_ENTERPRISE_LINK_COMPONENT_VO.getParentId()));
            BIG_ENTERPRISE_LINK_COMPONENT_VO.setDesc(config.get("BIG_ENTERPRISE_LINK_DESC", BIG_ENTERPRISE_LINK_COMPONENT_VO.getDesc()));
            BIG_ENTERPRISE_LINK_COMPONENT_VO.setImageUrl(
                    config.get("BIG_ENTERPRISE_LINK_IMAGE_URL", BIG_ENTERPRISE_LINK_COMPONENT_VO.getImageUrl()));
            BIG_ENTERPRISE_LINK_COMPONENT_VO.setCallBackUrl(
                    config.get("BIG_ENTERPRISE_LINK_CALL_BACK_URL", BIG_ENTERPRISE_LINK_COMPONENT_VO.getCallBackUrl()));
            //以逗号分割.
            FORCE_NOT_UPDATE_MODULE_KEY_LIST = Arrays.asList(config.get("FORCE_NOT_UPDATE_MODULE_KEY_LIST", "").split(","));
        });

        factory.getConfig("fs-open-app-center-common", (config -> {
            E_WALLET_APP_COMPONENT_ID = config.get("E_WALLET_APP_COMPONENT_ID", E_WALLET_APP_COMPONENT_ID);
            E_LINK_APP_IDS = Arrays.asList(config.get("E_LINK_APP_IDS", "").split(","));
            WX_LINK_APP_IDS = Arrays.asList(config.get("WX_LINK_APP_IDS", "").split(","));
        }));
    }

    public static int getTargetType(TryStatusEnum tryStatusEnum) {
        //但是现在统一为跳转到应用详情
        final boolean isAppAdded = APP_ADDED_STATUS.contains(tryStatusEnum);
        return isAppAdded ? TargetTypeEnum.GRAY.getCode() : TargetTypeEnum.URL.getCode();
    }

    private static final ImmutableSet<TryStatusEnum> APP_ADDED_STATUS = ImmutableSet.of(
            TryStatusEnum.ADMIN_ADDED, TryStatusEnum.ADMIN_TRYING, TryStatusEnum.BOUGHT, TryStatusEnum.EMPLOYEE_TRYING);

    public static String getTargetText(TryStatusEnum tryStatusEnum) {
        final boolean isAppAdded = APP_ADDED_STATUS.contains(tryStatusEnum);
        return isAppAdded ? "已添加" : "添加"; //新的分版销售，只需要显示"添加","已添加"状态 // ignoreI18n
    }

    public static void main(String[] args) {
        for (TryStatusEnum tryStatusEnum : TryStatusEnum.values()) {
            System.out.println("old" + tryStatusEnum.getText());

            System.out.println("new" + getTargetText(tryStatusEnum));
        }
    }


    public static String getTargetUrl(String appId, TryStatusEnum tryStatusEnum) {
//        return detailUrl + appId; //不支持在线添加，全部都是弹出提示
//        if (TryUrlEnum.TRY_URL == tryStatusEnum.getTryUrlEnum()) {
//            return TRY_URL + appId;
//        }
//
//        if (TryUrlEnum.AUTH_URL == tryStatusEnum.getTryUrlEnum()) {
//            return AUTH_URL + appId;
//        }
        //个人可添加 跳转 通知管理员开通页面
//        if (TryUrlEnum.NOTIFY_ADMIN_URL == tryStatusEnum.getTryUrlEnum()) {
//            return targetUrl + appId;
//        }

        return detailUrl + appId;  //不能直接添加（免费添加，试用，购买)的应用，全部跳转到详情
    }

    public static String getAppDtlUrl(String appId) {
        return detailUrl + appId;
    }

    public static String getAppImageUrl(String appId) {


        return imageUrl + "&appId=" + appId + "&ts=" + new Date().getTime();
    }

    public static int getMaxBestAppSize() {
        return 3;
    }

    public static boolean isOfficialApp(OpenAppDO openApp) {
        return AppCenterEnum.AppType.DEV_APP.value() == openApp.getAppType() && officialDevIds
                .contains(openApp.getAppCreater());
    }


    /**
     * 获取大版本号（例如5.2.1 返回5.2）.
     */
    public static String getBigVersion(String version) {

        if (StringUtils.isEmpty(version)) {
            return version;
        }

        String[] splits = version.split("\\.");
        if (splits.length >= 3) {
            return version.substring(0, splits[0].length() + splits[1].length() + 1);
        }

        return version;
    }

    // 组件cope服务类.
    private static final BeanCopier COMPONENT_VO_COPIER = BeanCopier.create(ComponentVO.class, ComponentVO.class, false);

    /**
     * 企业互联的纷享百川组件.
     */
    public static ComponentVO getELinkComponentVO() {
        ComponentVO componentVO = new ComponentVO();
        COMPONENT_VO_COPIER.copy(E_LINK_COMPONENT_VO, componentVO, null);
        return componentVO;
    }

    /**
     * 企业互联的微信百川组件.
     */
    public static ComponentVO getWxLinkComponentVO() {
        ComponentVO componentVO = new ComponentVO();
        COMPONENT_VO_COPIER.copy(WX_LINK_COMPONENT_VO, componentVO, null);
        return componentVO;
    }

    /**
     * 是否指定模块强制不更新.
     *
     * @param moduleKey 模块名.
     */
    public static boolean isForceNotUpdate(String moduleKey) {
        return FORCE_NOT_UPDATE_MODULE_KEY_LIST.contains(moduleKey);
    }

    /**
     * 企业互联入口
     */
    public static ComponentVO getBigEnterpriseLinkComponentVo() {
        ComponentVO componentVO = new ComponentVO();
        COMPONENT_VO_COPIER.copy(BIG_ENTERPRISE_LINK_COMPONENT_VO, componentVO, null);
        return componentVO;
    }

    /**
     * 客户互联入口
     */
    public static ComponentVO getBigCustomerLinkComponentVo() {
        ComponentVO componentVO = new ComponentVO();
        COMPONENT_VO_COPIER.copy(BIG_CUSTOMER_LINK_COMPONENT_VO, componentVO, null);
        return componentVO;
    }

}
