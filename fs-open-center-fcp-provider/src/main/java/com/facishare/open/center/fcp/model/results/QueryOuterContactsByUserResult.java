package com.facishare.open.center.fcp.model.results;

import com.alibaba.fastjson.annotation.JSONField;
import com.dyuproject.protostuff.Tag;
import com.facishare.open.center.fcp.model.vos.OuterContactsFCPVO;

import java.util.List;

/**
 * 查询作为服务专员的外部联系人接口返回.
 * Created by zenglb on 2016/11/17.
 */
public class QueryOuterContactsByUserResult extends BaseFcpResult {
    private static final long serialVersionUID = 8463176327440102805L;

    /**
     * 外部联系人列表.
     */
    @Tag(11)
    @JSONField(name = "M11")
    private List<OuterContactsFCPVO> outerContactsList;

    public List<OuterContactsFCPVO> getOuterContactsList() {
        return outerContactsList;
    }

    public void setOuterContactsList(List<OuterContactsFCPVO> outerContactsList) {
        this.outerContactsList = outerContactsList;
    }
}
