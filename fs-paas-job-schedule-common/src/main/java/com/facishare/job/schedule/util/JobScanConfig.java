package com.facishare.job.schedule.util;

import com.alibaba.fastjson.JSON;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.google.common.base.Strings;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.PostConstruct;

@Slf4j
public class JobScanConfig {

    private String configName;

    public volatile static Config config;

    public void setConfigName(String configName) {
        this.configName = configName;
    }

    @PostConstruct
    public void init() {
        ConfigFactory.getInstance().getConfig(configName, config -> {
            reload(config);
        });
    }

    private void reload(IConfig conf) {
        String content = new String(conf.getContent());
        if (Strings.isNullOrEmpty(content)) {
            log.error("{} config content is empty", configName);
        }
        config = JSON.parseObject(content, Config.class);
        log.info("reload config:{},content:{}", configName, config);
    }

    @Data
    public static class Config {
        //查数据接口一批查询数据的条数
        int scanDavinciDataIdBatch = 100;

        int scanFormulaDataIdBatch = 100;

        int scanFormulaMetaDataIdBatch = 1000; //扫描元数据数据量

        int scanSfaDataIdBatch = 50;

        int functionDataIdBatch = 20;

        int functionDataIdMaxBatch = 100;

        //负载上限
        int functionDataLoadRateMax = 50;

        //"asc"或者"ASC"代表升序；其他为降序
        String davinciOrder = "desc";

        String formulaOrder = "asc";

        //强制终止间隔时间（小时）
        int forceEndIntervalHour = 2;

        //dataId查询间隔时间（毫秒）
        int scanDavinciDataIdSleepMillis = 10;

        int scanSfaDataIdSleepMillis = 10;

        int scanFormulaDataIdSleepMillis = 10;

        //redis查询间隔批次
        int checkJobCanceledBatch = 10;

        //正在处理状态的任务扫描间隔时间 (分钟)
        int retryIntervalMinute = 60;

        //单个节点任务限制执行总条数
        int davinciLimitCount = 5;

        //单个节点限制执行总条数
        int formulaLimitCount = 5;

        //刷库任务开关
        boolean formulaManualSwitch = false;

        //单个节点刷库任务限制条数
        int formulaManualLimitCount = 1;

        //mongo锁过期时间(分钟)
        int expireTime = 10;

        //任务执行的最大次数（异常）
        int maxExecuteCount = 50;

        int bulkActionCount = 5;

        int bulkActionLimitSize = 5;

        boolean supportGray = false;
    }

}
