package com.facishare.paas.appframework.config;

import com.facishare.paas.appframework.core.model.User;

import java.util.Set;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2024/5/8
 */
public interface ApplicationLayeredGrayService {
    Set<String> defineAppId(User user);

    Set<String> getDefineAppIdByDescribeApiNames(User user, String describeApiName);

    boolean supportAppLayered(User user, String appId);

    boolean supportAppLayered(User user, String appId, String describeApiName);

    void openApplicationLayer(User user, String appId, String describeApiName);

    void closeApplicationLayer(User user, String appId, String describeApiName);

    boolean canOpenApplicationLayer(User user, String appId, String describeApiName);
}
