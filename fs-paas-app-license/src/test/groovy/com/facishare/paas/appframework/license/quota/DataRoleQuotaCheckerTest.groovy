package com.facishare.paas.appframework.license.quota

import com.facishare.paas.appframework.license.quota.checker.DataRoleQuotaChecker
import com.facishare.paas.appframework.license.quota.checker.QuotaChecker
import com.facishare.paas.appframework.license.util.ModulePara
import com.google.common.collect.Maps
import spock.lang.Specification

class DataRoleQuotaCheckerTest extends Specification {

    def "test getMaxCount should return mapped value if available"() {
        given:
        def paraKey = "data_share_rules_limit"
        def paraMap = [(paraKey): 50]
        def dataRoleQuotaChecker = DataRoleQuotaChecker.builder()
                .paraMap(paraMap)
                .build()
        def quotaInfo = QuotaChecker.QuotaInfo.builder()
                .paraKey(paraKey)
                .build()

        when:
        def result = dataRoleQuotaChecker.getMaxCount(quotaInfo)

        then:
        result == 50
    }

    def "test getMaxCount should return 0 if not mapped"() {
        given:
        def paraMap = Maps.newHashMap()
        def dataRoleQuotaChecker = DataRoleQuotaChecker.builder()
                .paraMap(paraMap)
                .build()
        def quotaInfo = QuotaChecker.QuotaInfo.builder()
                .paraKey("data_share_rules_limit")
                .build()

        when:
        def result = dataRoleQuotaChecker.getMaxCount(quotaInfo)

        then:
        result == 0
    }

    def "test getMaxCount should handle different paraKeys correctly"() {
        given:
        def paraMap = [
                "data_share_rules_limit": 50,
                "conditional_sharing_limit": 25
        ]
        def dataRoleQuotaChecker = DataRoleQuotaChecker.builder()
                .paraMap(paraMap)
                .build()
        
        when:
        def result1 = dataRoleQuotaChecker.getMaxCount(QuotaChecker.QuotaInfo.builder()
                .paraKey("data_share_rules_limit")
                .build())
        def result2 = dataRoleQuotaChecker.getMaxCount(QuotaChecker.QuotaInfo.builder()
                .paraKey("conditional_sharing_limit")
                .build())
        
        then:
        result1 == 50
        result2 == 25
    }

    def "test check should use paraKey from quotaInfo for comparison"() {
        given:
        def paraMap = [
                "data_share_rules_limit": 50,
                "conditional_sharing_limit": 25
        ]
        def dataRoleQuotaChecker = DataRoleQuotaChecker.builder()
                .paraMap(paraMap)
                .build()
        def quotaInfo1 = QuotaChecker.QuotaInfo.builder()
                .paraKey("data_share_rules_limit")
                .actualCount(40)
                .build()
        def quotaInfo2 = QuotaChecker.QuotaInfo.builder()
                .paraKey("conditional_sharing_limit")
                .actualCount(30)
                .build()
                
        when:
        def result1 = dataRoleQuotaChecker.check(quotaInfo1)
        def result2 = dataRoleQuotaChecker.check(quotaInfo2)
        
        then:
        result1 == true  // 40 < 50
        result2 == false // 30 > 25
    }
} 