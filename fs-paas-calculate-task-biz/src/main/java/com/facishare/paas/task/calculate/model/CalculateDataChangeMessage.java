package com.facishare.paas.task.calculate.model;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.task.calculate.util.QueueTypes;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Objects;

/**
 * Created by l<PERSON><PERSON> on 2018/10/16
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CalculateDataChangeMessage {
    private String eventId;
    private String tenantId;
    private String op;
    private String describeApiName;
    private List<String> dataIdList;
    private List<CalculateNode> calculateNodes;
    private String originalMessageId;
    private Long originalBornTimestamp;

    private transient String messageId;
    private boolean batch;
    private boolean fromOpTool;
    private Boolean flag;

    /**
     * 原始的对象 ApiName
     */
    private transient String originalDescribeApiName;
    /**
     * 原始的数据 Id
     */
    private transient List<String> originalObjectIds;
    private transient Long bornTimestamp;
    private transient long consumeTimestamp = System.currentTimeMillis();
    private transient String queueIndex;

    public boolean fromBatchOrOpTool() {
        return batch || fromOpTool;
    }

    public String queueType() {
        if (isFromOpTool()) {
            return QueueTypes.OPTOOL;
        }
        if (isBatch()) {
            return QueueTypes.BATCH;
        }
        return QueueTypes.MANUAL;
    }

    public boolean isSameMessage(CalculateDataChangeMessage message) {
        if (!this.tenantId.equals(message.getTenantId())) {
            return false;
        }
        if (!Objects.equals(this.describeApiName, message.getDescribeApiName())) {
            return false;
        }
        if (!CollectionUtils.isEqual(CollectionUtils.nullToEmpty(this.dataIdList), CollectionUtils.nullToEmpty(message.getDataIdList()))) {
            return false;
        }
        if (!CollectionUtils.isEqual(CollectionUtils.nullToEmpty(this.calculateNodes), CollectionUtils.nullToEmpty(message.getCalculateNodes()))) {
            return false;
        }
        return true;
    }
}
