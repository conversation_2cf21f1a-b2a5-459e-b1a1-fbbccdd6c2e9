package com.facishare.open.operating.center.service.mq.listener;

import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import com.facishare.open.app.center.mq.item.AppAdminItem;
import com.facishare.open.app.center.mq.item.AppOrServiceCreateItem;
import com.facishare.open.app.center.mq.item.CustomAppAdminItem;
import com.facishare.open.common.result.exception.BizException;
import com.facishare.open.operating.center.manager.OpenAppOrServiceCreateManager;
import com.facishare.open.operating.center.manager.OpenCustomAppManager;
import com.facishare.open.operating.center.manager.OperatingOpenServiceManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * Description: MQ消息处理器
 * User: zhouq
 * Date: 2016/7/28
 */
@Service(value = "appAndServiceAdminsChangeListener")
public class AppAndServiceAdminsChangeListener implements MessageListenerConcurrently {
    //mq日志分开打印
    private Logger MQ_LOG = LoggerFactory.getLogger("OPERATING_MQ_LOG");
    
    @Resource
    private OperatingOpenServiceManager openServiceManager;

    @Resource
    private OpenCustomAppManager openCustomAppManager;

    @Resource
    private OpenAppOrServiceCreateManager openAppOrServiceCreateManager;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> list, ConsumeConcurrentlyContext consumeConcurrentlyContext) {

        if (list.size() > 1) {
            MQ_LOG.error("There are more than one message in the RocketMQ, size[{}]", list.size());
        }

        try {
            processMessage(list.get(0));
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        } catch (BizException e) {
            MQ_LOG.error("AppAndServiceAdminsChangeListener fail to process message because BizException, message={}", list.get(0), e);
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        } catch (Exception ex) {
            MQ_LOG.error("AppAndServiceAdminsChangeListener Process Message Error, message={}", list.get(0), ex);
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }
    }

    private void processMessage(MessageExt message) {
        MQ_LOG.info("AppAndServiceAdminsChangeListener receive message={}", message);

        String topic = message.getTopic();
        switch (topic) {
            default:
                break;
        }

        MQ_LOG.info("AppAndServiceAdminsChangeListener consumed message success message={}", message);
    }
}
