package com.facishare.open.operating.center.utils;

import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * Created by zhouq on 5/18/16.
 *
 * <AUTHOR>
 */
public class ConfigCenter {
    private static Logger LOGGER = LoggerFactory.getLogger(ConfigCenter.class);

    // "玩转企业服务号"前端已改名为"玩转纷享服务号" 2017/7/25
    //玩转企业服务号相关配置
    //玩转企业服务号的企业ID
    public static String PLAY_COMPANY_SERVICE_FSEA;
    //玩转服务号的用户ID
    public static Integer PLAY_COMPANY_SERVICE_USER_ID;
    //玩转企业服务号的AppID
    public static String PLAY_COMPANY_SERVICE_APP_ID;

    // 企业服务号
    //第一次添加为企业服务号管理员，发送消息的图文消息ID
    public static String IS_FIRST_ADD_SERVICE_ADMINS_MATERIA_ID;
    //第一次利用企业服务号群发消息，发送消息的图文消息ID
    public static String IS_FIRST_SEND_GROUPMSG_MATERIA_ID;
    //第一次开启开发者模式，发送消息的图文消息ID
    public static String IS_FIRST_OPEN_DEVELOPERMODE_MATERIA_ID;

    // 互联服务号
    //第一次添加为互联服务号管理员，发送消息的图文消息ID
    public static String IS_FIRST_ADD_LINK_SERVICE_ADMINS_MATERIA_ID;
    //第一次利用互联服务号群发消息，发送消息的图文消息ID
    public static String IS_FIRST_LINK_SERVICE_SEND_GROUPMSG_MATERIA_ID;
    //第一次管理员回复用户信息，发送消息的图文消息ID
    public static String IS_FIRST_LINK_SERVICE_REPLY_MSG_MATERIA_ID;

    //开启任务系统功能
    public static String IS_OPEN_GROUP_MSG_TASK_FUNCTION;

    //纷享应用管家相关配置
    //纷享应用管家的企业ID
    public static String FS_APP_BUTLER_FSEA;
    //纷享应用管家的用户ID
    public static Integer FS_APP_BUTLER_USER_ID;
    //纷享应用管家的AppID
    public static String FS_APP_BUTLER_APP_ID;


    //第一次试用付费应用
    public static String IS_FIRST_TRAIL_APP_MATERIA_ID;
    //第一次添加为自定义管理员，发送消息的图文消息ID
    public static String IS_FIRST_ADD_CUSTOM_APP_ADMINS_MATERIA_ID;
    //第一次创建服务号，发送消息的图文消息ID
    public static String IS_FIRST_CREATE_APP_SERVICE_MATERIA_ID;
    //第一次创建自定义应用，发送消息的图文消息ID
    public static String IS_FIRST_CREATE_APP_CUSTOM_MATERIA_ID;
    //第一次管理员回复用户信息，发送消息的图文消息ID
    public static String IS_FIRST_SERVICE_REPLY_MSG_MATERIA_ID;
    //首次试用到期或首次购买收费应用后已到期，发送消息的图文消息ID
    public static String IS_FIRST_APP_TRAIL_OR_BUY_EXPIRE_MATERIA_ID;

    //通知消息文案
    //服务号启用
    public static String SYSTEM_NOTICE_APP_SERVICE_ON;
    // 服务号停用
    public static String SYSTEM_NOTICE_APP_SERVICE_OFF;
    // 微联服务号删除
    public static String SYSTEM_NOTICE_OUT_SERVICE_DELETE;
    //互联服务号启用
    public static String SYSTEM_NOTICE_LINK_SERVICE_ON;
    // 互联服务号停用
    public static String SYSTEM_NOTICE_LINK_SERVICE_OFF;
    //自定义应用启用
    public static String SYSTEM_NOTICE_APP_CUSTOM_ON;
    //自定义应用停用
    public static String SYSTEM_NOTICE_APP_CUSTOM_OFF;
    //平台应用启用
    public static String SYSTEM_NOTICE_APP_PLATFORM_ON;
    //平台应用停用
    public static String SYSTEM_NOTICE_APP_PLATFORM_OFF;

    //多客服开启
    public static String SYSTEM_NOTICE_SERVICE_NUMBER_ON;
    //多客服停用
    public static String SYSTEM_NOTICE_SERVICE_NUMBER_OFF;
    //服务号创建
    public static String SYSTEM_NOTICE_APP_SERVICE_CREATE;
    //互联服务号创建
    public static String SYSTEM_NOTICE_LINK_SERVICE_CREATE;
    //外联服务号创建
    public static String SYSTEM_NOTICE_OUTER_SERVICE_CREATE;
    //自定义应用创建
    public static String SYSTEM_NOTICE_APP_CUSTOM_CREATE;

    //添加客服人员消息
    public static String NOTIFY_SERVICE_CUSTOMER_ADD;
    //删除客服人员消息
    public static String NOTIFY_SERVICE_CUSTOMER_REMOVE;
    //添加互联服务号客服人员消息
    public static String NOTIFY_LINK_SERVICE_CUSTOMER_ADD;
    //添加服务号管理员权限时系统消息
    public static String NOTIFY_SERVICE_ADMIN_RIGHT_ADD;
    //添加外联服务号管理员权限时系统消息
    public static String NOTIFY_OUTER_SERVICE_ADMIN_RIGHT_ADD;
    //添加互联服务号管理员权限时系统消息
    public static String NOTIFY_LINK_SERVICE_ADMIN_RIGHT_ADD;
    //删除服务号管理员权限时系统消息
    public static String NOTIFY_SERVICE_ADMIN_RIGHT_REMOVE;
    //添加应用管理员权限时系统消息
    public static String NOTIFY_APP_ADMIN_RIGHT_ADD;
    public static String WX_LINK_NOTICE_APP_ID;
    //通知助手特殊提示
    public static String TZ_APP_ADMIN_ADD;
    //删除应用管理员权限时系统消息
    public static String NOTIFY_APP_ADMIN_RIGHT_REMOVE;
    //通知助手特殊提示
    public static String TZ_APP_ADMIN_REMOVE;
    //付费应用个人试用系统消息
    public static String NOTIFY_APP_TRY_FORMATTER;
    //付费应用企业试用系统消息
    public static String NOTIFY_APP_ENTERPRISE_TRY_FORMATTER;
    //试用应用-个人试用到期
    public static String NOTIFY_APP_TRY_EXPIRE;
    //试用应用-企业试用到期
    public static String NOTIFY_EA_APP_TRY_EXPIRE;
    //购买应用
    public static String NOTIFY_APP_BUY;
    //购买应用-已到期，待续费
    public static String NOTIFY_APP_BUY_EXPIRE;
    //汇聚停用付费应用
    public static String NOTIFY_APP_BUY_ON_BY_HUIJU;
    //汇聚启用付费应用
    public static String NOTIFY_APP_BUY_OFF_BY_HUIJU;

    //付费应用试用推送图文消息
    public static Map<String /* appId */, String /* materialId */> APP_TRAIL_MAP;

    //基础应用添加为管理员推送图文消息
    public static Map<String /* appId */, String /* materialId */> BASE_APP_ADMIN_MAP;

    //服务号模板配置相应的运营指导图文消息素材
    public static Map<String /* templateId */, String /* materialId */> SERVICE_TEMPLATE_MATERIAL_MAP;
    //crmId
    public static String CRM_APP_ID ;
    //不需要发送消息的appids   eg: FSAID_989a78,FSAID_989a79
    public static List<String> NO_SEND_MSG_APP_IDS = Lists.newArrayList("FSAID_989a78");

    static {
        ConfigFactory.getInstance().getConfig("fs-open-app-center-common", config -> {
            PLAY_COMPANY_SERVICE_FSEA = config.get("PLAY_COMPANY_SERVICE_FSEA");
            PLAY_COMPANY_SERVICE_USER_ID = config.getInt("PLAY_COMPANY_SERVICE_USER_ID");
            PLAY_COMPANY_SERVICE_APP_ID = config.get("PLAY_COMPANY_SERVICE_APP_ID");

            // 企业服务号和应用
            IS_FIRST_ADD_SERVICE_ADMINS_MATERIA_ID = config.get("IS_FIRST_ADD_SERVICE_ADMINS_MATERIA_ID");
            IS_FIRST_SEND_GROUPMSG_MATERIA_ID = config.get("IS_FIRST_SEND_GROUPMSG_MATERIA_ID");
            IS_FIRST_CREATE_APP_SERVICE_MATERIA_ID = config.get("IS_FIRST_CREATE_APP_SERVICE_MATERIA_ID");
            IS_FIRST_CREATE_APP_CUSTOM_MATERIA_ID = config.get("IS_FIRST_CREATE_APP_CUSTOM_MATERIA_ID");

            // 互联服务号
            IS_FIRST_ADD_LINK_SERVICE_ADMINS_MATERIA_ID = config.get("IS_FIRST_ADD_LINK_SERVICE_ADMINS_MATERIA_ID");
            IS_FIRST_LINK_SERVICE_SEND_GROUPMSG_MATERIA_ID = config.get("IS_FIRST_LINK_SERVICE_SEND_GROUPMSG_MATERIA_ID");
            IS_FIRST_LINK_SERVICE_REPLY_MSG_MATERIA_ID = config.get("IS_FIRST_LINK_SERVICE_REPLY_MSG_MATERIA_ID");

            FS_APP_BUTLER_FSEA = config.get("FS_APP_BUTLER_FSEA");
            FS_APP_BUTLER_USER_ID = config.getInt("FS_APP_BUTLER_USER_ID");
            FS_APP_BUTLER_APP_ID = config.get("FS_APP_BUTLER_APP_ID");
            IS_FIRST_OPEN_DEVELOPERMODE_MATERIA_ID = config.get("IS_FIRST_OPEN_DEVELOPERMODE_MATERIA_ID");
            IS_FIRST_TRAIL_APP_MATERIA_ID = config.get("IS_FIRST_TRAIL_APP_MATERIA_ID");
            IS_FIRST_ADD_CUSTOM_APP_ADMINS_MATERIA_ID = config.get("IS_FIRST_ADD_CUSTOM_APP_ADMINS_MATERIA_ID");
            IS_FIRST_SERVICE_REPLY_MSG_MATERIA_ID = config.get("IS_FIRST_SERVICE_REPLY_MSG_MATERIA_ID");
            IS_FIRST_APP_TRAIL_OR_BUY_EXPIRE_MATERIA_ID = config.get("IS_FIRST_APP_TRAIL_OR_BUY_EXPIRE_MATERIA_ID");

            APP_TRAIL_MAP = stringToMap(config.get("APP_TRAIL_MAP"));
            BASE_APP_ADMIN_MAP = stringToMap(config.get("BASE_APP_ADMIN_MAP"));
            SERVICE_TEMPLATE_MATERIAL_MAP = stringToMap(config.get("SERVICE_TEMPLATE_MATERIAL_MAP"));

            SYSTEM_NOTICE_SERVICE_NUMBER_ON = config.get("SYSTEM_NOTICE_SERVICE_NUMBER_ON", "您好！管理员已启用多客服功能，请继续您的移动客服处理！"); // ignoreI18n
            SYSTEM_NOTICE_SERVICE_NUMBER_OFF = config.get("SYSTEM_NOTICE_SERVICE_NUMBER_OFF", "您好！管理员已停用多客服功能，请知悉！"); // ignoreI18n
            SYSTEM_NOTICE_APP_SERVICE_CREATE = config.get("SYSTEM_NOTICE_APP_SERVICE_CREATE", "您成功创建%s企业服务号！"); // ignoreI18n
            SYSTEM_NOTICE_LINK_SERVICE_CREATE = config.get("SYSTEM_NOTICE_LINK_SERVICE_CREATE", "您已成功创建%s互联服务号！"); // ignoreI18n
            SYSTEM_NOTICE_OUTER_SERVICE_CREATE = config.get("SYSTEM_NOTICE_OUTER_SERVICE_CREATE", "您成功创建%s外联服务号！"); // ignoreI18n
            SYSTEM_NOTICE_APP_CUSTOM_CREATE = config.get("SYSTEM_NOTICE_APP_CUSTOM_CREATE", "您已成功创建%s应用！"); // ignoreI18n

            NOTIFY_SERVICE_ADMIN_RIGHT_ADD = config.get("NOTIFY_SERVICE_ADMIN_RIGHT_ADD", "您成为%s的管理员，您可群发文本、图文消息，收集员工反馈，提供自助服务等，请进入Web端【应用】-【服务号工作台】中进行设置，即刻开启您的服务号探索之旅吧！"); // ignoreI18n
            NOTIFY_OUTER_SERVICE_ADMIN_RIGHT_ADD = config.get("NOTIFY_OUTER_SERVICE_ADMIN_RIGHT_ADD", "您成为%s的管理员，您可群发通知、配置服务号菜单、管理客服等，请进入Web端【应用】-【服务号工作台】中进行设置，即刻开启您的服务号探索之旅吧！"); // ignoreI18n
            NOTIFY_LINK_SERVICE_ADMIN_RIGHT_ADD = config.get("NOTIFY_LINK_SERVICE_ADMIN_RIGHT_ADD", "您已成为互联服务号-%s的管理员，您可回复合作伙伴的咨询、给合作伙伴群发文本、图文消息等，请进入Web端【应用】-【服务号工作台】中进行设置，即刻开启您的服务号探索之旅吧！"); // ignoreI18n
            NOTIFY_SERVICE_ADMIN_RIGHT_REMOVE = config.get("NOTIFY_SERVICE_ADMIN_RIGHT_REMOVE", "您好！您的管理权限被取消，请知悉！"); // ignoreI18n
            NOTIFY_APP_ADMIN_RIGHT_ADD = config.get("NOTIFY_APP_ADMIN_RIGHT_ADD", "您成为%s的管理员，请进入网页端【应用】-【应用管理】中进行设置，即刻开启您的自建应用探索之旅吧！"); // ignoreI18n
            TZ_APP_ADMIN_ADD = config.get("TZ_APP_ADMIN_ADD", "您成为企业互联-微信 %s的管理员，可进入网页端或移动端【应用】-【企业互联-微信】-【通知助手】中进行通知业务的管理。"); // ignoreI18n
            NOTIFY_APP_ADMIN_RIGHT_REMOVE = config.get("NOTIFY_APP_ADMIN_RIGHT_REMOVE", "您好！您的%s管理权限被取消，请知悉！"); // ignoreI18n
            TZ_APP_ADMIN_REMOVE = config.get("TZ_APP_ADMIN_REMOVE", "您好！您的企业互联-微信 %s管理权限被取消，请知悉！"); // ignoreI18n
            NOTIFY_APP_TRY_FORMATTER = config.get("NOTIFY_APP_TRY_FORMATTER", "您开通{appName}免费试用，有效期到{expireTime}，快去体验高效便捷的应用吧！"); // ignoreI18n
            NOTIFY_APP_ENTERPRISE_TRY_FORMATTER = config.get("NOTIFY_APP_ENTERPRISE_TRY_FORMATTER", "贵公司开通{appName}免费试用，有效期到{expireTime}，快去体验高效便捷的应用吧！"); // ignoreI18n
            NOTIFY_APP_BUY_EXPIRE = config.get("NOTIFY_APP_BUY_EXPIRE", "您好！您的%s应用已到期，如需继续使用，请联系贵公司系统管理员或企业钱包管理员续费！"); // ignoreI18n
            NOTIFY_APP_TRY_EXPIRE = config.get("NOTIFY_APP_TRY_EXPIRE", "您好！您的%s试用已经到期，如需继续使用，请联系贵公司系统管理员或企业钱包管理员购买！"); // ignoreI18n
            NOTIFY_EA_APP_TRY_EXPIRE = config.get("NOTIFY_EA_APP_TRY_EXPIRE", "您好！贵公司的%s试用已经到期，如需继续使用，请联系贵公司系统管理员或企业钱包管理员购买！"); // ignoreI18n
            NOTIFY_APP_BUY = config.get("NOTIFY_APP_BUY", "贵公司成功购买%s，请登录网页端[应用]-[应用管理]中设置员工的可见范围，开启业务操作！\n" +
                    "购买用户：%s\n" +
                    "有 效 期：%s"); // ignoreI18n
            NOTIFY_APP_BUY_ON_BY_HUIJU = config.get("NOTIFY_APP_BUY_ON_BY_HUIJU", "您好！贵公司的%s应用已被启用，请继续进行您的业务操作。给您带来不便，请谅解！"); // ignoreI18n
            NOTIFY_APP_BUY_OFF_BY_HUIJU = config.get("NOTIFY_APP_BUY_OFF_BY_HUIJU", "您好！贵公司的%s应用已被停用，如有疑问请联系贵公司系统管理员或企业钱包管理员。给您带来不便，请谅解！"); // ignoreI18n

            NOTIFY_SERVICE_CUSTOMER_ADD = config.get("NOTIFY_SERVICE_CUSTOMER_ADD", "您成为%s的客服人员，您可以在APP服务号工作台中回复员工的反馈了，快去感受移动客服的便利吧"); // ignoreI18n
            NOTIFY_SERVICE_CUSTOMER_REMOVE = config.get("NOTIFY_SERVICE_CUSTOMER_REMOVE", "您好！您的客服权限被取消，请知悉！"); // ignoreI18n
            NOTIFY_LINK_SERVICE_CUSTOMER_ADD = config.get("NOTIFY_LINK_SERVICE_CUSTOMER_ADD", "管理员已将您设置为互联服务号-%s的客服人员，您可以在APP服务号工作台中回复合作伙伴对接人的咨询了，快去感受企业间移动客服的便利吧！"); // ignoreI18n
            SYSTEM_NOTICE_APP_SERVICE_ON = config.get("SYSTEM_NOTICE_APP_SERVICE_ON", "您好！贵公司的%s被启用了，请继续您的服务号探索之旅吧！"); // ignoreI18n
            SYSTEM_NOTICE_APP_SERVICE_OFF = config.get("SYSTEM_NOTICE_APP_SERVICE_OFF", "您好！贵公司的%s被%s停用了，请知悉！"); // ignoreI18n
            SYSTEM_NOTICE_LINK_SERVICE_ON = config.get("SYSTEM_NOTICE_LINK_SERVICE_ON", "您好！贵公司的%s被启用了，请继续您的服务号探索之旅吧！"); // ignoreI18n
            SYSTEM_NOTICE_LINK_SERVICE_OFF = config.get("SYSTEM_NOTICE_LINK_SERVICE_OFF", "您好！贵公司的互联服务号-%s被%s停用了，请知悉！"); // ignoreI18n

            SYSTEM_NOTICE_APP_CUSTOM_ON = config.get("SYSTEM_NOTICE_APP_CUSTOM_ON", "您好！贵公司的%s被启用了，请继续您的自建应用探索之旅吧！"); // ignoreI18n
            SYSTEM_NOTICE_APP_CUSTOM_OFF = config.get("SYSTEM_NOTICE_APP_CUSTOM_OFF", "您好！贵公司的%s被%s停用了，请知悉！"); // ignoreI18n
            SYSTEM_NOTICE_APP_PLATFORM_ON = config.get("SYSTEM_NOTICE_APP_PLATFORM_ON", "您好！贵公司的%s应用被%s启用了，请知悉！"); // ignoreI18n
            SYSTEM_NOTICE_APP_PLATFORM_OFF = config.get("SYSTEM_NOTICE_APP_PLATFORM_OFF", "您好！贵公司的%s应用被%s停用了，请知悉！"); // ignoreI18n

            SYSTEM_NOTICE_OUT_SERVICE_DELETE = config.get("SYSTEM_NOTICE_OUT_SERVICE_DELETE", "【%s】删除了服务号【%s】"); // ignoreI18n

            IS_OPEN_GROUP_MSG_TASK_FUNCTION = config.get("IS_OPEN_GROUP_MSG_TASK_FUNCTION");
            NO_SEND_MSG_APP_IDS = Lists.newArrayList(config.get("NO_SEND_MSG_APP_IDS", "NO_SEND_MSG_APP_IDS").split(","));
        });

        ConfigFactory.getInstance().getConfig("fs-open-app-center-common", config -> {
            CRM_APP_ID = config.get("CRM_APP_ID", "FSAID_5f5e519");
            if (CRM_APP_ID == null) {
                LOGGER.error("crm app id is null, please set CRM_APP_ID in " + "fs-open-app-center-common");
            }
            WX_LINK_NOTICE_APP_ID = config.get("WX_LINK_NOTICE_APP_ID", "FSAID_989776");
        });
    }


    /**
     * 配置中心，以strs=aakey:aavalue,bbkey:bbvalue数据转换为map(key,value)
     *
     * @param strs str
     * @return map
     */
    private static Map<String, String> stringToMap(String strs) {

        if (StringUtils.isBlank(strs)) {
            return Collections.emptyMap();
        }
        String[] strArray = StringUtils.split(strs, ",");

        Map<String, String> map = Maps.newHashMap();
        for (String str : strArray) {
            String[] entry = StringUtils.split(str, ":");
            if (entry.length < 2) {
                continue;
            } else {
                map.put(entry[0], entry[1]);
            }
        }
        return map;
    }
}
