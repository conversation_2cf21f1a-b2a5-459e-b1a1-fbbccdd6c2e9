package com.facishare.open.operating.center.utils;

import com.facishare.open.operating.center.api.model.OperationLogVO;
import com.fxiaoke.i18n.client.I18nClient;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2018/12/20
 */
public class I18NUtils {
    private static final String FORMAT_SYSTEM_NOTICE_STRING_KEY = "appcenter.operating.notice.%s";
    private static final String FORMAT_OPERATION_LOG_STRING_KEY = "appcenter.material.operationLog.%s";
    /**
     * 根据通知名称和语言环境换取多语言通知模板
     * @param noticeName  ConfigCenter对应的keyName
     * @param lang
     * @return
     */
    public static String getNoticeTemplateByLang(int ei, String lang, String noticeName, String defaultMsg) {
        if (StringUtils.isBlank(lang)) {
            return defaultMsg;
        }
        String key = String.format(FORMAT_SYSTEM_NOTICE_STRING_KEY, noticeName);
        String result = I18nClient.getInstance().get(key, ei, lang);
        if (StringUtils.isBlank(result)) {
            return defaultMsg;
        }
        return result;
    }

    /**
     * 根据语言环境 修改操作日志内容
     * @param ei
     * @param lang
     * @param operationLogVOs
     * @return
     */
    public static List<OperationLogVO> modifyOperationLogByLang(int ei, String lang, List<OperationLogVO> operationLogVOs) {
        if (StringUtils.isBlank(lang)) {
            return operationLogVOs;
        }
        Set<String> keys = Sets.newHashSet();
        //Map<日志内容，双引号内容>
        Map<String, String> contentMatchValueMap = Maps.newHashMap();
        //Map<日志内容，多语言key>
        Map<String, String> contentKeyMap = Maps.newConcurrentMap();

        Pattern pattern = Pattern.compile("\"([^\"]*)\"");

        //将日志内容转为多语言key
        //含双引号的需将引号内容替换成%s
        operationLogVOs.forEach(operationLogVO -> {
            String content = operationLogVO.getOperateContent();
            //取出双引号中间内容
            Matcher matcher = pattern.matcher(content);
            if (matcher.find()) {
                if (!contentMatchValueMap.containsKey(content)) {
                    contentMatchValueMap.put(content, matcher.group());
                    String newKey = String.format(FORMAT_OPERATION_LOG_STRING_KEY, content.replace(matcher.group(), "%s"));
                    keys.add(newKey);
                    contentKeyMap.putIfAbsent(content, newKey);
                }
            } else {
                String newKey = String.format(FORMAT_OPERATION_LOG_STRING_KEY, content);
                keys.add(newKey);
                contentKeyMap.putIfAbsent(content, newKey);
            }


        });


        Map<String, String> resultMap = I18nClient.getInstance().get(new ArrayList<String>(keys), ei, lang);

        operationLogVOs.forEach(operationLogVO -> {
            String key = contentKeyMap.get(operationLogVO.getOperateContent());
            String result = resultMap.get(key);
            if (StringUtils.isNotBlank(result)) {
                //没有双引号
                if (contentMatchValueMap.get(operationLogVO.getOperateContent()) == null) {
                    operationLogVO.setOperateContent(result);
                } else {
                    operationLogVO.setOperateContent(String.format(result, contentMatchValueMap.get(operationLogVO.getOperateContent())));
                }
            }
        });
        return operationLogVOs;
    }
}
