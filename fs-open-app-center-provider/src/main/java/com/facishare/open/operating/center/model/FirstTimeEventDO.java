package com.facishare.open.operating.center.model;

import org.mongodb.morphia.annotations.*;

/**
 * Created by <PERSON>iq<PERSON><PERSON> on 2016/8/1.
 */
@Entity(value = "FirstTimeEvent", noClassnameStored = true)
public class FirstTimeEventDO {
    @Id
    private String id;
    private String eventFlag;
    private String eventExecutor;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getEventFlag() {
        return eventFlag;
    }

    public void setEventFlag(String eventFlag) {
        this.eventFlag = eventFlag;
    }

    public String getEventExecutor() {
        return eventExecutor;
    }

    public void setEventExecutor(String eventExecutor) {
        this.eventExecutor = eventExecutor;
    }

    @Override
    public String toString() {
        return "FirstTimeEventDO{" +
                "id='" + id + '\'' +
                ", eventFlag='" + eventFlag + '\'' +
                ", eventExecutor='" + eventExecutor + '\'' +
                '}';
    }
}
