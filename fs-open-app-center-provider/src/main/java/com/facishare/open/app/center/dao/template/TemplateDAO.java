package com.facishare.open.app.center.dao.template;

import com.facishare.open.app.center.api.model.template.TemplateDO;
import com.facishare.open.common.storage.mysql.dao.Pager;

import java.util.List;
import java.util.Map;

/**
 * 模板
 *
 * <AUTHOR>
 * @date 2015年8月28日
 */
public interface TemplateDAO {

    /**
     * 保存模板记录
     *
     * @param templateDO
     */
    void addTemplate(TemplateDO templateDO);

    /**
     * 修改模板记录
     *
     * @param templateDO
     */
    void updateTemplate(TemplateDO templateDO);

    /**
     * 获取模板记录列表
     *
     * @param map
     * @return
     */
    List<TemplateDO> queryTemplateByList(Map<String, Object> map);

    /**
     * 删除模板记录
     *
     * @param id 模板记录的ID
     */
    int deleteTemplate(long id);

    /**
     * 获取模板对象
     *
     * @param templateId 模板id
     * @param status     模板状态
     * @return
     */
    TemplateDO getTemplateByParams(String templateId, int status);

    /**
     * 获取模板对象
     *
     * @param id
     * @return
     */
    TemplateDO getTemplateById(long id);

    /**
     * 分页
     *
     * @param pager
     * @return
     */
    Pager<TemplateDO> queryTemplateByPager(Pager<TemplateDO> pager);
}
