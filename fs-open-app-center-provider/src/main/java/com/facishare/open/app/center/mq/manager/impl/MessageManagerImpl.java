package com.facishare.open.app.center.mq.manager.impl;

import com.facishare.open.app.center.mq.manager.MessageManager;
import com.facishare.open.app.center.mq.utils.ConfigCenter;
import com.facishare.open.common.result.BaseResult;
import com.facishare.open.common.result.exception.BizException;
import com.facishare.open.material.api.enums.CreatorTypeEnum;
import com.facishare.open.material.api.enums.MaterialTypeEnum;
import com.facishare.open.material.api.enums.MessageSendTypeEnum;
import com.facishare.open.material.api.model.vo.AccepterVO;
import com.facishare.open.material.api.model.vo.MessageVO;
import com.facishare.open.material.api.service.MaterialMessageService;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by xialf on 4/12/16.
 *
 * <AUTHOR>
 */
@Service
public class MessageManagerImpl implements MessageManager {
    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private MaterialMessageService materialMessageService;

    @Override
    public String sendTextMsg(String fsEa, String content, List<Integer> targetUserIds) {
        if (targetUserIds.isEmpty()) {
            return null;
        }
        MessageVO messageVO = new MessageVO();
        messageVO.setAppId(ConfigCenter.getOfficialAppId());//目前填写的是各个环境上的纷享应用管家APPID
        messageVO.setContent(content);
        messageVO.setMaterialType(MaterialTypeEnum.TEXT);

        AccepterVO accepterVO = new AccepterVO();
        accepterVO.setEa(fsEa);
        accepterVO.setEmployees(targetUserIds);
        accepterVO.setIsAllEmployees(false);
        accepterVO.setDepartments(new ArrayList<>());//空List
        messageVO.setAccepterVO(accepterVO);

        messageVO.setCreatorTypeEnum(CreatorTypeEnum.DEV);//开发者
        messageVO.setSender(null);//当CreatorTypeEnum.APP_ADMIN时 不能为null
        MessageSendTypeEnum messageSendType = MessageSendTypeEnum.SYSTEM_MSG;//目前系统消息无法在web查看到 被筛选了
        BaseResult<String> sendMaterialMsgResult = materialMessageService.sendMessage(messageVO, messageSendType);
        if (!sendMaterialMsgResult.isSuccess()) {
            logger.warn("failed to call materialMessageService.sendMessage,messageVO={}, messageSendType={}，result={}",
                    messageVO, messageSendType, sendMaterialMsgResult);
            throw new BizException(sendMaterialMsgResult);
        }
        logger.info("success to call materialMessageService.sendMessage,messageVO={},messageSendType={},messageId={}",
                messageVO, messageSendType, sendMaterialMsgResult.getResult());
        return sendMaterialMsgResult.getResult();
    }

    /**
     * 发送普通文本消息
     *
     * @param fsEa          企业号
     * @param content       发送内容
     * @param targetUserIds 接收者
     * @param appId         发送者的appId
     * @return
     */
    @Override
    public void sendTextMsgByAppId(String fsEa, String content, List<Integer> targetUserIds, String appId) {
        logger.info("sendTextMsgByAppId.sendMessage,fsEa={},content={},targetUserIds={},appId={}",
                fsEa, content, targetUserIds, appId);
        if (StringUtils.isBlank(fsEa) || StringUtils.isBlank(appId) || CollectionUtils.isEmpty(targetUserIds)) {
            return ;
        }
        MessageVO messageVO = new MessageVO();
        messageVO.setAppId(appId);//纷享应用管家 或者 玩转企业服务号
        messageVO.setContent(content);
        messageVO.setMaterialType(MaterialTypeEnum.TEXT);

        AccepterVO accepterVO = new AccepterVO();
        accepterVO.setEa(fsEa);
        accepterVO.setEmployees(targetUserIds);
        accepterVO.setIsAllEmployees(false);
        accepterVO.setDepartments(new ArrayList<>());
        messageVO.setAccepterVO(accepterVO);

        messageVO.setCreatorTypeEnum(CreatorTypeEnum.DEV);
        messageVO.setSender(null);//当CreatorTypeEnum.APP_ADMIN时 不能为null
        MessageSendTypeEnum messageSendType = MessageSendTypeEnum.SYSTEM_MSG;
        BaseResult<String> sendMaterialMsgResult;
        try {
            logger.info("start materialMessageService.sendMessage , messageVO[{}], accepterVO[{}], messageSendType[{}]",
                    messageVO, ToStringBuilder.reflectionToString(messageVO.getAccepterVO()), messageSendType);
            sendMaterialMsgResult = materialMessageService.sendMessage(messageVO, messageSendType);
            if (!sendMaterialMsgResult.isSuccess()) {
                logger.warn("failed to call materialMessageService.sendMessage,messageVO={}, messageSendType={}，result={}",
                        messageVO, messageSendType, sendMaterialMsgResult);
                throw new BizException(sendMaterialMsgResult);
            }
        } catch (BizException e) {
            logger.error("materialMessageService.sendMessage failed, messageVO[{}], accepterVO[{}], messageSendType[{}]",
                    messageVO, ToStringBuilder.reflectionToString(messageVO.getAccepterVO()), messageSendType);
            return ;
        }
        logger.info("success to call materialMessageService.sendMessage,messageVO={},messageSendType={},messageId={}",
                messageVO, messageSendType, sendMaterialMsgResult.getResult());
    }
}
