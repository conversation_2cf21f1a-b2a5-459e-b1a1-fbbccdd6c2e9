package com.facishare.open.app.center.dao.impl;

import com.facishare.open.app.center.api.model.OpenDemoAppDO;
import com.facishare.open.app.center.base.CommonDAO;
import com.facishare.open.app.center.dao.OpenDemoAppDAO;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.Map;

/**
 * impl.
 * Created by zenglb on 2016/3/17.
 */
@Repository
public class OpenDemoAppDAOImpl extends CommonDAO<OpenDemoAppDO> implements OpenDemoAppDAO {
    public static final String NAME_SPACE = "openDemoApp";

    @Override
    public int saveOpenDemoAppDO(OpenDemoAppDO entity) {
        return save(NAME_SPACE + ".saveOpenDemoAppDO", entity);
    }

    @Override
    public int updateOpenDemoAppDO(OpenDemoAppDO entity) {
        return update(NAME_SPACE + ".updateOpenDemoAppDO", entity);
    }

    @Override
    public OpenDemoAppDO queryByFsEa(String fsEa) {
        Map<String, Object> params = new HashMap<>();
        params.put("fsEa", fsEa);
        return getUnique(NAME_SPACE + ".queryByFsEa", params);
    }
}
