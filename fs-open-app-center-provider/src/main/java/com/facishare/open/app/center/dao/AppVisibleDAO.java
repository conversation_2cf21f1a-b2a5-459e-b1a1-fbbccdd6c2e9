package com.facishare.open.app.center.dao;

import com.facishare.open.app.center.api.model.AppVisibleDO;

import java.util.List;

/**
 * 更新appVisibale
 *
 * <AUTHOR>
 * @date 2015年8月31日
 */
public interface AppVisibleDAO {

    /**
     * 保存
     *
     * @param appVisible
     */
    int save(AppVisibleDO appVisible);

    /**
     * 删除
     *
     * @param appId
     */
    int deleteByAppId(String appId);

    /**
     * 根据appid列表批量查询
     *
     * @param appIds
     * @return
     */
    List<AppVisibleDO> queryByAppIds(List<String> appIds);

    AppVisibleDO queryByAppId(String appId);
}
