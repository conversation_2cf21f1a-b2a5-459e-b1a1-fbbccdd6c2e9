package com.facishare.open.app.common.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.TimeUnit;

public class StopWatch {
    private static Logger LOG = LoggerFactory.getLogger(StopWatch.class);
    private long startTime;
    private long lapStartTime;
    private String tagName;
    private List<String> steps = new ArrayList();

    public static StopWatch create(String tagName) {
        return new StopWatch(tagName);
    }

    private StopWatch(String tagName) {
        this.tagName = tagName;
        long start = System.nanoTime();
        this.startTime = start;
        this.lapStartTime = start;
    }

    public void lap(String stepName) {
        long elapsedTime = TimeUnit.NANOSECONDS.toMillis(System.nanoTime() - this.lapStartTime);
        int index = this.steps.size() + 1;
        String step = String.format("T%d(%s)/%d", index, stepName, elapsedTime);
        this.steps.add(step);
        this.lapStartTime = System.nanoTime();
    }

    public void log() {
        StringBuilder stringBuilder = this.createLog();
        LOG.warn(stringBuilder.toString());
    }

    public void logSlow(long slow) {
        long totalElapsedTime = TimeUnit.NANOSECONDS.toMillis(System.nanoTime() - this.startTime);
        if (totalElapsedTime > slow) {
            StringBuilder stringBuilder = this.createLog();
            LOG.warn(stringBuilder.toString());
        }

    }

    private StringBuilder createLog() {
        long totalElapsedTime = TimeUnit.NANOSECONDS.toMillis(System.nanoTime() - this.startTime);
        StringBuilder stringBuilder = new StringBuilder(this.tagName);
        stringBuilder.append(' ');
        stringBuilder.append(" Total/");
        stringBuilder.append(totalElapsedTime);
        stringBuilder.append(' ');
        Iterator var4 = this.steps.iterator();

        while(var4.hasNext()) {
            String step = (String)var4.next();
            stringBuilder.append(step);
            stringBuilder.append(' ');
        }

        return stringBuilder;
    }
}
