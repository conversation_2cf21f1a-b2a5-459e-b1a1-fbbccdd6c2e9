package com.facishare.open.app.pay.cache;

import java.util.Date;

/**
 * Created by xialf on 4/20/16.
 *
 * <AUTHOR>
 */
public class CacheUtils {
    /**
     * 缓存最长时间(24小时).
     */
    private static final int CACHE_TTL = 24 * 60 * 60;

    /**
     * 服务器时间差.
     */
    private static final int TIME_OFFSET = 5;

    private CacheUtils() {
    }

    /**
     * 根据结束时间计算缓存有效期.
     * 失效时间小于endTime，且小于{@value CACHE_TTL}
     * @param endTime   配额失效时间
     * @return 缓存有效期
     */
    public static int computeTtl(final Date endTime) {
        final Date now = new Date();
        if (endTime == null
                || endTime.getTime() <= now.getTime()) {
            return CACHE_TTL;
        } else {
            long offsetTime = Math.min((endTime.getTime() - now.getTime()) / 1000 + TIME_OFFSET, Integer.MAX_VALUE);
            return Math.min(CACHE_TTL, (int) offsetTime);
        }
    }
}
