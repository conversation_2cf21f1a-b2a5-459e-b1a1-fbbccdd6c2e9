package com.facishare.open.app.center.manager.impl;

import com.alibaba.fastjson.JSONObject;
import com.facishare.open.app.center.manager.PollingService;
import com.facishare.open.app.center.utils.BizCommonUtils;
import com.facishare.open.app.center.utils.GrayReleaseBiz;
import com.facishare.polling.api.arg.UpdatePollingDataArg;
import com.facishare.polling.api.enums.PollingNotifyFlag;
import com.facishare.polling.api.enums.PollingOSType;
import com.facishare.polling.api.util.PollingMessageProducer;
import com.facishare.polling.api.util.RangeBuilder;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created by xialf on 2017/04/18.
 *
 * <AUTHOR>
 * @since 2017/04/18 4:41 PM
 */
@Component
public class PollingServiceImpl implements PollingService {
    private static final Logger log = LoggerFactory.getLogger("NewWebPoll");

    /**
     * CRM轮询改造使用到的key.
     */
    private static final String CRM_AVAIL_POLL_KEY = "crm_availability";

    /**
     * 移动端通知重新拉取应用列表的pollingKey
     */
    private static final String AGAIN_QUERY_APP_LIST = "againQueryAppList";
    private static final String AGAIN_QUERY_SERVICE_NUM_LIST = "againQueryServiceNumList";

    @Resource
    private PollingMessageProducer pollingMessageProducer;

    @Override
    public void notifyComponentViewChanged(String componentId, String fsEa, List<Integer> users) {
        if (!componentId.equals(BizCommonUtils.getCrmComponentId()) || users.isEmpty()) { //现在只需要处理CRM的轮询
            return;
        }
        UpdatePollingDataArg arg = new UpdatePollingDataArg();
        arg.setKey(CRM_AVAIL_POLL_KEY);
        arg.setVersion(System.currentTimeMillis());
        arg.setOsType(PollingOSType.WEB);
        arg.setRange(RangeBuilder.buildMultiEmployeesRange(fsEa, users));
        arg.setRealTime(true);

        try {
            // 通过rocketMq发送消息
            pollingMessageProducer.sendMessage(PollingNotifyFlag.UPDATE_MULTI_EMPLOYEE, arg);
            log.info("pollingMessageProducer.sendMessage success: componentId[{}], fsEa[{}], users[{}], arg[{}]", componentId, fsEa, users, arg);
        } catch (Exception e) {
            log.warn("pollingMessageProducer.sendMessage failed: componentId[{}], fsEa[{}], users[{}], arg[{}]", componentId, fsEa, users, arg);
        }
    }

    @Override
    public void senPoling(String fsEa, List<Integer> users, String pollingKey, boolean realTime, PollingOSType osType) {
        UpdatePollingDataArg arg = new UpdatePollingDataArg();
        arg.setKey(pollingKey);
        arg.setVersion(System.currentTimeMillis());
        arg.setOsType(osType);
        arg.setRange(RangeBuilder.buildMultiEmployeesRange(fsEa, users));
        arg.setRealTime(realTime);
        try {
            // 通过rocketMq发送消息
            pollingMessageProducer.sendMessage(PollingNotifyFlag.UPDATE_MULTI_EMPLOYEE, arg);
            log.info("senPoling success: param = {}", JSONObject.toJSONString(arg));
        } catch (Exception e) {
            log.info("senPoling faild: param = {}", JSONObject.toJSONString(arg));
        }
    }

    @Override
    public void senPoling(String fsEa, List<Integer> users, Integer msgSessionKey, boolean realTime, PollingOSType osType) {
        String pollingKey = "";
        switch (msgSessionKey) {
            case 100:
                pollingKey = AGAIN_QUERY_APP_LIST;
                break;
            case 20:
                pollingKey = AGAIN_QUERY_SERVICE_NUM_LIST;
                break;
            default:
                break;
        }
        if (StringUtils.isNotBlank(pollingKey)) {
            senPoling(fsEa, users, pollingKey, realTime, osType);
        }
    }
}
