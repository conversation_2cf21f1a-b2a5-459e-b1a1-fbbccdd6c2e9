//package com.facishare.open.app.pay.converter;
//
//import com.facishare.open.app.pay.api.model.EmployeeTrialVo;
//import com.facishare.open.app.pay.entity.EmployeeTrial;
//import org.springframework.cglib.beans.BeanCopier;
//
///**
// * Created by xialf on 2017/04/17.
// *
// * <AUTHOR>
// * @since 2017/04/17 5:33 PM
// */
//public final class EmployeeTrialConverter {
//    private static final BeanCopier DO_2_VO = BeanCopier.create(EmployeeTrial.class, EmployeeTrialVo.class, false);
//    private EmployeeTrialConverter() {
//    }
//
//    public static EmployeeTrialVo do2vo(final EmployeeTrial entity) {
//        final EmployeeTrialVo vo = new EmployeeTrialVo();
//        DO_2_VO.copy(entity, vo, null);
//        vo.setTrialBeginDate(entity.getGmtBegin());
//        vo.setTrialExpirationDate(entity.getGmtEnd());
//        return vo;
//    }
//}
