package com.facishare.open.operating.center.manager.impl;

import com.facishare.open.operating.center.manager.FirstTimeEventManager;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.UUID;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2016/8/5.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring/unit-test.xml")
public class FirstTimeEventManagerTest {
    @Resource
    FirstTimeEventManager firstTimeEventManager;

    @Test
    public void isFirstTime_Success() {
        String eventFlag = "UNIT-TEST-EVENT";
        String eventExecutor = "UNIT-TEXT-EVENT-EXECUTOR-" + UUID.randomUUID().toString();

        boolean firstResult = firstTimeEventManager.isFirstTime(eventFlag, eventExecutor);
        Assert.assertTrue(firstResult);

        boolean secondResult = firstTimeEventManager.isFirstTime(eventFlag, eventExecutor);
        Assert.assertFalse(secondResult);
    }

    @Test
    public void isFirstTimeWithOnlyQueryParam_Success() {
        String eventFlag = "UNIT-TEST-EVENT";
        String eventExecutor = "UNIT-TEXT-EVENT-EXECUTOR-" + UUID.randomUUID().toString();
        boolean onlyQuery = true;
        boolean firstResult = firstTimeEventManager.isFirstTime(eventFlag, eventExecutor, onlyQuery);
        Assert.assertTrue(firstResult);
        firstResult = firstTimeEventManager.isFirstTime(eventFlag, eventExecutor, onlyQuery);
        Assert.assertTrue(firstResult);

        onlyQuery = false;
        firstResult = firstTimeEventManager.isFirstTime(eventFlag, eventExecutor, onlyQuery);
        Assert.assertTrue(firstResult);

        firstResult = firstTimeEventManager.isFirstTime(eventFlag, eventExecutor, onlyQuery);
        Assert.assertFalse(firstResult);
    }
}
