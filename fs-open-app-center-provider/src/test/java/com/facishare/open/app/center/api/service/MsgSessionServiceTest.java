package com.facishare.open.app.center.api.service;

import com.facishare.open.msg.model.PrivateAppServiceConfig;
import com.facishare.open.msg.result.MessageResult;
import com.facishare.open.msg.service.MsgSessionService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date on 2017/5/2.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring-common-test.xml"})
public class MsgSessionServiceTest {

    @Resource
    private MsgSessionService msgSessionService;


    @Test
    public void testUpdatePrivateAppServiceConfig(){

        for(int i =0;i<10000;i++){
            PrivateAppServiceConfig config = new PrivateAppServiceConfig();
            config.setAppId("FSAID_5f5e276");
            config.setEa("2");
            config.setName("永辉培训");
        config.setPortrait("http://a4.ceshi113.com/FSC/EM/Avatar/GetAvatar?path=N_201704_21_4469e7dd87a54ffc8474b3bd66432adf.jpg&size=150_150&ea=appCenter&type=WEB&width=60&height=60");


            MessageResult messageResult = msgSessionService.updatePrivateAppServiceConfig(config);
            System.out.println("==========================="+messageResult);


        }


    }


}
