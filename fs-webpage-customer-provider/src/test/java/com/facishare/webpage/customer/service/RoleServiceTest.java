package com.facishare.webpage.customer.service;

import com.facishare.privilege.api.RolePrivilegeRestService;
import com.facishare.privilege.api.UserPrivilegeRestService;
import com.facishare.privilege.api.module.PrivilegeContext;
import com.facishare.privilege.api.module.role.GetRoleListVo;
import com.facishare.privilege.api.module.role.RoleSourceVo;
import com.facishare.privilege.api.module.role.RoleVo;
import com.facishare.privilege.api.module.user.GetRolesByUsersVo;
import com.facishare.privilege.api.module.user.UserRoleVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/13 11:46 下午
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:fs-plat-privilege-api-rest-client.xml")
public class RoleServiceTest {

    static {
        System.getProperties().setProperty("process.profile", "fstest");
    }

    @Resource
    private UserPrivilegeRestService userPrivilegeService;
    @Resource
    private RolePrivilegeRestService rolePrivilegeRestService;

    @Test
    public void userRoleCodes() {
        GetRolesByUsersVo.Argument argument = new GetRolesByUsersVo.Argument();
        argument.setRoleSource(RoleSourceVo.INNER_ROLE);
        argument.setAppIds(Sets.newHashSet("CRM"));
        argument.setUserIds(Lists.newArrayList(String.valueOf(1017)));

        PrivilegeContext privilegeContext = PrivilegeContext.builder().appId("CRM").tenantId(71574).operatorId(1017).build();
        List<UserRoleVo> rolesByUsers = userPrivilegeService.getRolesByUsers(privilegeContext, argument);
        System.out.println(rolesByUsers);
    }

    @Test
    public void managerRoleCodes() {
        PrivilegeContext privilegeContext = PrivilegeContext.builder().appId("CRM").tenantId(71574).operatorId(-10000).build();
        GetRoleListVo.Argument argument = new GetRoleListVo.Argument();
        argument.setAppIds(Lists.newArrayList("CRM"));
        List<RoleVo> roleList = rolePrivilegeRestService.getRoleList(privilegeContext, argument);
        System.out.println(roleList);
    }
}
