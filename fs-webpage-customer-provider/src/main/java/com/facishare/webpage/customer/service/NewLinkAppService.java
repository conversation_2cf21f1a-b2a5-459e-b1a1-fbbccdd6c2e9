package com.facishare.webpage.customer.service;

import com.facishare.webpage.customer.api.model.SimpleLinkAppRoleVO;
import com.facishare.webpage.customer.api.model.SimpleLinkAppVO;
import com.facishare.webpage.customer.api.model.arg.*;
import com.facishare.webpage.customer.api.model.result.GetSimpleLinkAppListResult;

import java.util.List;

public interface NewLinkAppService {



    // arg必须有ei
    List<SimpleLinkAppVO> getPresetlinkAppVOListWithEi(GetlinkAppListArg arg);

    List<SimpleLinkAppVO> getPresetlinkAppVOList(GetlinkAppListArg arg);

    SimpleLinkAppVO getlinkAppVOByLinkAppId(GetLinkAppByAppIdArg arg);

    List<SimpleLinkAppVO> getlinkAppVOListByRoleId(GetlinkAppListArg arg);

    GetSimpleLinkAppListResult getLinkAppVOList(GetlinkAppListArg arg);

    void createOrUpdatePresetlinkApp(SimpleLinkAppVO arg);

    void bathCreateOrUpdatelinkAppRole(BathCreateOrUpdatelinkAppRoleArg arg);

    List<SimpleLinkAppRoleVO> getLinkAppRoleList(GetlinkAppRoleListArg arg);

    void deleteLinkAppRole(DeleteLinkAppRoleAra arg);

    void batchAddLinkApp(BatchAddlinkAppListArg arg);

    void createOrUpdateLinkAppUrl(CreateOrUpdateLinkAppUrlArg arg);

    void deleteLinkAppUrl(DeleteLinkAppUrlArg arg);

    List<Integer> listByStatus(GetTenantIdListForLinkAppByStatusArg arg);

    Integer getFirstUpstreamEaByApp(GetLinkAppByAppIdArg arg);

    void batchUpdateStatus(BatchUpdatelinkAppStatusArg arg);

    void deletePresetlinkApp(DeletelinkAppListArg arg);

    void deleteTenantlinkAppRelation(DeletelinkAppListArg arg);

    void deleteByAppId(String appId);
}
