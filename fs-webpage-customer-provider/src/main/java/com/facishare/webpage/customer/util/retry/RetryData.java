package com.facishare.webpage.customer.util.retry;

import lombok.Getter;
import lombok.ToString;

import java.util.List;
import java.util.concurrent.TimeUnit;


@ToString
//TODO
public class RetryData {
    @Getter
    private RetryMethod retryMethod;
    // =1表示正在进行第一次重试中
    private int hasRetryTime;
    // 重试梯度，[1,2] 表示等待1分钟进行第一次重试 再等待2分钟进行第二次重试
    private List<Integer> retryGradientTime;
    @Getter
    private TimeUnit retryTimeUnit;

    public RetryData(RetryMethod retryMethod, List<Integer> retryGradientTime,TimeUnit retryTimeUnit) {
        this.retryMethod = retryMethod;
        this.hasRetryTime = 0;
        this.retryTimeUnit = retryTimeUnit;
        this.retryGradientTime = retryGradientTime;
    }

    public int getNextRetryTimeAndIncrementRetry() {
        return retryGradientTime.get(hasRetryTime++);
    }

    // 如果重试达到最大次数则失败
    public boolean isFailure() {
        return hasRetryTime >= retryGradientTime.size();
    }
}
