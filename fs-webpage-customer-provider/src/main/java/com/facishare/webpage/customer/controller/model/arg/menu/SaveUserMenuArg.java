package com.facishare.webpage.customer.controller.model.arg.menu;

import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.exception.WebPageException;
import com.facishare.webpage.customer.controller.model.TenantMenuSimpleDataVo;
import com.facishare.webpage.customer.controller.model.arg.BaseArg;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> Yu
 * @date 2022/3/4 11:04 AM
 */
@Data
@Builder
public class SaveUserMenuArg extends BaseArg {
    /**
     * 菜单id
     */
    private String menuId;
    /**
     * 保存模板的类型
     */
    private Integer pageTemplateType;
    /**
     * 应用id
     */
    private String appId;
    /**
     * 菜单数据
     */
    private List<TenantMenuSimpleDataVo> tenantMenuSimpleItems;

    /**
     * 菜单折叠时，是否显示菜单图标
     */
    private Boolean isShowMenuIcon = true;

    /**
     *隐藏快速新建
     */
    private Boolean hiddenQuickCreate = false;

    @Override
    public void valid() throws Exception {
        if (pageTemplateType == null) {
            throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
        }
    }
}
