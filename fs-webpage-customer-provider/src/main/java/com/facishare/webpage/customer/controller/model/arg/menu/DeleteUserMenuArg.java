package com.facishare.webpage.customer.controller.model.arg.menu;

import com.facishare.webpage.customer.controller.model.arg.BaseArg;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/11/24.
 */
@Data
public class DeleteUserMenuArg extends BaseArg {

    @SerializedName("menu_id")
    private String menuId;

    @Override
    public void valid() throws Exception {

    }
}
