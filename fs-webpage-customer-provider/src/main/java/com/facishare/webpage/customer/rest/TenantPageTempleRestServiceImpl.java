package com.facishare.webpage.customer.rest;

import com.alibaba.fastjson.JSONObject;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.converter.EIEAConverter;
import com.facishare.rest.core.annotation.HeaderParam;
import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.constant.*;
import com.facishare.webpage.customer.api.exception.WebPageException;
import com.facishare.webpage.customer.api.model.I18nTrans;
import com.facishare.webpage.customer.api.model.PageTemplate;
import com.facishare.webpage.customer.api.model.Scope;
import com.facishare.webpage.customer.api.model.UserPageTemplate;
import com.facishare.webpage.customer.api.model.arg.*;
import com.facishare.webpage.customer.api.model.result.*;
import com.facishare.webpage.customer.api.service.TenantPageTempleRestService;
import com.facishare.webpage.customer.common.LanguageService;
import com.facishare.webpage.customer.common.OrganizationCommonService;
import com.facishare.webpage.customer.core.util.ScopesUtil;
import com.facishare.webpage.customer.dao.TenantPageTempleDao;
import com.facishare.webpage.customer.dao.entity.PageTempleEntity;
import com.facishare.webpage.customer.model.UtilityBarVO;
import com.facishare.webpage.customer.service.*;
import com.fxiaoke.enterpriserelation2.result.GetOuterAccountByFsResult;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@Slf4j
@RequestMapping("/webPage/tenantPageTempleRestService")
public class TenantPageTempleRestServiceImpl implements TenantPageTempleRestService {

    @Autowired
    private TenantPageTempleBaseService tenantPageTempleBaseService;
    @Autowired
    private RemoteService remoteService;
    @Autowired
    private UserPageTempleService userPageTempleService;
    @Resource
    private RemoteCrossService remoteCrossService;
    @Autowired
    private EIEAConverter eieaConverter;
    @Resource
    private OrganizationCommonService organizationCommonService;
    @Autowired
    TenantPageTempleDao tenantPageTempleDao;
    @Autowired
    private LanguageService languageService;
    @Autowired
    private UtilityBarService utilityBarService;

    @RequestMapping(value = "/checkUserPermission", method = RequestMethod.POST)
    @Override
    public BaseApiResult checkUserPermission(@HeaderParam("x-fs-ei") String tenantId,
                                             @RequestBody CheckUserPermissionApiArg apiArg) {
        boolean status = checkUserPermissionStatus(apiArg);
        log.debug("checkUserPermission status {} case by  {}", status, apiArg);
        return CheckUserPermissionApiResult.success(status);
    }

    @RequestMapping(value = "/getPageTemplates", method = RequestMethod.POST)
    @Override
    public GetPageTemplatesResult getPageTemplates(@HeaderParam("x-fs-ei") String tenantId,
                                                   @RequestBody GetPageTemplatesArg arg) {
        List<Integer> upTenantIds = remoteCrossService.getUpTenantIds(arg.getDownTenantId());
        log.debug("getUpTenantIds upTenantIds {}", upTenantIds);
        GetPageTemplatesResult result = new GetPageTemplatesResult();
        try {
            UserInfo userInfo = new UserInfo();
            userInfo.setEnterpriseAccount(eieaConverter.enterpriseIdToAccount(arg.getDownTenantId()));
            userInfo.setEnterpriseId(arg.getDownTenantId());
            userInfo.setEmployeeId(arg.getDownEmployeeId());
            String appId = arg.getAppId();

            GetOuterAccountByFsResult outerAccount = userPageTempleService.getOuterAccount(userInfo);

            //对互联应用做权限校验
            userPageTempleService.checkCrossPermission(upTenantIds, appId, outerAccount);

            Map<Integer, List<String>> outerRoleIdMap = userPageTempleService.getOuterRoleIdMap(appId, upTenantIds, outerAccount);
            List<UserPageTemplate> userPageTemplates = userPageTempleService.getUserPageByTenantIds(appId, upTenantIds, outerRoleIdMap, TempleType.APP);
            result.setPageTemplates(userPageTemplates);
        } catch (WebPageException e) {
            log.warn("getPageTemplates error by arg : {}", arg, e);
        } catch (Exception e) {
            log.error("getPageTemplates error by arg : {}", arg, e);
        }
        return result;
    }

    @RequestMapping(value = "/checkUserPermissionV2", method = RequestMethod.POST)
    @Override
    public CheckUserPermissionResult checkUserPermissionV2(@HeaderParam("x-fs-ei") String tenantId,
                                                           @RequestBody CheckUserPermissionApiArg arg) {
        boolean status = checkUserPermissionStatus(arg);
        log.debug("checkUserPermissionV2 status {} case by  {}", status, arg);
        CheckUserPermissionResult result = new CheckUserPermissionResult();
        result.setSuccess(status);
        return result;
    }

    @RequestMapping(value = "/getPageTemplateById", method = RequestMethod.POST)
    @Override
    public GetPageTemplateByIdRestResult getPageTemplateById(@HeaderParam("x-fs-ei") String tenantId,
                                                             @RequestBody GetPageTemplateByIdRestArg arg) {
        arg.valid();
        PageTemplate pageTemplate = tenantPageTempleBaseService.getPageTemplateById(arg.getEnterpriseId(), arg.getTempleId());
        if (ObjectUtils.isEmpty(pageTemplate)) {
            throw new WebPageException(InterErrorCode.PAGE_TEMPLE_NOT_FUND);
        }

        GetPageTemplateByIdRestResult result = new GetPageTemplateByIdRestResult();
        result.setPageTemplate(pageTemplate);
        return result;
    }

    @RequestMapping(value = "/getToolBarById", method = RequestMethod.POST)
    @Override
    public GetToolBarById.Result getToolBarById(@HeaderParam("x-fs-ei") String tenantId, @RequestBody GetToolBarById.Arg arg) {
        arg.valid();
        UserInfo userInfo = new UserInfo();
        userInfo.setEnterpriseId(arg.getTenantId());
        userInfo.setEmployeeId(arg.getEmployeeId());

        UtilityBarVO utilityBarVO = utilityBarService.queryUtilityBarForManager(userInfo, arg.getAppId(), arg.getBizType(),
                arg.getPageTemplateId(), StringUtils.isNotEmpty(arg.getLang()) ? Locale.forLanguageTag(arg.getLang()) : Locale.CHINA);

        if (ObjectUtils.isEmpty(utilityBarVO)) {
            throw new WebPageException(InterErrorCode.PAGE_TEMPLE_NOT_FUND);
        }
        GetToolBarById.Result result = GetToolBarById.Result.builder().id(utilityBarVO.getId()).build();
        JSONObject componentJson;
        try {
            //如果转成对象报错的话，代表
            componentJson = utilityBarVO.getUtilityBarLayout().getJSONObject("components");
        } catch (ClassCastException e) {
            return result;
        }
        if (Objects.isNull(componentJson)) {
            return result;
        }
        List<GetToolBarById.ToolBarComponent> componentsList = Lists.newArrayList();

        for (String key : componentJson.keySet()) {
            JSONObject jsonObject = componentJson.getJSONObject(key);
            if (Objects.isNull(jsonObject)) {
                continue;
            }
            JSONObject props = jsonObject.getJSONObject(CustomerLayoutField.props);
            if (Objects.isNull(props)) {
                continue;
            }

            List<String> preKeys = Lists.newArrayList();
            String nameI18nKey = props.getString(CustomerLayoutField.nameI18nKey);
            if (StringUtils.isNotBlank(nameI18nKey)) {
                preKeys.add(nameI18nKey);
            }
            String customKey = TranslateI18nUtils.getToolBarToolNameKey(utilityBarVO.getId(), props.getString(CustomerLayoutField.apiName));
            I18nTrans.TransArg transArg = TranslateI18nUtils.convertToTransArgWithoutEi(arg.getTenantId(), customKey, preKeys, null);
            componentsList.add(GetToolBarById.ToolBarComponent.builder()
                    .apiName(props.getString(CustomerLayoutField.apiName))
                    .header(props.getString(CustomerLayoutField.header))
                    .key(transArg.getCustomKey())
                    .preKeyList(transArg.getPreKeyList())
                    .newHeader(props.getString(CustomerLayoutField.newHeader))
                    .build());
        }
        return GetToolBarById.Result.builder().id(utilityBarVO.getId())
                .components(componentsList)
                .build();
    }

    @RequestMapping(value = "/queryScopeList", method = RequestMethod.POST)
    @Override
    public QueryScopeListRestResult queryScopeList(@HeaderParam("x-fs-ei") String tenantId,
                                                   @RequestBody QueryScopeListRestArg arg) {
        QueryScopeListRestResult queryScopeListRestResult = new QueryScopeListRestResult();
        List<Scope> scopeList = organizationCommonService.queryScopeList(arg.getTenantId(), arg.getEmployeeId(), arg.getOutTenantId(), arg.getOutUserId(), arg.getAppId());
        queryScopeListRestResult.setScopeList(CollectionUtils.isEmpty(scopeList) ? new ArrayList<>() : scopeList);
        return queryScopeListRestResult;
    }

    @RequestMapping(value = "/updateScopeList", method = RequestMethod.POST)
    @Override
    public UpdatePageTempleScopeListRestResult updateScopeList(@HeaderParam("x-fs-ei") String tenantId,
                                                               @RequestBody UpdatePagetempleScopeListRestArg arg) {
        UpdatePageTempleScopeListRestResult result = new UpdatePageTempleScopeListRestResult();
        PageTempleEntity pageTempleEntity = tenantPageTempleDao.getPageTemplateById(arg.getPageTempleId(), null);
        if (null == pageTempleEntity) {
            log.error("updateScopeList pageTempleEntity is null,pageTempleId:{}", arg.getPageTempleId());
        }
        pageTempleEntity.setScopes(arg.getScopeList());
        tenantPageTempleDao.findAndModify(arg.getTenantId(), pageTempleEntity);
        return result;
    }

    @RequestMapping(value = "/getPageTemplesByTypeAndAppIds", method = RequestMethod.POST)
    @Override
    public GetPageTemplesByTypeAndAppIdsResult getPageTemplesByTypeAndAppIds(@HeaderParam("x-fs-ei") String tenantId,
                                                                             @RequestBody GetPageTemplesByTypeAndAppIdsArg arg) {
        GetPageTemplesByTypeAndAppIdsResult result = new GetPageTemplesByTypeAndAppIdsResult();
        if (null == arg || CollectionUtils.isEmpty(arg.getAppIds())) {
            return result;
        }
        List<PageTemplate> pageTemplateList = new ArrayList<>();
        if (arg.getAppIds().size() == 1) {
            List<PageTemplate> pageTemplates = tenantPageTempleBaseService.getCustomerPageTemples(arg.getTenantId(), arg.getAppIds().get(0), arg.getType());
            for (PageTemplate pageTemplate : pageTemplates) {
                TranslateI18nUtils.PreAndDefaultTranslateKey translateKey = TranslateI18nUtils.getTemplateNamePreTranslateKey(pageTemplate.getTenantId(), pageTemplate.getTempleId());
                String customKey = TranslateI18nUtils.getTemplateNameKey(pageTemplate.getTempleId());
                I18nTrans.TransArg transKeyInfo = TranslateI18nUtils.formatTransKey(arg.getTenantId(), translateKey.convertToTransKeyInfo(customKey));
                pageTemplateList.add(PageTemplate.builder().templeId(pageTemplate.getTempleId()).name(pageTemplate.getName())
                        .appPageId(pageTemplate.getAppPageId()).webPageId(pageTemplate.getWebPageId())
                        .webMenuId(pageTemplate.getWebMenuId())
                        .translateKey(transKeyInfo.getCustomKey())
                        .preTranslateKeys(transKeyInfo.getPreKeyList())
                        .defaultTranslateKey(translateKey.getDefaultTranslateKey())
                        .build());
            }
        } else {
            List<PageTempleEntity> pageTempleEntities = tenantPageTempleDao.getPageTemplesByTypeAndAppIds(arg.getTenantId(), arg.getAppIds(), arg.getType());
            if (CollectionUtils.isEmpty(pageTempleEntities)) {
                return result;
            }
            for (PageTempleEntity pageTempleEntity : pageTempleEntities) {
                TranslateI18nUtils.PreAndDefaultTranslateKey translateKey = TranslateI18nUtils.getTemplateNamePreTranslateKey(pageTempleEntity.getTenantId(), pageTempleEntity.getTempleId());
                String customKey = TranslateI18nUtils.getTemplateNameKey(pageTempleEntity.getTempleId());
                I18nTrans.TransArg transKeyInfo = TranslateI18nUtils.formatTransKey(arg.getTenantId(), translateKey.convertToTransKeyInfo(customKey));
                pageTemplateList.add(PageTemplate.builder().templeId(pageTempleEntity.getTempleId()).name(pageTempleEntity.getName())
                        .appPageId(pageTempleEntity.getAppPageId()).webPageId(pageTempleEntity.getWebPageId())
                        .webMenuId(pageTempleEntity.getWebMenuId())
                        .translateKey(transKeyInfo.getCustomKey())
                        .preTranslateKeys(transKeyInfo.getPreKeyList())
                        .defaultTranslateKey(translateKey.getDefaultTranslateKey())
                        .build());
            }
        }
        if (CollectionUtils.isNotEmpty(pageTemplateList)) {
            Map<String, String> templateKeyMap = languageService.queryPaaSTemplateLanguage(arg.getTenantId(), pageTemplateList, arg.getLocale(), true);
            pageTemplateList.forEach(pageTemplate -> {
                pageTemplate.setName(StringUtils.defaultIfBlank(templateKeyMap.get(TranslateI18nUtils.getTemplateNameKey(pageTemplate.getTempleId())), pageTemplate.getName()));
            });
        }
        result.setPageTemplateList(pageTemplateList);
        return result;
    }

    /**
     * 视图更改集同步场景：
     * 1、出站企业web端视图同步app端，且入站到了入站企业
     * 2、此时出站企业关闭视图同步
     * 3、再次出站入站到入站企业
     * 4、此时要把入站企业app视图同步删除掉
     *
     * @param tenantId
     * @param arg
     * @return
     */
    @RequestMapping(value = "/changeSetAfterAction", method = RequestMethod.POST)
    @Override
    public PageTempleChangeSetAfterActionResult changeSetAfterAction(@HeaderParam("x-fs-ei") String tenantId, @RequestBody PageTempleChangeSetAfterActionArg arg) {
        PageTempleChangeSetAfterActionResult result = new PageTempleChangeSetAfterActionResult();
        if (null == arg || StringUtils.isBlank(arg.getPageTempleId()) || arg.getOleTenantId() == 0 || arg.getTenantId() == 0) {
            log.warn("changeSetAfterAction arg is null or pageTempleIdempty or oleTenantId or tenantId is 0");
            return result;
        }

        String newWebPageTempleId = arg.getPageTempleId().replace(String.valueOf(arg.getOleTenantId()), String.valueOf(arg.getTenantId()));

        PageTempleEntity newWebPageTempleEntity = tenantPageTempleDao.getPageTemplateById(newWebPageTempleId, null);
        log.info("changeSetAfterAction.newPageTempleEntity={}", JSONObject.toJSONString(newWebPageTempleEntity));
        if (null != newWebPageTempleEntity && "web".equals(newWebPageTempleEntity.getType())) {
            List<PageTempleEntity> appPageTempleEntitys = tenantPageTempleDao.getPageTemplatesBySynFromWebTemplateId(newWebPageTempleId, arg.getTenantId());
            log.info("changeSetAfterAction.newPageTempleEntity={}", JSONObject.toJSONString(appPageTempleEntitys));

            if (CollectionUtils.isNotEmpty(appPageTempleEntitys)) {
                List<PageTempleEntity> needDeleteAppPageTempleEntitys = new ArrayList<>();
                if (newWebPageTempleEntity.isHasBeenSynToApp()) {
                    needDeleteAppPageTempleEntitys.addAll(appPageTempleEntitys.stream().filter(x -> !x.getTempleId().equals(newWebPageTempleEntity.getAppTempleId())).collect(Collectors.toList()));
                } else {
                    needDeleteAppPageTempleEntitys.addAll(appPageTempleEntitys);
                }
                //删除移动视图
                needDeleteAppPageTempleEntitys.forEach(appPageTempleEntity -> {
                    if (appPageTempleEntity != null && appPageTempleEntity.getStatus() != AllStatus.PageTempleStatus.DELETE) {
                        tenantPageTempleBaseService.updatePageTempleStatus(appPageTempleEntity.getTempleId(), arg.getTenantId(), -10000, AllStatus.PageTempleStatus.DELETE);
                    }
                });
            }
        }

        return result;
    }

    private boolean checkUserPermissionStatus(CheckUserPermissionApiArg arg) {
        PageTemplate pageTemplate = tenantPageTempleBaseService.getPageTemplateById(arg.getTenantId(), arg.getTempleId());
        if (pageTemplate == null || pageTemplate.getStatus() != 0) {
            return false;
        }
        boolean status = remoteService.checkUserPermission(arg.getTenantId(), pageTemplate.getAppId(), arg.getOuterTenantId(),
                arg.getOuterUserId(), ScopesUtil.getDataId(ScopeType.OutRole.getType(), pageTemplate.getScopeList()));
        return status;
    }
}
