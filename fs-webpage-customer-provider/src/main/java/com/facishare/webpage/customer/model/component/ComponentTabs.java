package com.facishare.webpage.customer.model.component;

import com.alibaba.fastjson.JSONObject;
import com.facishare.webpage.customer.constant.ComponentConstant;
import com.facishare.webpage.customer.core.component.Component;
import com.facishare.webpage.customer.core.config.ComponentNameConfig;
import com.facishare.webpage.customer.core.model.ComponentDto;
import com.facishare.webpage.customer.core.model.ComponentTypConst;
import com.facishare.webpage.customer.core.model.Widget;
import com.facishare.webpage.customer.core.util.DropListUtil;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.util.Map;

/**
 * Created by zhangyu on 2020/4/7
 */
@Data
public class ComponentTabs extends Component {

    private ComponentDto componentDto;

    private Map<String, String> componentLanguage;

    private ComponentNameConfig componentNameConfig;

    @Override
    public String getId() {
        return componentDto.getId();
    }

    @Override
    public String getPId() {
        return componentDto.getParentId();
    }

    @Override
    public String getName() {
        return DropListUtil.getDropListName(componentDto, componentLanguage);
    }

    @Override
    public String getDropItemType() {
        return componentDto.getComponentType() == ComponentTypConst.WIDGET_TYPE ? ComponentConstant.dropListCompType : ComponentConstant.dropListGroupType;
    }

    @Override
    public String getApiName() {
        return componentDto.getWidget().getId();
    }

    @Override
    public String getType() {
        if (componentDto.getComponentType() != ComponentTypConst.WIDGET_TYPE){
            return "";
        }

        Widget widget = componentDto.getWidget();

        return componentNameConfig.getComponentName(StringUtils.isEmpty(widget.getCardId()) ? widget.getId() : widget.getCardId());
    }

    @Override
    public int getLimit() {
        return componentDto.getWidget().getLimit();
    }

    @Override
    public JSONObject getProps() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("api_name", getApiName());
        jsonObject.put("buttons", Lists.newArrayList());
        jsonObject.put("components", Lists.newArrayList());
        jsonObject.put("header", getName());
        jsonObject.put("limit", getLimit());
        jsonObject.put(getApiName(), Lists.newArrayList());
        jsonObject.put("type", componentDto.getWidget().getWidgetType());
        if (componentDto.getWidget().getExtProp() != null){
            jsonObject.putAll(componentDto.getWidget().getExtProp());
        }

        if (componentDto.getWidget() != null && componentDto.getWidget().getWidgetScope() != null) {
            jsonObject.putAll(componentDto.getWidget().getWidgetScope());
        }

        return jsonObject;
    }

    @Override
    public int getGrayLimit() {
        return 1;
    }
}
