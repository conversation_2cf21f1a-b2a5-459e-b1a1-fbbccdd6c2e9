package com.facishare.webpage.customer.migrate.dao.entity;

import lombok.Data;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Property;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/23 2:21 PM
 */
@Data
@Entity(value = "UserAppPageMigrateEntity", noClassnameStored = true)
public class UserAppPageMigrateEntity {

    @Id
    private ObjectId id;
    /**
     * 企业id
     */
    @Property("TId")
    private int tenantId;
    /**
     * 员工id
     */
    @Property("EId")
    private int employeeId;
    /**
     * 首页id
     */
    @Property("WPId")
    private String webPageId;
    /**
     * 常用菜单数据
     */
    @Property("CMS")
    private List<String> commonMenus;
    /**
     * 移动端自定义页面id
     */
    @Property("APTId")
    private String appPageTemplateId;
    /**
     * 迁移状态
     */
    @Property("S")
    private int status;


}
