package com.facishare.webpage.customer.rest;

import com.facishare.cep.plugin.enums.ClientTypeEnum;
import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.converter.EIEAConverter;
import com.facishare.rest.core.annotation.HeaderParam;
import com.facishare.webpage.customer.api.constant.PaaSAppStatus;
import com.facishare.webpage.customer.api.constant.SourceType;
import com.facishare.webpage.customer.api.constant.TempleType;
import com.facishare.webpage.customer.api.constant.TranslateI18nUtils;
import com.facishare.webpage.customer.api.model.*;
import com.facishare.webpage.customer.api.model.arg.*;
import com.facishare.webpage.customer.api.model.result.*;
import com.facishare.webpage.customer.api.service.PaaSAppRestService;
import com.facishare.webpage.customer.api.utils.RequestContextManager;
import com.facishare.webpage.customer.api.utils.WebPageUtils;
import com.facishare.webpage.customer.common.CheckService;
import com.facishare.webpage.customer.config.AppIdListConfig;
import com.facishare.webpage.customer.config.AppMenuConfig;
import com.facishare.webpage.customer.constant.AppTypeEnum;
import com.facishare.webpage.customer.controller.PaaSAppAction;
import com.facishare.webpage.customer.controller.model.arg.paas.GetAppStatusArg;
import com.facishare.webpage.customer.controller.model.result.paas.GetAppListResult;
import com.facishare.webpage.customer.controller.model.result.paas.GetAppStatusResult;
import com.facishare.webpage.customer.dao.PaaSAppDao;
import com.facishare.webpage.customer.dao.entity.PaaSAppEntity;
import com.facishare.webpage.customer.dao.entity.PageTempleEntity;
import com.facishare.webpage.customer.mq.model.AppViewChangeEvent;
import com.facishare.webpage.customer.mq.producer.InnerAppMqProducer;
import com.facishare.webpage.customer.remote.InnerAppCacheService;
import com.facishare.webpage.customer.remote.RoleService;
import com.facishare.webpage.customer.service.PaaSAppService;
import com.facishare.webpage.customer.service.TenantPageTempleBaseService;
import com.facishare.webpage.customer.system.TemplateSystemService;
import com.facishare.webpage.customer.util.PaaSAppUtil;
import com.fxiaoke.api.model.BatchGetPageTemplate;
import com.fxiaoke.api.service.PageTemplateService;
import com.github.jedis.support.JedisCmd;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.webpage.customer.constant.WebPageConstants.PAAS_APP_FUNCTION;

@RestController
@Slf4j
@RequestMapping("/webPage/PaaSAppRestService")
public class PaaSAppRestServiceImpl implements PaaSAppRestService {
    @Resource
    private PaaSAppService paaSAppService;

    @Autowired
    private PaaSAppAction paaSAppAction;

    @Autowired
    private TenantPageTempleBaseService tenantPageTempleBaseService;

    @Autowired
    private TemplateSystemService templateSystemService;

    @Autowired
    private PaaSAppDao paaSAppDao;

    @Autowired
    EIEAConverter eieaConverter;

    @Autowired
    private InnerAppMqProducer innerAppMqProducer;
    @Resource
    private JedisCmd webpageRedis;
    @Resource
    private PageTemplateService pageTemplateService;
    @Resource
    private RoleService roleService;
    @Resource
    private CheckService checkService;
    @Resource
    private AppMenuConfig appMenuConfig;
    @Resource
    private AppIdListConfig appIdListConfig;

    private static final String APP_NAME = "appName";
    private static final String ENTRY_NAME = "entryName";
    private static final String WEB_TEMPLATE_NAME = "webTemplateName";
    private static final String APP_TEMPLATE_NAME = "appTemplateName";
    private static final String APP_TEMPLATE_FOOT_MENU_NAME = "appTemplateFootMenuName";
    private static final String WEB_TEMPLATE_WIDGET_NAME = "webTemplateWidgetsName";
    private static final String MENU_GROUP_NAME = "menuGroupName";
    private static final String TOOL_BAR_TOOL_NAME = "toolbarToolName";
    public static final String SEARCH_TYPE_WEB_LABEL_NAME = "webLabelName";

    public static final String SEARCH_TYPE_APP_TEMPLATE_WIDGET_NAME = "appTemplateWidgetsName";
    public static final String SEARCH_TYPE_APP_TAB_NAME = "appTabName";
    public static final String SEARCH_TYPE_WEB_TAB_NAME = "webTabName";
    public static final String SEARCH_TYPE_APP_LABEL_NAME = "appLabelName";
    public static final String SEARCH_TYPE_APP_MENU_NAME = "appMenuName";

    @RequestMapping(value = "/getPaaSAppInfo", method = RequestMethod.POST)
    @Override
    public GetPaaSAppInfoResult getPaaSAppInfo(@RequestHeader Map<String, String> headers, @RequestBody GetPaaSAppInfoArg arg) {
        arg.valid();
        PaaSAppVO paaSAppVO = paaSAppService.getPaaSAppByAppId(arg.getTenantId(), arg.getAppId(), arg.getLocale());
        GetPaaSAppInfoResult result = new GetPaaSAppInfoResult();
        result.setPaaSAppVO(paaSAppVO);
        return result;
    }

    @RequestMapping(value = "/getAppList", method = RequestMethod.POST)
    @Override
    public QueryPaasAppResult getAppList(@HeaderParam("x-fs-ei") String tenantId, @RequestBody QueryPaasAppArg arg) {
        RequestContextManager.initContextForIsFromRest(true);
        List<PaasAppRestVO> paasAppRestVOList = new ArrayList<>();
        QueryPaasAppResult queryPaasAppResult = QueryPaasAppResult.builder().paasAppRestVOList(paasAppRestVOList).build();
        UserInfo userInfo = new UserInfo();
        userInfo.setEnterpriseId(arg.getEnterpriseId());
        userInfo.setEmployeeId(arg.getEmployeeId());
        userInfo.setEnterpriseAccount(eieaConverter.enterpriseIdToAccount(arg.getEnterpriseId()));
        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setLocale(Objects.isNull(arg.getLocale()) ? Locale.CHINA : arg.getLocale());
        GetAppListResult ret = paaSAppAction.getAppList(userInfo, clientInfo);
        if (null != ret && CollectionUtils.isNotEmpty(ret.getAppItemVOList())) {
            List<String> appIds = ret.getAppItemVOList().stream().filter(item -> item.getStatus() == PaaSAppStatus.ENABLE).map(item -> item.getAppId()).collect(Collectors.toList());

            List<PageTemplate> pageTemplates = tenantPageTempleBaseService.queryPageTempleIdsByAppIds(arg.getEnterpriseId(), appIds, TempleType.APP);
            Map<String, List<PageTemplate>> map = pageTemplates.stream().collect(Collectors.groupingBy(pageTemplate -> pageTemplate.getAppId()));
            List<String> noCustomerTemplateSystemAppIds = Lists.newArrayList();
            ret.getAppItemVOList().forEach(item -> {
                PaasAppRestVO paasAppRestVO = PaasAppRestVO.builder()
                        .appId(item.getAppId())
                        .name(item.getName())
                        .icon(item.getIcon())
                        .status(item.getStatus())
                        .appType(item.getAppType())
                        .hasAppView(CollectionUtils.isNotEmpty(map.get(item.getAppId())))
                        .jumpUrl(PaaSAppUtil.buildJumpUrl(item.getAppId()))
                        .build();
                paasAppRestVOList.add(paasAppRestVO);
                //预置的PaaS应用需要判断是否有预置的视图
                if (!paasAppRestVO.isHasAppView()
                        && AppTypeEnum.PAAS_APP.getAppType() == item.getAppType()
                        && SourceType.SYSTEM.equals(item.getSourceType())) {
                    noCustomerTemplateSystemAppIds.add(item.getAppId());
                }
            });

            //判断是否有预置的视图
            if (CollectionUtils.isNotEmpty(noCustomerTemplateSystemAppIds)) {
                Map<String, List<PageTempleEntity>> systemTemplateMap = templateSystemService.querySystemTemplateMap(arg.getEnterpriseId(),
                        AppTypeEnum.PAAS_APP.getAppType(), noCustomerTemplateSystemAppIds, TempleType.APP);
                paasAppRestVOList.stream()
                        .filter(item -> systemTemplateMap.containsKey(item.getAppId()))
                        .forEach(item -> item.setHasAppView(true));
            }
        }
        return queryPaasAppResult;
    }

    @RequestMapping(value = "/getUserPaaSAppList", method = RequestMethod.POST)
    public QueryPaasAppResult getUserPaaSAppList(@HeaderParam("x-fs-ei") String tenantId, @RequestBody QueryPaasAppArg arg) {
        List<PaasAppRestVO> paasAppRestVOList = new ArrayList<>();
        QueryPaasAppResult queryPaasAppResult = QueryPaasAppResult.builder().paasAppRestVOList(paasAppRestVOList).build();
        UserInfo userInfo = new UserInfo();
        userInfo.setEnterpriseId(arg.getEnterpriseId());
        userInfo.setEmployeeId(arg.getEmployeeId());
        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setLocale(Objects.isNull(arg.getLocale()) ? Locale.CHINA : arg.getLocale());
        List<PaaSAppVO> paaSAppList = paaSAppService.getUserPaaSAppList(arg.getEnterpriseId(), arg.getEmployeeId(), arg.getLocale());
        if (CollectionUtils.isNotEmpty(paaSAppList)) {
            paaSAppList.forEach(item ->
                    paasAppRestVOList.add(PaasAppRestVO.builder()
                            .appId(item.getAppId())
                            .name(item.getName())
                            .icon(item.getIcon())
                            .status(item.getStatus())
                            .jumpUrl(PaaSAppUtil.buildJumpUrl(item.getAppId()))
                            .build())
            );
        }
        return queryPaasAppResult;
    }

    @Override
    @RequestMapping(value = "/saveTenantPaaSAppInfo", method = RequestMethod.POST)
    public SavePaasAppResult saveTenantPaaSAppInfo(@HeaderParam("x-fs-ei") String tenantId, @RequestBody SavePaasAppArg arg) {
        PaaSAppVO paaSAppVO = PaaSAppVO.builder()
                .appId(arg.getAppId())
                .scopeList(arg.getScopes())
                .appType(AppTypeEnum.BASE_APP.getAppType())
                .status(arg.getStatus())
                .accessType(arg.getAccessType())
                .parentAppId(arg.getParentAppId())
                .build();
        try {
            paaSAppService.findAndModifyPreApp(arg.getTenantId(), arg.getUserId(), paaSAppVO);
            //清除缓存
            webpageRedis.del(WebPageUtils.buildAppStatusKey(arg.getAppId(), arg.getTenantId()));
        } catch (Exception e) {
            return SavePaasAppResult.builder().result(false).build();
        }
        return SavePaasAppResult.builder().result(true).build();
    }

    @Override
    @RequestMapping(value = "/getUserPerAppList", method = RequestMethod.POST)
    public GetUserPerAppResult getUserPerAppList(@HeaderParam("x-fs-ei") String tenantId, @RequestBody GetUserPerAppArg arg) {
        arg.valid();
        //获取在适用范围中的app信息
        List<PaaSAppVO> paaSAppList = paaSAppService.getUserPaaSAppListByType(arg.getTenantId(), arg.getEmployeeId(), arg.getAccessTypeList(), AppTypeEnum.BASE_APP.getAppType());
        if (CollectionUtils.isEmpty(paaSAppList)) {
            return new GetUserPerAppResult();
        }
        return GetUserPerAppResult.builder().
                preAppInfoByUser(paaSAppList).
                build();
    }

    @Override
    @RequestMapping(value = "/getTenantPerAppList", method = RequestMethod.POST)
    public GetTenantPerAppResult getTenantPerAppList(@HeaderParam("x-fs-ei") String tenantId, @RequestBody GetTenantPerAppArg arg) {
        arg.valid();
        List<String> accessTypeList = arg.getAccessTypeList().stream().map(x -> x.getType()).collect(Collectors.toList());
        //获取所有在paas应用存适用范围的appId
        List<PaaSAppVO> paaSAppList = paaSAppService.getPaaSAppIdListByType(arg.getTenantId(), AppTypeEnum.BASE_APP.getAppType(), accessTypeList, Locale.CHINA);
        if (CollectionUtils.isEmpty(paaSAppList)) {
            return new GetTenantPerAppResult();
        }
        return GetTenantPerAppResult.builder().
                preAppInfoByTenant(paaSAppList).
                build();
    }

    @Override
    @RequestMapping(value = "/clearTenantCache", method = RequestMethod.POST)
    public ClearTenantCacheResult clearTenantCache(@HeaderParam("x-fs-ei") String tenantId, @RequestBody ClearTenantCacheArg arg) {
        String ea = eieaConverter.enterpriseIdToAccount(arg.getTenantId());
        AppViewChangeEvent appViewChangeEvent = new AppViewChangeEvent();
        appViewChangeEvent.setDelEaAuth(true);
        appViewChangeEvent.setFsEa(ea);
        //删除全公司缓存
        appViewChangeEvent.setChangeType(1);
        boolean result = innerAppMqProducer.sendEvent(appViewChangeEvent);
        return ClearTenantCacheResult.builder().success(result).build();
    }

    @Override
    @RequestMapping(value = "/getPaaSAppByAppIds", method = RequestMethod.POST)
    public QueryPaasAppResult getPaaSAppByAppIds(@HeaderParam("x-fs-ei") String tenantId, @RequestBody QueryPaasAppListByIdsArg arg) {
        List<PaasAppRestVO> paasAppRestVOList = new ArrayList<>();
        QueryPaasAppResult queryPaasAppResult = QueryPaasAppResult.builder().paasAppRestVOList(paasAppRestVOList).build();
        List<PaaSAppVO> paaSAppList = paaSAppService.getPaaSAppByAppIds(arg.getEnterpriseId(), arg.getAppIds(), null, null == arg.getLocale() ? Locale.CHINA : arg.getLocale());
        if (CollectionUtils.isNotEmpty(paaSAppList)) {
            paaSAppList.forEach(item ->
                    paasAppRestVOList.add(PaasAppRestVO.builder()
                            .appId(item.getAppId())
                            .name(item.getName())
                            .icon(item.getIcon())
                            .status(item.getStatus())
                            .jumpUrl(PaaSAppUtil.buildJumpUrl(item.getAppId()))
                            .build())
            );
        }
        return queryPaasAppResult;
    }

    @Override
    @RequestMapping(value = "/getTenantPaaSAppStatusByAppId", method = RequestMethod.POST)
    public GetTenantAppStatusResult getTenantPaaSAppStatusByAppId(@HeaderParam("x-fs-ei") String tenantId, @RequestBody GetTenantAppStatusArg arg) {
        int ei = arg.getTenantId();
        UserInfo userInfo = new UserInfo();
        userInfo.setEmployeeId(arg.getEmployeeId());
        userInfo.setEnterpriseId(ei);
        String ea = eieaConverter.enterpriseIdToAccount(ei);
        userInfo.setEnterpriseAccount(ea);
        ClientInfo clientInfo = new ClientInfo();
        /**
         * 其他团队的屏蔽统一按照禁用启用按钮屏蔽
         */
        if (arg.isFollowClientType()) {
            clientInfo.setType(arg.getClientTypeEnum());
        } else {
            clientInfo.setType(ClientTypeEnum.Web);
        }
        GetAppStatusArg args = new GetAppStatusArg();
        if (StringUtils.isNotEmpty(arg.getAppId())) {
            args.setAppId(arg.getAppId());
        }
        GetAppStatusResult result = paaSAppAction.getQiXinAppStatus(userInfo, clientInfo, args);
        return GetTenantAppStatusResult.builder().
                hiddenApplicationEntry(result.isHiddenApplicationEntry()).
                build();
    }

    @Override
    @RequestMapping(value = "/savePaasAppEntity", method = RequestMethod.POST)
    public SavePaasAppEntityResult savePaasAppEntity(@HeaderParam("x-fs-ei") String tenantId, @RequestBody PaaSAppEntityVO arg) {
        PaaSAppEntity paaSAppEntity = new PaaSAppEntity();
        BeanUtils.copyProperties(arg, paaSAppEntity);
        if (AppTypeEnum.LINK_APP.getAppType() == arg.getAppType()) {
            paaSAppDao.findAndModifyPaaSAppEntityForLinkApp(arg.getTenantId(), paaSAppEntity);
        } else {
            paaSAppDao.savePaaSApp(paaSAppEntity);
        }
        return new SavePaasAppEntityResult();
    }

    @Override
    @RequestMapping(value = "/updatePaasAppScopeList", method = RequestMethod.POST)
    public UpdatePaaSAppScopeListResult updatePaasAppScopeList(@HeaderParam("x-fs-ei") String tenantId, @RequestBody UpdatePaasAppScopeListVO arg) {
        paaSAppDao.updatePaaSAppScopeList(arg.getTenantId(), arg.getAppId(), arg.getScopeList(), arg.getScopeListForCross());
        return new UpdatePaaSAppScopeListResult();
    }

    @RequestMapping(value = "/getAppListForTransLate", method = RequestMethod.POST)
    @Override
    public QueryPaasAppResult getAppListForTransLate(@HeaderParam("x-fs-ei") String tenantId, @RequestBody QueryPaasAppArg arg) {
        List<PaasAppRestVO> paasAppRestVOList = new ArrayList<>();
        QueryPaasAppResult queryPaasAppResult = QueryPaasAppResult.builder().paasAppRestVOList(paasAppRestVOList).build();

        UserInfo userInfo = new UserInfo();
        userInfo.setEnterpriseId(arg.getEnterpriseId());
        userInfo.setEmployeeId(arg.getEmployeeId());
        userInfo.setEnterpriseAccount(eieaConverter.enterpriseIdToAccount(arg.getEnterpriseId()));
        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setLocale(Objects.isNull(arg.getLocale()) ? Locale.CHINA : arg.getLocale());
        List<AppItemVO> appItemList = Lists.newArrayList();     // 查出来所有应用的基础信息
        if (Lists.newArrayList("platApp", "customApp").contains(arg.getApplicationType())) {
            boolean hasPermission = roleService.checkHasPermissionByCode(arg.getEnterpriseId(), arg.getEmployeeId(), PAAS_APP_FUNCTION);
            appItemList = paaSAppService.getAppItemList(arg.getEnterpriseId(), eieaConverter.enterpriseIdToAccount(arg.getEnterpriseId()),
                    arg.getEmployeeId(), hasPermission, clientInfo.getLocale(), true, false);
        } else if ("linkApp".equals(arg.getApplicationType())) {
            GetlinkAppListArg getlinkAppListArg = new GetlinkAppListArg();
            getlinkAppListArg.setTenantId(arg.getEnterpriseId());
            // apptype(com.facishare.webpage.customer.constant.AppTypeEnum) 传空就是查所有互联和自定义互联应用
            appItemList = paaSAppService.getLinkAppItemVOList(getlinkAppListArg, clientInfo.getLocale());
        }

        //查询所有预置应用
        if (CollectionUtils.isEmpty(appItemList)) {
            return queryPaasAppResult;
        }
        List<String> appIds = appItemList.stream().map(AppItemVO::getAppId).collect(Collectors.toList());
        Map<String, List<PageTemplate>> appTemplateMap = new HashMap<>();
        Map<String, List<PageTemplate>> webTemplateMap = new HashMap<>();
        Map<String, Map<String, String>> footLayoutTemplateMap = new HashMap<>();
        if (APP_NAME.equals(arg.getQueryType()) || ENTRY_NAME.equals(arg.getQueryType())) {
            //过滤掉应用中心的预置应用,与剑宏确认，互联应用不用走该逻辑
            if ("platApp".equals(arg.getApplicationType())) {
                //过滤掉应用中心的本人非管理员的自建应用
                appItemList = appItemList.stream()
                        .filter(x -> AppTypeEnum.BASE_V2_APP.getAppType() != x.getAppType())
                        .filter(x -> AppTypeEnum.CUSTOMER_APP.getAppType() != x.getAppType())
                        .collect(Collectors.toList());
                if (!checkService.checkGoNewCRM(arg.getEnterpriseId())) {
                    //老版crm不能修改名称
                    appItemList = appItemList.stream().filter(x -> !appMenuConfig.getCrmAppId().equals(x.getAppId())).collect(Collectors.toList());
                }
            } else if ("customApp".equals(arg.getApplicationType())) {
                appItemList = appItemList.stream()
                        .filter(x -> AppTypeEnum.CUSTOMER_APP.getAppType() == x.getAppType())
                        .collect(Collectors.toList());
            }
            //TODO 暂时，没有批量查询是否应用管理员的接口，先不做
        } else if (WEB_TEMPLATE_NAME.equals(arg.getQueryType())) {  // web布局
            List<PageTemplate> webPageTemplates = findSystemPageTemples(arg.getEnterpriseId(), TempleType.WEB, appIds);
            webTemplateMap = webPageTemplates.stream().filter(x -> StringUtils.isNotEmpty(x.getWebPageId())).collect(Collectors.groupingBy(PageTemplate::getAppId));
        } else if (APP_TEMPLATE_NAME.equals(arg.getQueryType())) {
            List<PageTemplate> appPageTemplates = findSystemPageTemples(arg.getEnterpriseId(), TempleType.APP, appIds);
            appTemplateMap = appPageTemplates.stream().collect(Collectors.groupingBy(PageTemplate::getAppId));
        } else if (WEB_TEMPLATE_WIDGET_NAME.equals(arg.getQueryType()) || MENU_GROUP_NAME.equals(arg.getQueryType()) || TOOL_BAR_TOOL_NAME.equals(arg.getQueryType())
                || SEARCH_TYPE_WEB_LABEL_NAME.equals(arg.getQueryType()) || SEARCH_TYPE_WEB_TAB_NAME.equals(arg.getQueryType())) {
            List<PageTemplate> webPageTemplates = Lists.newArrayList();

            if (MENU_GROUP_NAME.equals(arg.getQueryType()) || TOOL_BAR_TOOL_NAME.equals(arg.getQueryType())) {
                //获取所有的平台应用id(互联应用+平台应用)
                List<String> appIdList = appItemList.stream()
                        .filter(x -> Lists.newArrayList(AppTypeEnum.BASE_APP.getAppType(), AppTypeEnum.PAAS_APP.getAppType(),
                                AppTypeEnum.BASE_V2_APP.getAppType(), AppTypeEnum.LINK_APP.getAppType(), AppTypeEnum.CUSTOMER_LINK_APP.getAppType()).contains(x.getAppType()))
                        .map(AppItemVO::getAppId).collect(Collectors.toList());
                for (String x : appIdList) {
                    //todo 梳理查PageTemplate逻辑，将循环查改为批量查
                    webPageTemplates.addAll(tenantPageTempleBaseService.getCustomerPageTemples(arg.getEnterpriseId(), x, TempleType.WEB));
                }

            } else {
                webPageTemplates = findSystemPageTemples(arg.getEnterpriseId(), TempleType.WEB, appIds);
            }
            webTemplateMap = webPageTemplates.stream().filter(x -> StringUtils.isNotEmpty(x.getWebPageId())).collect(Collectors.groupingBy(PageTemplate::getAppId));
        } else if (APP_TEMPLATE_FOOT_MENU_NAME.equals(arg.getQueryType())
                || SEARCH_TYPE_APP_LABEL_NAME.equals(arg.getQueryType()) || SEARCH_TYPE_APP_MENU_NAME.equals(arg.getQueryType())
                || SEARCH_TYPE_APP_TEMPLATE_WIDGET_NAME.equals(arg.getQueryType()) || SEARCH_TYPE_APP_TAB_NAME.equals(arg.getQueryType())) {
            List<PageTemplate> appPageTemplates = findSystemPageTemples(arg.getEnterpriseId(), TempleType.APP, appIds);
            List<String> appPageIds = appPageTemplates.stream().map(x -> x.getAppPageId()).collect(Collectors.toList());
            BatchGetPageTemplate.Arg batchGetPageTemplate = new BatchGetPageTemplate.Arg();
            batchGetPageTemplate.setIds(appPageIds);
            batchGetPageTemplate.setEnterpriseId(arg.getEnterpriseId());
            BatchGetPageTemplate.Result result = pageTemplateService.batchGetPageTemplateByIds(batchGetPageTemplate);
            if (null != result && CollectionUtils.isNotEmpty(result.getPageTemplates())) {
                List<com.fxiaoke.api.model.PageTemplate> appPages = result.getPageTemplates().stream().filter(x -> 1 == x.getLayoutType()).collect(Collectors.toList());
                List<String> footLayoutAppPageIds = appPages.stream().map(x -> x.getPageTemplateId()).collect(Collectors.toList());
                Map<String, List<PageTemplate>> map = appPageTemplates.stream().filter(x -> footLayoutAppPageIds.contains(x.getAppPageId())).collect(Collectors.groupingBy(pageTemplate -> pageTemplate.getAppId()));
                map.forEach((x, y) -> {
                    Map<String, String> map1 = y.stream().collect(Collectors.toMap(z -> z.getName(), z -> z.getAppPageId(), (o1, o2) -> o1));
                    footLayoutTemplateMap.put(x, map1);
                    // Map<String, Map<String, String>>
                    // "appId": ["pageTemplateName": "appPageId",]
                });
            }
            webTemplateMap = appPageTemplates.stream().filter(x -> StringUtils.isNotEmpty(x.getAppPageId())).collect(Collectors.groupingBy(PageTemplate::getAppId));
        }
        for (AppItemVO item : appItemList) {
            TranslateI18nUtils.PreAndDefaultTranslateKey translateKey = TranslateI18nUtils.getAppNamePreTranslateKey(item.getAppId());
            String customKey = TranslateI18nUtils.getAppNametranslateKey(item.getAppId());
            I18nTrans.TransArg transArg = TranslateI18nUtils.formatTransKey(arg.getEnterpriseId(), translateKey.convertToTransKeyInfo(customKey));
            List<String> preKeyList = com.facishare.webpage.customer.core.util.CollectionUtils.nullToEmpty(transArg.getPreKeyList());
            preKeyList.addAll(com.facishare.webpage.customer.core.util.CollectionUtils.nullToEmpty(item.getNamePreKeyList()));
            paasAppRestVOList.add(PaasAppRestVO.builder()
                    .appId(item.getAppId())
                    .name(item.getName())
                    .appIntroduce(item.getDescription())
                    .appIntroduceTranslateKey(TranslateI18nUtils.getAppIntroduceTranslateKey(item.getAppId()))
                    .preAppIntroduceTranslateKeys(item.getDescPreKeyList())
                    .icon(item.getIcon())
                    .status(item.getStatus())
                    .appType(item.getAppType())
                    .hasAppView(appIdListConfig.isPresetAppId(item.getAppId()) || CollectionUtils.isNotEmpty(appTemplateMap.get(item.getAppId())))
                    .hasWebView(appIdListConfig.isPresetAppId(item.getAppId()) || CollectionUtils.isNotEmpty(webTemplateMap.get(item.getAppId())))
                    .webTemplateMap(null == webTemplateMap.get(item.getAppId()) || APP_TEMPLATE_FOOT_MENU_NAME.equals(arg.getQueryType()) ?
                            new HashMap<>() :
                            MENU_GROUP_NAME.equals(arg.getQueryType()) || TOOL_BAR_TOOL_NAME.equals(arg.getQueryType()) ?
                                    webTemplateMap.get(item.getAppId()).stream().collect(Collectors.toMap(PageTemplate::getName, PageTemplate::getTempleId, (o1, o2) -> o1)) :
                                    SEARCH_TYPE_APP_LABEL_NAME.equals(arg.getQueryType()) || SEARCH_TYPE_APP_MENU_NAME.equals(arg.getQueryType())
                                            || SEARCH_TYPE_APP_TEMPLATE_WIDGET_NAME.equals(arg.getQueryType()) || SEARCH_TYPE_APP_TAB_NAME.equals(arg.getQueryType()) ?
                                            webTemplateMap.get(item.getAppId()).stream().collect(Collectors.toMap(PageTemplate::getName, PageTemplate::getAppPageId, (o1, o2) -> o1)) :
                                            webTemplateMap.get(item.getAppId()).stream().collect(Collectors.toMap(PageTemplate::getName, PageTemplate::getWebPageId, (o1, o2) -> o1)))
                    .footLayoutTemplateMap(footLayoutTemplateMap.get(item.getAppId()))
                    .translateKey(transArg.getCustomKey())
                    .defaultTranslateKey(translateKey.getDefaultTranslateKey())
                    .preTranslateKeys(preKeyList)
                    .jumpUrl(PaaSAppUtil.buildJumpUrl(item.getAppId()))
                    .build());
        }
        return queryPaasAppResult;
    }

    private I18nTrans.TransArg buildTransKey(String customKey, TranslateI18nUtils.PreAndDefaultTranslateKey translateKey) {
        return I18nTrans.TransArg.builder()
                .customKey(customKey)
                .preKeyList(translateKey.getPreTranslateKeys())
                .build();
    }

    private List<PageTemplate> findSystemPageTemples(int tenantId, String type, List<String> appIds) {
        List<PageTemplate> systemPageTemples = Lists.newArrayList();
        for (String x : appIds) {
            //todo 梳理查PageTemplate逻辑，将循环查改为批量查
            systemPageTemples.addAll(tenantPageTempleBaseService.getCustomerPageTemples(tenantId, x, type));
        }
        return systemPageTemples;
    }

}