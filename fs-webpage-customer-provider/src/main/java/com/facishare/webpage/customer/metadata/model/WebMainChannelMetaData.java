package com.facishare.webpage.customer.metadata.model;

import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * Created by <PERSON><PERSON><PERSON> on 2020/11/19
 */
@Data
@Builder
public class WebMainChannelMetaData {
    @NotNull
    private String appId;

    private String openAppId;

    private int appType;

    private String name;

    private String upEnterpriseAccount;

    private String upTenantName;

    private String description;

    private String url;

    private String iconIndex;

    private String icon;

    private String crossName;
    /**
     * 上传图片类型
     * 如果没有上传图片不展示
     */
    private String iconType;

    private Boolean showOwnerIconFlag= false;

    private String appTypeName;

    private Boolean preLinkAppCustomIcon;

}
