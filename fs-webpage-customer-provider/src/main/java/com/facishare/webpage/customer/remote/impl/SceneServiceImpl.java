package com.facishare.webpage.customer.remote.impl;

import com.alibaba.fastjson.JSONObject;
import com.facishare.qixin.objgroup.common.service.utils.PaasMetaUtils;
import com.facishare.webpage.customer.common.resource.UdObjRestResource;
import com.facishare.webpage.customer.common.resource.TransScenesResource;
import com.facishare.webpage.customer.common.resource.model.arg.BatchGetSceneArg;
import com.facishare.webpage.customer.common.resource.model.arg.FindOuterTenantSceneListArg;
import com.facishare.webpage.customer.common.resource.model.result.BatchGetSceneResult;
import com.facishare.webpage.customer.common.resource.model.result.FindOuterTenantSceneListResult;
import com.facishare.webpage.customer.remote.SceneService;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by zhangyu on 2020/9/11
 */
@Component
public class SceneServiceImpl implements SceneService {

    public static final Logger logger = LoggerFactory.getLogger(SceneServiceImpl.class);

    @Resource
    private TransScenesResource transScenesResource;
    @Resource
    private UdObjRestResource udObjRestResource;

    @Override
    public List<BatchGetSceneResult.SceneResult> batchGetSceneName(int tenantId, List<BatchGetSceneArg.SceneArg> sceneArgList, Locale locale) {
        if (CollectionUtils.isEmpty(sceneArgList)) {
            return Lists.newArrayList();
        }
        try {
            //给sceneArgList去重
            sceneArgList = sceneArgList.stream().collect(Collectors.toMap(x -> (x.getApiName() + x.getSearchApiName()), x -> x, (x, y) -> x)).values().stream().collect(Collectors.toList());
            //重新设置TraceContext多语
            TraceContext.get().setLocale(StringUtils.isNotEmpty(locale.toLanguageTag()) ? locale.toLanguageTag() : TraceContext.get().getLocale());
            Map<String, String> headers = PaasMetaUtils.getHeaders(String.valueOf(tenantId), locale);
            BatchGetSceneArg batchGetSceneArg = new BatchGetSceneArg();
            batchGetSceneArg.setSceneInfos(sceneArgList);
            BatchGetSceneResult batchGetSceneResult = transScenesResource.batchGetScene(headers, batchGetSceneArg);
            return batchGetSceneResult.getTransResults();
        } catch (Exception e) {
            logger.error("batchGetSceneName error by tenantId : {}, sceneArgList : {}, locale : {}", tenantId, sceneArgList, locale, e);
        }
        return Lists.newArrayList();
    }

    @Override
    public JSONObject findOuterTenantSceneList(int tenantId,
                                               String objectApiName,
                                               Locale locale) {
        try {
            FindOuterTenantSceneListArg arg =
                    FindOuterTenantSceneListArg.builder().describeApiName(objectApiName).build();
            FindOuterTenantSceneListResult result = udObjRestResource.findOuterTenantSceneList(
                    PaasMetaUtils.getHeaders(String.valueOf(tenantId), locale), arg);
            if (result.getData() == null || CollectionUtils.isEmpty(result.getData().getScenes())) {
                return null;
            }
            List<JSONObject> sceneList = result.getData().getScenes().stream().map(x -> {
                JSONObject scene = new JSONObject();
                scene.put("_id", x.getString("id"));
                scene.put("api_name", x.getString("api_name"));
                scene.put("object_describe_api_name", x.getString("object_describe_api_name"));
                scene.put("label", x.getString("display_name"));
                scene.put("type", x.getString("type"));
                return scene;
            }).collect(Collectors.toList());

            JSONObject templates = new JSONObject();
            templates.put("templates", sceneList);
            return templates;
        } catch (Exception e) {
            logger.error("findOuterTenantSceneList error by tenantId:{}, objectApiName:{}, locale:{}",
                    tenantId, objectApiName, locale);
        }
        return null;
    }
}
