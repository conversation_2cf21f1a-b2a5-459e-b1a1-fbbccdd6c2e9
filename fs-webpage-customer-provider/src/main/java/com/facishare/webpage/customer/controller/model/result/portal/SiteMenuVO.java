package com.facishare.webpage.customer.controller.model.result.portal;

import com.alibaba.fastjson.JSONObject;
import com.facishare.webpage.customer.dao.entity.MenuDataEntity;
import com.facishare.webpage.customer.dao.entity.TenantMenuEntity;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2024/11/4.
 */
@Data
public class SiteMenuVO {
    private String id;
    private String apiName;
    private String name;
    private String description;
    private List<MenuItemVO> menuItems;
    private Integer creatorId;
    private Long createTime;
    private Integer updaterId;
    private Long updateTime;
    private int type = 0;

    @Data
    public static class MenuItemVO {
        private String appId;
        private String id;
        private String type;
        private String displayName;
        private String pid;
        private int number;
        private String metaType;
        private JSONObject data;
        private List<JSONObject> icons;

        public static MenuItemVO of(MenuDataEntity entity) {
            MenuItemVO vo = new MenuItemVO();
            vo.setAppId(entity.getAppId());
            vo.setId(entity.getApiName());
            vo.setType(entity.getType());
            vo.setDisplayName(entity.getName());
            vo.setPid(entity.getGroupApiName());
            vo.setNumber(entity.getOrderNumber());
            vo.setMetaType(entity.getMetaType());
            vo.setData(entity.getData());
            vo.setIcons(entity.getIcons());
            return vo;
        }
    }

    public static SiteMenuVO of(TenantMenuEntity entity) {
        SiteMenuVO vo = new SiteMenuVO();
        vo.setApiName(entity.getApiName());
        vo.setId(entity.getId());
        vo.setName(entity.getName());
        vo.setDescription(entity.getDescription());
        vo.setType(entity.getType());
        List<MenuItemVO> menuItemList = CollectionUtils.emptyIfNull(entity.getMenuDataEntities()).stream()
                .map(MenuItemVO::of)
                .collect(Collectors.toList());
        vo.setMenuItems(menuItemList);
        return vo;
    }
}
