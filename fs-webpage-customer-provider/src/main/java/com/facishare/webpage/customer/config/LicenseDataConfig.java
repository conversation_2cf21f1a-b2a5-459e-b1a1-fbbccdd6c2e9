package com.facishare.webpage.customer.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.webpage.customer.config.model.LicenseData;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IChangeListener;
import com.github.autoconf.api.IConfig;
import com.google.common.collect.Maps;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by zhangyu on 2020/3/7
 */
@Component
@Getter
public class LicenseDataConfig {

    private static Logger logger = LoggerFactory.getLogger(LicenseDataConfig.class);

    private static final String licenseConfig = "fs-bi-homepage-license";

    private Map<String, LicenseData> dataMap = Maps.newHashMap();

    @PostConstruct
    public void init() {
        IChangeListener listener = config -> loadConfig();
        ConfigFactory.getConfig(licenseConfig, listener, true);
    }

    public void loadConfig() {
        IConfig config = ConfigFactory.getConfig(licenseConfig);

        String licenseString = config.getString();
        Map<String, Object> map = JSON.parseObject(licenseString, Map.class);

        Map<String, LicenseData> licenseMap = new HashMap<>();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            String key = entry.getKey();
            LicenseData licenseData = JSONObject.parseObject(JSON.toJSONString(entry.getValue()), LicenseData.class);
            licenseMap.put(key, licenseData);
        }
        dataMap = licenseMap;

        logger.info("init LicenseDataConfig config dataMap:{}", dataMap);
    }

    public LicenseData getLicenseData(String productionVersion){
        LicenseData licenseData = dataMap.get(productionVersion);
        if (licenseData == null){
            return new LicenseData();
        }else {
            return licenseData;
        }
    }

    public static void main(String[] args) {
        System.setProperty("process.profile", "fstest");
        LicenseDataConfig licenseDataConfig = new LicenseDataConfig();
        licenseDataConfig.init();
        LicenseData strengthen_edition = licenseDataConfig.getLicenseData("strengthen_edition");
        System.out.println(strengthen_edition);
    }

}
