package com.facishare.webpage.customer.drop;

import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.constant.BizType;
import com.facishare.webpage.customer.api.constant.TempleType;
import com.facishare.webpage.customer.api.exception.WebPageException;
import com.facishare.webpage.customer.component.ComponentService;
import com.facishare.webpage.customer.config.AppMenuConfig;
import com.facishare.webpage.customer.core.business.ComponentListManager;
import com.facishare.webpage.customer.core.model.ComponentDto;
import com.facishare.webpage.customer.core.model.DropListItem;
import com.facishare.webpage.customer.designer.DropListService;
import com.facishare.webpage.customer.designer.model.DesignerAuthInfo;
import com.facishare.webpage.customer.designer.model.DropListType;
import com.facishare.webpage.customer.designer.model.StandGetDropList;
import com.facishare.webpage.customer.drop.model.GetUtilityBarDropList;
import com.facishare.webpage.customer.core.util.WebPageGraySwitch;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Yu
 */
@Component
public class UtilityBarDropListService extends DropListService<GetUtilityBarDropList.Arg, StandGetDropList.Result> {

    @Resource
    private ComponentListManager componentListManager;
    @Resource
    private ComponentService componentService;
    @Resource
    private AppMenuConfig appMenuConfig;


    @Override
    public DropListType getDropListType() {
        return DropListType.UTILITY_BAR;
    }

    @Override
    protected StandGetDropList.Result doGetDropList(GetUtilityBarDropList.Arg arg, DesignerAuthInfo designerAuthInfo) {
        String bizId = getBizId(arg);
        List<ComponentDto> componentDtos = componentListManager.getComponentDtoListByAppId(bizId + "-web-utility-bar");
        if (CollectionUtils.isEmpty(componentDtos)) {
            return StandGetDropList.Result.builder().build();
        }
        Integer enterpriseId = designerAuthInfo.getUserInfo().getEnterpriseId();
        Locale locale = designerAuthInfo.getLocale();
        //设置多语
        setComponentDtoLanguage(enterpriseId, componentDtos, locale);
        List<DropListItem> dropListItems = componentService.getGeneralAndTabsComponents(enterpriseId, arg.getBizType(), bizId, componentDtos, Maps.newHashMap());
        List<DropListItem> cusDropListItem = componentService.getCusComponents(enterpriseId, arg.getBizType(), bizId, null, TempleType.WEB);
        dropListItems.addAll(cusDropListItem);
        List<String> grayComponentByAppId = appMenuConfig.getGrayUtilityBarByAppId(bizId);
        dropListItems = dropListItems.stream().
                filter(componentDto -> !grayComponentByAppId.contains(componentDto.getId() + "_UtilityBar_" + bizId) ||
                        (grayComponentByAppId.contains(componentDto.getId() + "_UtilityBar_" + bizId) &&
                                WebPageGraySwitch.isAllowComponentByBusiness(componentDto.getId() + "_UtilityBar_" + bizId, enterpriseId))).
                collect(Collectors.toList());
        dropListItems = componentService.filterNoChildDropList(dropListItems);

        return StandGetDropList.Result.builder().dropListItemList(dropListItems).build();
    }

    @Override
    protected void before(GetUtilityBarDropList.Arg arg) {
        super.before(arg);
        if (arg.getBizType() == null || StringUtils.isEmpty(arg.getBizId())) {
            throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
        }
    }

    @Override
    protected StandGetDropList.Result after(GetUtilityBarDropList.Arg arg, StandGetDropList.Result result) {
        return super.after(arg, result);
    }

    @Override
    protected String getBizId(GetUtilityBarDropList.Arg arg) {
        if (BizType.PAAS.getType() == arg.getBizType()) {
            return BizType.PAAS.getDefaultAppId();
        }
        return arg.getBizId();
    }
}
