package com.facishare.webpage.customer.service;

import com.facishare.cep.plugin.model.OuterUserInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.webpage.customer.controller.model.arg.BatchGetCustomCountArg;
import com.facishare.webpage.customer.controller.model.arg.GetCustomCountArg;
import com.facishare.webpage.customer.controller.model.result.BatchGetCustomCountResult;
import com.facishare.webpage.customer.controller.model.result.GetCustomCountResult;

/**
 * Created by she<PERSON> on 19/9/25.
 */
public interface CustomCountService {

    GetCustomCountResult getCustomCount(UserInfo userInfo, OuterUserInfo outerUserInfo, GetCustomCountArg arg);

    BatchGetCustomCountResult batchGetCustomCount(UserInfo userInfo, OuterUserInfo outerUserInfo, BatchGetCustomCountArg arg);

}
