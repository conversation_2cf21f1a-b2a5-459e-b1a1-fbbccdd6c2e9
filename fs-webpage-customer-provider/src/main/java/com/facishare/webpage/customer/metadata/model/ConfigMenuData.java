package com.facishare.webpage.customer.metadata.model;

import com.facishare.webpage.customer.api.model.Scope;
import com.facishare.webpage.customer.api.model.core.Icon;
import com.facishare.webpage.customer.api.model.core.Menu;
import com.facishare.webpage.customer.api.model.core.Url;
import com.facishare.webpage.customer.constant.MenuType;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * Created by zhangyu on 2020/5/28
 */
@Data
public class ConfigMenuData implements MetaMenuData {

    private Menu menu;
    private String name;
    private String appId;
    private String target;
    private List<String> i18nKeyList;
    @Override
    public String getApiName() {
        return menu.getId();
    }

    @Override
    public String getAppId() {
        return appId;
    }

    @Override
    public String getObjectApiName() {
        return null;
    }

    @Override
    public String getObjectRecordTypeApiName() {
        return null;
    }

    @Override
    public boolean isActive() {
        return true;
    }

    @Override
    public String getName() {
        return StringUtils.isEmpty(name) ? menu.getName() : name;
    }

    @Override
    public Icon getIcon() {
        return menu.getIcon();
    }

    @Override
    public int getIconIndex() {
        return menu.getIconIndex();
    }

    @Override
    public Integer getIconSlot() {
        return null;
    }

    @Override
    public Url getUrl() {
        return menu.getUrl();
    }

    @Override
    public List<String> getDeviceTypes() {
        return menu.getDeviceTypes();
    }

    @Override
    public String getMenuType() {
        return MenuType.NO_OBJ;
    }

    @Override
    public List<String> getFunctions() {
        if (menu.getPersonPrivilege() == null || CollectionUtils.isEmpty(menu.getPersonPrivilege().getFunctionCode())){
            return Lists.newArrayList();
        }else {
            return menu.getPersonPrivilege().getFunctionCode();
        }
    }

    @Override
    public List<String> getRoleCodes() {
        if (menu.getPersonPrivilege() == null || CollectionUtils.isEmpty(menu.getPersonPrivilege().getRoleCodes())){
            return Lists.newArrayList();
        }else {
            return menu.getPersonPrivilege().getRoleCodes();
        }
    }

    @Override
    public List<Scope> getScopeList() {
        return Lists.newArrayList();
    }

    @Override
    public boolean hidden() {
        return menu.isHidden();
    }

    @Override
    public boolean customerMenuHidden() {
        return false;
    }

    @Override
    public boolean checkDetailObjectButton() {
        return false;
    }

    @Override
    public String getTarget() {
        return target;
    }

    @Override
    public String getFxIcon() {
        return "";
      /*  if (menu.getIcon().getFxIcon() != null) {
            return menu.getIcon().getFxIcon();
        }
        return "fx-icon-obj-app"+ (menu.getIconIndex() + 1);*/
    }

    @Override
    public List<String> getI18nKeyList() {
        return i18nKeyList;
    }

}
