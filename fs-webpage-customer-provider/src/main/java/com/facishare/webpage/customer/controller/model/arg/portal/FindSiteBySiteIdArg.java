package com.facishare.webpage.customer.controller.model.arg.portal;

import com.facishare.webpage.customer.api.constant.ClientType;
import org.apache.commons.lang3.StringUtils;

import lombok.Data;

/**
 * Created by zhouwr on 2024/11/6.
 */
@Data
public class FindSiteBySiteIdArg {
    private String siteId;
    private String clientType = ClientType.web.getValue();

    public void validAndFix() {
        clientType = StringUtils.firstNonBlank(clientType, ClientType.web.getValue());
    }
}
