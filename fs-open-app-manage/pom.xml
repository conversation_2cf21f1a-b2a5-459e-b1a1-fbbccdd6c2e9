<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.facishare.open</groupId>
		<artifactId>fs-open-app-center</artifactId>
		<version>1.0.0-SNAPSHOT</version>
	</parent>

	<artifactId>fs-open-app-manage</artifactId>
	<packaging>war</packaging>
	<dependencies>
		<dependency>
			<groupId>com.facishare</groupId>
			<artifactId>fs-uc-api</artifactId>
		</dependency>
		<!--mq-->
		<dependency>
			<groupId>com.fxiaoke</groupId>
			<artifactId>fs-rocketmq-support</artifactId>
		</dependency>

		<dependency>
			<groupId>com.facishare</groupId>
			<artifactId>fs-rest-proxy</artifactId>
			<version>6.3.1-SNAPSHOT</version>
			<exclusions>
				<exclusion>
					<groupId>com.facishare</groupId>
					<artifactId>fs-metadata-api</artifactId>
				</exclusion>

			</exclusions>
		</dependency>

		<!-- 应用中心Dubbo扩展 -->
		<dependency>
			<groupId>com.facishare.open</groupId>
			<artifactId>dubbox-rpc</artifactId>
		</dependency>
		<dependency>
			<groupId>com.facishare.open</groupId>
			<artifactId>fs-open-app-center-common-utils</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.facishare</groupId>
			<artifactId>fs-enterprise-id-account-converter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.facishare.open</groupId>
			<artifactId>fs-open-msg-auto-reply-api</artifactId>
		</dependency>
		<dependency>
			<groupId>com.facishare.open</groupId>
			<artifactId>fs-open-material-api</artifactId>
		</dependency>
		<dependency>
			<groupId>com.facishare.open</groupId>
			<artifactId>fs-open-support-center-api</artifactId>
		</dependency>
		<dependency>
			<groupId>com.facishare.open</groupId>
			<artifactId>fs-open-common-utils</artifactId>
		</dependency>
        <dependency>
            <groupId>com.fxiaoke.common</groupId>
            <artifactId>java-utils</artifactId>
			<version>${jutil.version}</version>
		</dependency>
        <!--开平消息api-->
		<dependency>
			<groupId>com.facishare.open</groupId>
			<artifactId>fs-broker-message-api</artifactId>
			<exclusions>
				<exclusion>
					<groupId>com.github.sgroschupf</groupId>
					<artifactId>zkclient</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<!--开平登录api-->
		<dependency>
			<groupId>com.facishare.open</groupId>
			<artifactId>fs-broker-login-api</artifactId>
			<exclusions>
				<exclusion>
					<groupId>com.github.sgroschupf</groupId>
					<artifactId>zkclient</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<!--网页轮询改造api-->
		<dependency>
			<groupId>com.facishare</groupId>
			<artifactId>fs-polling-api</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>commons-lang</artifactId>
					<groupId>commons-lang</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-logging</artifactId>
					<groupId>commons-logging</groupId>
				</exclusion>
				<exclusion>
					<artifactId>fs-cep-spring-plugin</artifactId>
					<groupId>com.facishare</groupId>
				</exclusion>
				<exclusion>
					<artifactId>fs-crm-rest-api</artifactId>
					<groupId>com.fxiaoke</groupId>
				</exclusion>
				<exclusion>
					<artifactId>fs-paas-app-api</artifactId>
					<groupId>com.facishare</groupId>
				</exclusion>
				<exclusion>
					<artifactId>fs-timezone-api</artifactId>
					<groupId>com.facishare</groupId>
				</exclusion>
				<exclusion>
					<artifactId>hamcrest-core</artifactId>
					<groupId>org.hamcrest</groupId>
				</exclusion>
				<exclusion>
					<artifactId>i18n-util</artifactId>
					<groupId>com.facishare</groupId>
				</exclusion>
				<exclusion>
					<artifactId>mongo-java-driver</artifactId>
					<groupId>org.mongodb</groupId>
				</exclusion>
				<exclusion>
					<artifactId>netty</artifactId>
					<groupId>io.netty</groupId>
				</exclusion>
				<exclusion>
					<artifactId>retrofit-spring</artifactId>
					<groupId>com.github.zhxing</groupId>
				</exclusion>
				<exclusion>
					<artifactId>retrofit-spring2</artifactId>
					<groupId>com.fxiaoke</groupId>
				</exclusion>
				<exclusion>
					<artifactId>snakeyaml</artifactId>
					<groupId>org.yaml</groupId>
				</exclusion>
				<exclusion>
					<artifactId>swagger-annotations</artifactId>
					<groupId>io.swagger</groupId>
				</exclusion>
				<exclusion>
					<groupId>com.fxiaoke.fs-user-extension</groupId>
					<artifactId>fs-user-extension-api</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>fs-rocketmq-support</artifactId>
					<groupId>com.fxiaoke</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<!--评论系统api-->
		<dependency>
			<groupId>com.facishare.appserver</groupId>
			<artifactId>fs-appserver-comment-api</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>commons-collections4</artifactId>
					<groupId>org.apache.commons</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-lang</artifactId>
					<groupId>commons-lang</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-logging</artifactId>
					<groupId>commons-logging</groupId>
				</exclusion>
				<exclusion>
					<artifactId>fs-cep-spring-plugin</artifactId>
					<groupId>com.facishare</groupId>
				</exclusion>
				<exclusion>
					<artifactId>fs-crm-rest-api</artifactId>
					<groupId>com.fxiaoke</groupId>
				</exclusion>
				<exclusion>
					<artifactId>fs-paas-app-api</artifactId>
					<groupId>com.facishare</groupId>
				</exclusion>
				<exclusion>
					<artifactId>fs-timezone-api</artifactId>
					<groupId>com.facishare</groupId>
				</exclusion>
				<exclusion>
					<artifactId>hamcrest-core</artifactId>
					<groupId>org.hamcrest</groupId>
				</exclusion>
				<exclusion>
					<artifactId>i18n-util</artifactId>
					<groupId>com.facishare</groupId>
				</exclusion>
				<exclusion>
					<artifactId>mongo-java-driver</artifactId>
					<groupId>org.mongodb</groupId>
				</exclusion>
				<exclusion>
					<artifactId>netty</artifactId>
					<groupId>io.netty</groupId>
				</exclusion>
				<exclusion>
					<artifactId>retrofit-spring</artifactId>
					<groupId>com.github.zhxing</groupId>
				</exclusion>
				<exclusion>
					<artifactId>retrofit-spring2</artifactId>
					<groupId>com.fxiaoke</groupId>
				</exclusion>
				<exclusion>
					<artifactId>snakeyaml</artifactId>
					<groupId>org.yaml</groupId>
				</exclusion>
				<exclusion>
					<artifactId>swagger-annotations</artifactId>
					<groupId>io.swagger</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<!--开平oauth-->
		<dependency>
			<groupId>com.facishare.open</groupId>
			<artifactId>fs-broker-oauth-api</artifactId>
		</dependency>
		<!--消息api-->
		<dependency>
			<groupId>com.facishare.open</groupId>
			<artifactId>fs-open-msg-api</artifactId>
		</dependency>

		<dependency>
			<groupId>com.facishare.open</groupId>
			<artifactId>fs-open-common-storage</artifactId>
		</dependency>
		<!--图片系统api-->
		<dependency>
			<groupId>com.facishare.open</groupId>
			<artifactId>fs-open-warehouse-api</artifactId>
		</dependency>
		<!--应用中心api-->
		<dependency>
			<groupId>com.facishare.open</groupId>
			<artifactId>fs-open-app-center-api</artifactId>
		</dependency>
		<dependency>
			<groupId>com.facishare.open</groupId>
			<artifactId>fs-open-intelligence-form-api</artifactId>
		</dependency>
		<!--oauth api-->
		<dependency>
			<groupId>com.facishare.open</groupId>
			<artifactId>fs-oauth-base-api</artifactId>
		</dependency>
		<!--结果集-->
        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>fs-open-common-result</artifactId>
        </dependency>

		<dependency>
			<groupId>com.facishare.open</groupId>
			<artifactId>fs-portal-api</artifactId>
		</dependency>

		<dependency>
			<groupId>com.facishare.open</groupId>
			<artifactId>app-access-control-api</artifactId>
		</dependency>
		<!--灰度组件 -->
		<dependency>
			<groupId>com.fxiaoke.common</groupId>
			<artifactId>gray-release</artifactId>
		</dependency>

		<dependency>
			<groupId>com.facishare</groupId>
			<artifactId>fs-qixin-api</artifactId>
			<version>0.0.8-SNAPSHOT</version>
			<exclusions>
				<exclusion>
					<artifactId>mongo-java-driver</artifactId>
					<groupId>org.mongodb</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<!--support by colin-lee-->
		<dependency>
			<groupId>com.github.colin-lee</groupId>
			<artifactId>core-filter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.github.colin-lee</groupId>
			<artifactId>rpc-trace</artifactId>
		</dependency>
		<dependency>
			<groupId>com.github.colin-lee</groupId>
			<artifactId>mybatis-spring-support</artifactId>
		</dependency>
		<dependency>
			<groupId>com.github.colin-lee</groupId>
			<artifactId>jedis-spring-support</artifactId>
		</dependency>
		<dependency>
			<groupId>com.github.colin-lee</groupId>
			<artifactId>mongo-spring-support</artifactId>
		</dependency>
		<dependency>
			<groupId>com.github.colin-lee</groupId>
			<artifactId>config-core</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>commons-collections4</artifactId>
					<groupId>org.apache.commons</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.github.colin-lee</groupId>
			<artifactId>spring-support</artifactId>
		</dependency>
		<!--业务监控-->
		<dependency>
			<groupId>com.fxiaoke.common</groupId>
			<artifactId>metrics-oss</artifactId>
		</dependency>
		<dependency>
			<groupId>com.fxiaoke</groupId>
			<artifactId>logconfig-core</artifactId>
		</dependency>
		<!--redis-->
		<dependency>
			<groupId>redis.clients</groupId>
			<artifactId>jedis</artifactId>
		</dependency>
		<!--dubbo-->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>dubbo</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>commons-lang</artifactId>
					<groupId>commons-lang</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.facishare.open</groupId>
			<artifactId>fs-open-app-center-common</artifactId>
		</dependency>
		<!--java-->
		<dependency>
			<groupId>org.javassist</groupId>
			<artifactId>javassist</artifactId>
		</dependency>
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
			<scope>provided</scope>
		</dependency>
		<!--google-->
		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
		</dependency>
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
		</dependency>
		<!--spring-->
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-aspects</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-beans</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context-support</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-core</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-tx</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-webmvc</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-test</artifactId>
		</dependency>
		<!--commons-->
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
		</dependency>
		<dependency>
			<groupId>commons-collections</groupId>
			<artifactId>commons-collections</artifactId>
		</dependency>
		<dependency>
			<groupId>commons-fileupload</groupId>
			<artifactId>commons-fileupload</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpcore</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpmime</artifactId>
		</dependency>
		<!--for log-->
		<dependency>
			<groupId>ch.qos.logback</groupId>
			<artifactId>logback-classic</artifactId>
		</dependency>
		<dependency>
			<groupId>ch.qos.logback</groupId>
			<artifactId>logback-core</artifactId>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>jcl-over-slf4j</artifactId>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>jul-to-slf4j</artifactId>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>log4j-over-slf4j</artifactId>
		</dependency>
		<dependency>
			<groupId>com.facishare.open</groupId>
			<artifactId>fs-open-logback-support</artifactId>
		</dependency>
		<!--for unit test-->
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.h2database</groupId>
			<artifactId>h2</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-core</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.easymock</groupId>
			<artifactId>easymock</artifactId>
			<scope>test</scope>
		</dependency>
		<!--for csv file-->
		<dependency>
			<groupId>com.opencsv</groupId>
			<artifactId>opencsv</artifactId>
		</dependency>

		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
		</dependency>

	</dependencies>
	<build>
		<resources>
			<resource>
				<directory>src/main/resources</directory>
				<filtering>true</filtering>
			</resource>
			<resource>
				<directory>src/main/resources/spring</directory>
				<filtering>true</filtering>
			</resource>
		</resources>
		<sourceDirectory>src/main/java</sourceDirectory>

		<plugins>
			<plugin>
				<groupId>org.eclipse.jetty</groupId>
				<artifactId>jetty-maven-plugin</artifactId>
			</plugin>
		</plugins>
	</build>

</project>
