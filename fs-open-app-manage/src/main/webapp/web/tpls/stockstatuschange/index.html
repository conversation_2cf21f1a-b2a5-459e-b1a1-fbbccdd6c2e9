<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="nimo">
    <title>纷享开放平台</title>
    <link rel="shortcut icon" href="/web/favicon.ico">
    <!-- Bootstrap Core CSS -->
    <link href="/web/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Custom Fonts -->
    <link href="/web/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css">
    <!-- Common CSS -->
    <link href="/web/assets/style/common.css" rel="stylesheet">
    <!-- Base CSS -->
    <link href="/web/assets/style/base.css" rel="stylesheet">
    <style>
        .breadcrumb {
            font-size: 18px;
        }

        #form-stockstatus .form-group .help-block {
            display: none;
        }
        #form-stockstatus .has-error .help-block {
            display: inline-block;
        }
        .stockinfo {
            margin-top: 20px;
            font-size: 18px;
        }
    </style>
    <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
        <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
        <script src="https://oss.maxcdn.com/libs/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>

<body>
    <div class="g-doc">
        <nav class="g-hd navbar navbar-fixed-top">
            <div class="container-fluid">
                <div class="navbar-header">
                    <a class="h-brand" href="/web/tpls/welcome/index.html">纷享开放平台</a>
                </div>
                <div class="collapse navbar-collapse">
                    <ul class="h-user nav navbar-nav navbar-right">
                        <li class="h-dropdown">
                            <div class="user-name"></div>
                        </li>
                        <li class="h-dropdown">|</li>
                        <li class="h-dropdown">
                            <a href="javascript:;" class="h-logout js-logout">退出登录</a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
        <div class="g-bd">
            <div id="sideMenu" class="g-sd">

            </div>
            <div class="g-content">
                <div id="page-wrapper">
                    <div class="container-fluid operation-container">
                        <h3 class="breadcrumb">库存开关</h3>
                        <div class="m-tips warning bg-warning">
                                <h4>注意事项：</h4>
                                <p>库存开关修改只修改配置，不做对应业务逻辑。</p>
                            </div>
                        <div class="row operation-action">
                            <div class="col-lg-10">
                                <form id="form-stockstatus">
                                    <div class="form-inline form-group">
                                        <label for="tenantId">企业ID：</label>
                                        <input class="form-control" type="text" name="tenantId" id="tenantId" placeholder="企业ID">
                                        <small class="help-block">请输入企业ID</small>
                                    </div>
                                    <div class="form-inline form-group">
                                        <label for="stockSwitchType">开&nbsp;&nbsp;&nbsp;&nbsp;关：</label>
                                        <select name="stockSwitchType" id="stockSwitchType" class="form-control">
                                            <option value="1">未开启</option>
                                            <option value="2">开启</option>
                                            <option value="3">停用</option>
                                        </select>
                                    </div>
                                    <div class="form-inline form-group">
                                        <button class="btn btn-primary btn-submit j-query" data-loading-text="查询中..." type="submit">查询</button>
                                        <button class="btn btn-primary btn-submit j-modify" data-loading-text="发送中..." type="submit">修改</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                        <div class="row j-stockinfo">

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script id="tpl-stock" type="text/template">
        <ul class="stockinfo">
            <li>发货单<%= obj.isDeliveryNoteEnable ? '已' : '未' %>开启</li>
            <li>纷享库存<%= obj.isStockEnable ? '已' : '未' %>开启</li>
            <li>纷享库存开关：<%= obj.typeMap[obj.stockSwitchType] %> </li>
            <li>ERP库存<%= obj.isErpStockEnable ? '已' : '未' %>开启</li>
            <li>批次与序列号<%= obj.isBatchSNEnable ? '已' : '未' %>开启</li>
            <li>纷享库存支持ERP<%= obj.isForErp ? '已' : '未' %>开启</li>
        </ul>
    </script>

    <script src="/web/assets/libs/seajs/2.2.0/sea.js"></script>
    <script src="/web/assets/js/common.js"></script>
    <script>
        seajs.use('/web/tpls/stockstatuschange/main.js');
    </script>
</body>

</html>