define("arale/dialog/1.0.2/dialog",["$","arale/overlay/1.0.1/overlay","arale/position/1.0.0/position","arale/iframe-shim/1.0.1/iframe-shim","arale/widget/1.0.3/widget","arale/base/1.0.1/base","arale/class/1.0.0/class","arale/events/1.0.0/events","arale/overlay/1.0.1/mask","arale/widget/1.0.3/templatable","gallery/handlebars/1.0.0/handlebars","./dialog-tpl.js"],function(e,t,i){function s(e){null==e.attr("tabindex")&&e.attr("tabindex","-1")}function a(e){var t=e[0].contentWindow.document;return t.body.scrollHeight&&t.documentElement.scrollHeight?Math.min(t.body.scrollHeight,t.documentElement.scrollHeight):t.documentElement.scrollHeight?t.documentElement.scrollHeight:t.body.scrollHeight?t.body.scrollHeight:void 0}var n=e("$"),r=e("arale/overlay/1.0.1/overlay"),l=e("arale/overlay/1.0.1/mask"),h=e("arale/events/1.0.0/events"),o=e("arale/widget/1.0.3/templatable"),c=".dialog",f="300px",d=r.extend({Implements:o,attrs:{template:e("./dialog-tpl"),trigger:{value:null,getter:function(e){return n(e)}},classPrefix:"ui-dialog",content:{value:"",setter:function(e){return/^(https?:\/\/|\/|\.\/|\.\.\/)/.test(e)&&(this._type="iframe"),e}},hasMask:!0,closeTpl:"×",width:500,height:null,effect:"none",zIndex:999,autoFit:!0,align:{selfXY:["50%","50%"],baseXY:["50%","50%"]}},parseElement:function(){this.model={classPrefix:this.get("classPrefix")},d.superclass.parseElement.call(this),this.contentElement=this.$("[data-role=content]"),this.contentElement.css({background:"#fff",height:"100%",zoom:1}),this.$("[data-role=close]").hide()},events:{"click [data-role=close]":function(e){e.preventDefault(),this.hide()}},show:function(){return"iframe"===this._type&&(!this.get("height")&&this.element.css("height",f),this._showIframe()),d.superclass.show.call(this),this},hide:function(){return"iframe"===this._type&&this.iframe&&(this.iframe.attr({src:"javascript:'';"}),this.iframe.remove(),this.iframe=null),d.superclass.hide.call(this),clearInterval(this._interval),delete this._interval,this},destroy:function(){return this.get("trigger")&&this.get("trigger").off("click"+c+this.cid),n(document).off("keyup."+c+this.cid),this.element.remove(),this.get("hasMask")&&l.hide(),clearInterval(this._interval),d.superclass.destroy.call(this)},setup:function(){d.superclass.setup.call(this),this._setupTrigger(),this._setupMask(),this._setupKeyEvents(),this._setupFocus(),s(this.element),s(this.get("trigger")),this.activeTrigger=this.get("trigger").eq(0)},_onRenderContent:function(e){if("iframe"!==this._type){var t;try{t=n(e)}catch(i){t=[]}t[0]?this.contentElement.empty().append(t):this.contentElement.empty().html(e)}},_onRenderCloseTpl:function(e){""===e?this.$("[data-role=close]").html(e).hide():this.$("[data-role=close]").html(e).show()},_onRenderVisible:function(e){e?"fade"===this.get("effect")?this.element.fadeIn(300):this.element.show():this.element.hide()},_setupTrigger:function(){var e=this;this.get("trigger").on("click"+c+this.cid,function(t){t.preventDefault(),e.activeTrigger=n(this),e.show()})},_setupMask:function(){var e,t=this.get("hasMask"),i=parseInt(this.get("zIndex"),10);this.before("show",function(){t&&(e=l.get("zIndex"),l.set("zIndex",i-1).show())}),this.after("hide",function(){t&&l.set("zIndex",e).hide()})},_setupFocus:function(){this.after("show",function(){this.element.focus()}),this.after("hide",function(){this.activeTrigger&&this.activeTrigger.focus()})},_setupKeyEvents:function(){var e=this;n(document).on("keyup."+c+this.cid,function(t){27===t.keyCode&&e.get("visible")&&e.hide()})},_showIframe:function(){var e=this;this.iframe||this._createIframe(),this.iframe.attr({src:this._fixUrl(),name:"dialog-iframe"+(new Date).getTime()}),this.iframe.one("load",function(){e.get("visible")&&(e.get("autoFit")&&(clearInterval(e._interval),e._interval=setInterval(function(){e._syncHeight()},300)),e._syncHeight(),e._setPosition(),e.trigger("complete:show"))})},_fixUrl:function(){var e=this.get("content").match(/([^?#]*)(\?[^#]*)?(#.*)?/);return e.shift(),e[1]=(e[1]&&"?"!==e[1]?e[1]+"&":"?")+"t="+(new Date).getTime(),e.join("")},_createIframe:function(){var e=this;this.iframe=n("<iframe>",{src:"javascript:'';",scrolling:"no",frameborder:"no",allowTransparency:"true",css:{border:"none",width:"100%",display:"block",height:"100%",overflow:"hidden"}}).appendTo(this.contentElement),h.mixTo(this.iframe[0]),this.iframe[0].on("close",function(){e.hide()})},_syncHeight:function(){var e;if(this.get("height"))clearInterval(this._interval),delete this._interval;else{try{this._errCount=0,e=a(this.iframe)+"px"}catch(t){this._errCount=(this._errCount||0)+1,this._errCount>=6&&(e=f,clearInterval(this._interval),delete this._interval)}this.element.css("height",e),this.element[0].className=this.element[0].className}}});i.exports=d}),define("arale/dialog/1.0.2/dialog-tpl",["gallery/handlebars/1.0.0/handlebars"],function(e,t,i){var s=e("gallery/handlebars/1.0.0/handlebars");(function(){var e=s.template;s.templates=s.templates||{},i.exports=e(function(e,t,i,s,a){this.compilerInfo=[2,">= 1.0.0-rc.3"],i=i||e.helpers,a=a||{};var n,r="",l="function",h=this.escapeExpression;return r+='<div class="',(n=i.classPrefix)?n=n.call(t,{hash:{},data:a}):(n=t.classPrefix,n=typeof n===l?n.apply(t):n),r+=h(n)+'">\n    <div class="',(n=i.classPrefix)?n=n.call(t,{hash:{},data:a}):(n=t.classPrefix,n=typeof n===l?n.apply(t):n),r+=h(n)+'-close" title="关闭本框" data-role="close"></div>\n    <div class="',(n=i.classPrefix)?n=n.call(t,{hash:{},data:a}):(n=t.classPrefix,n=typeof n===l?n.apply(t):n),r+=h(n)+'-content" data-role="content"></div>\n</div>\n'})})()});
