package com.facishare.open.manage.ajax.result;

import com.facishare.open.manage.model.CertifyRecordVO;
import com.facishare.open.support.base.Pager;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * created by dailf on 2018/4/26
 *
 * <AUTHOR>
 */
@Data
public class CertifyRecordType {
    @Data
    public static class GetCertifyRecordByPageResult extends BaseCrmResult {
        private ResultBody<CertifyRecordVO> result;


    }

    @Data
    public static class GetCertifyRecordByPageArg {
        private Integer pageSize;
        private Integer currentPage;
        private Integer userObj;
        private Integer certifyType;
        private String certifyStatus;
        private Long startTime;
        private Long endTime;
    }

}
