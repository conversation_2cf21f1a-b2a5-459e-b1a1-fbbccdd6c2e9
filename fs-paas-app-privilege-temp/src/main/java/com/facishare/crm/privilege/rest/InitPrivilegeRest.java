package com.facishare.crm.privilege.rest;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.common.exception.CRMErrorCode;
import com.facishare.crm.common.exception.CrmCheckedException;
import com.facishare.crm.privilege.model.valueobject.CrmResult;
import com.facishare.crm.privilege.service.MigrateService;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.Example;
import io.swagger.annotations.ExampleProperty;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Response;

/**
 * Created by luxin on 2017/1/13.
 */
@Slf4j
@Component
@Path("/initPrivilege")
public class InitPrivilegeRest {
    @Autowired
    private MigrateService migrateService;

    @POST
    @Produces({"application/json"})
    @io.swagger.annotations.ApiOperation(value = "initPrivilege", notes = "初始化企业的权限数据", response = CrmResult.class, tags = {"init"})
    public Response initPrivilege(@ApiParam(
            examples =@Example(value = @ExampleProperty(mediaType = "json", value = "{\"tenantIds\":[\"7\",\"53544\"]}"))
            ,required = true)  String body) {
        JSONObject jsonObject = JSON.parseObject(body);
        JSONArray tenantIdArr = jsonObject.getJSONArray("tenantIds");
        String userId = jsonObject.getString("userId");

        CrmResult result = new CrmResult();
        for (Object tenantIdObj : tenantIdArr) {
            String tenantId = String.valueOf(tenantIdObj);
            if (StringUtils.isBlank(tenantId)) {
                log.error("parameter is null or blank! tenantId -{}", tenantId);
                result.setCode(CRMErrorCode.PARAMETER_IS_WRONG.getCode());
                result.setMsg(CRMErrorCode.PARAMETER_IS_WRONG.getMessage());
                return Response.ok().entity("parameter is null or blank! tenantId -" + tenantId).build();
            }
            try {
                migrateService.initPrivilegeData(tenantId,userId);
            } catch (CrmCheckedException e) {
                log.error("initPrivilege failed! tenantId is -{}", tenantId, e);
                result.setCode(CRMErrorCode.PAAS_ERROR.getCode());
                result.setMsg(CRMErrorCode.PAAS_ERROR.getMessage() + e.getMessage());
                return Response.ok().entity(CRMErrorCode.PAAS_ERROR.getMessage()).build();
            }
        }
        result.setCode(CrmResult.SUCCESS);
        result.setMsg(CrmResult.SUCCESS_MSG);
        return Response.ok().entity(result).build();
    }


}
