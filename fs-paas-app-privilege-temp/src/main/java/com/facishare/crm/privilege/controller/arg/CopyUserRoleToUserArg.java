package com.facishare.crm.privilege.controller.arg;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * Created by lei on 12/15/16.
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class CopyUserRoleToUserArg {
    private String userIdOld;
    @Deprecated
    private String userIds;

    private List<Integer> employeeIds;
    private List<Integer> departmentIds;

    private String majorRoleCode;
}
