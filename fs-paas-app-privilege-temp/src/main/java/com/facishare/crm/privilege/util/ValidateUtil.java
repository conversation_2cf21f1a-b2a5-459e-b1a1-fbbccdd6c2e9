package com.facishare.crm.privilege.util;

import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import javax.ws.rs.core.Response;
import java.util.List;
import java.util.Map;

/**
 * Created by luxin on 2017/7/3.
 */
public class ValidateUtil {

  public static final String SUCCESS = "success";
  public static final String SUCCESS_CODE = "0";

  public static boolean isValidParam(List<String> list, String... strings) {
    if (CollectionUtils.isEmpty(list)) return Boolean.FALSE;
    for (String string : strings) {
      if (StringUtils.isBlank(string)) return Boolean.FALSE;
    }
    return Boolean.TRUE;
  }


  public static boolean isValidParam(String... strings) {
    for (String string : strings) {
      if (StringUtils.isBlank(string)) return Boolean.FALSE;
    }
    return Boolean.TRUE;
  }


  public static Response getResponse(Integer strCode, String message) {
    Map<String, String> tmp = Maps.newHashMap();
    tmp.put("code", strCode.toString());
    tmp.put("msg", message);

    return Response.ok().entity(tmp).build();
  }


  public static Response getSuccessResponse() {
    Map<String, String> tmp = Maps.newHashMap();
    tmp.put("code", SUCCESS_CODE);
    tmp.put("msg", SUCCESS);
    return Response.ok().entity(tmp).build();
  }


  public static Response getSuccessResponse(Object value) {
    Map<String, Object> tmp = Maps.newHashMap();
    tmp.put("code", SUCCESS_CODE);
    tmp.put("msg", SUCCESS);
    tmp.put("value", value);
    return Response.ok().entity(tmp).build();
  }


}
