package com.facishare.crm.privilege.controller.arg;

import lombok.Data;
import lombok.ToString;

import java.util.Map;

/**
 * Created by lei on 11/21/16.
 */
@Data
@ToString
public class CreateGroupArg {
    private boolean delFlag;
    private String description;
    private String groupId;
    private String name;
    private Integer status; //默认为启用态，0：启用；1：停用
    private String userIds;
    private Integer type; //不为空（0:公开；1:私有）
    private Map<String, String> languages;
}
